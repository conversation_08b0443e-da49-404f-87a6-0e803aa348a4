import Taro from '@tarojs/taro';

const request = (options ) => {
  const { url } = options;
  return new Promise((resolve, reject) => {
    Taro.request({
      url: `http://localhost:3000/api${url}`,
      method: options.method,
      data: options.data,
      header: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${Taro.getStorageSync('token')}`,
      },
      success(res: any) {
        resolve(res.data);
      },
      fail(error) {
        reject(error);
      },
    });
  });
};

export default request;

  