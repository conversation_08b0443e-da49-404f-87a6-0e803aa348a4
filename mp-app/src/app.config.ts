export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/statistics/index',
    'pages/user/index',
    'pages/babies/add/index',
    'pages/habits/library/index',
    'pages/habits/create/index',
    'pages/habits/detail/index',
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#F9F7F2',
    navigationBarTitleText: '小脚步',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    custom: false,
    color: '#C8C8C8',
    selectedColor: '#7EBBB0',
    backgroundColor: '#F9F7F2',
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '主页',
        iconPath: 'assets/tabbar/home.png',
        selectedIconPath: 'assets/tabbar/home-active.png'
      },
      {
        pagePath: 'pages/statistics/index',
        text: '统计',
        iconPath: 'assets/tabbar/statistics.png',
        selectedIconPath: 'assets/tabbar/tatistics-active.png'
      },
      {
        pagePath: 'pages/user/index',
        text: '我的',
        iconPath: 'assets/tabbar/user.png',
        selectedIconPath: 'assets/tabbar/user-active.png'
      }
    ]
  }
})
