import React from 'react'
import { View, Text } from '@tarojs/components'
import { TimelineProps, TimelineHabitItem, TimelineGroup } from '../../types/timeline'
import './index.scss'

const Timeline: React.FC<TimelineProps> = ({
  habits,
  onHabitClick,
  onToggleComplete
}) => {
  // 将习惯按时间分组
  const groupHabitsByTime = (habits: TimelineHabitItem[]): TimelineGroup[] => {
    const groups: { [key: string]: TimelineHabitItem[] } = {}

    habits.forEach(habit => {
      let time: string
      if (habit.preferred_time === null) {
        time = 'no-time' // 特殊标识符，用于无时间的习惯
      } else {
        time = habit.preferred_time.substring(0, 5) // 提取 HH:mm
      }

      if (!groups[time]) {
        groups[time] = []
      }
      groups[time].push(habit)
    })

    // 按时间排序，将 'no-time' 排在最后
    return Object.entries(groups)
      .map(([time, habits]) => ({
        time: time === 'no-time' ? '待定' : time,
        habits
      }))
      .sort((a, b) => {
        if (a.time === '待定') return 1
        if (b.time === '待定') return -1
        return a.time.localeCompare(b.time)
      })
  }

  const timelineGroups = groupHabitsByTime(habits)

  const handleHabitClick = (habit: TimelineHabitItem) => {
    onHabitClick?.(habit)
  }

  const handleToggleComplete = (habitId: string, currentStatus: boolean) => {
    onToggleComplete?.(habitId, !currentStatus)
  }

  // 获取图标的emoji表示
  const getIconEmoji = (iconName: string): string => {
    const iconMap: { [key: string]: string } = {
      'apple': '🍎',
      'rabbit': '🐰',
      'ball': '⚽',
      'sleep': '🌙',
      'feeding': '🍼',
      'play': '🧸',
      'learning': '📚',
      'exercise': '💪',
      'water': '💧',
      'book': '📖',
      'music': '🎵',
      'meditation': '🧘',
      'walk': '🚶',
      'run': '🏃',
      'bike': '🚴',
      'swim': '🏊',
      'yoga': '🧘‍♀️',
      'cook': '👨‍🍳',
      'clean': '🧹',
      'study': '📝'
    }
    return iconMap[iconName] || iconName
  }

  return (
    <View className="timeline">
      {timelineGroups.map((group) => (
        <View key={group.time} className="timeline-group">
          <View className="timeline-time">
            <Text className="time-text">{group.time}</Text>
          </View>

          <View className="timeline-content">
            {group.habits.map((habit) => (
              <View
                key={habit.habit_id}
                className={`habit-item ${habit.is_completed ? 'completed' : ''}`}
                style={{
                  backgroundColor: habit.theme_color + '20', // 添加透明度
                  borderLeft: `4px solid ${habit.theme_color}`
                }}
                onClick={() => handleHabitClick(habit)}
              >
                <View className="habit-left-bar" style={{ backgroundColor: habit.theme_color }} />

                <View className="habit-icon" style={{ backgroundColor: habit.theme_color + '40' }}>
                  <Text className="icon-text">{getIconEmoji(habit.habit_icon)}</Text>
                </View>

                <View className="habit-info">
                  <Text className="habit-name">{habit.habit_name}</Text>
                  {habit.preferred_time && (
                    <Text className="habit-time">{habit.preferred_time.substring(0, 5)}</Text>
                  )}
                </View>

                <View className="habit-actions">
                  <View className="action-button edit-button">
                    <Text className="action-icon">✏️</Text>
                  </View>
                  <View className="action-button menu-button">
                    <Text className="action-icon">⋯</Text>
                  </View>
                </View>

                <View
                  className={`completion-button ${habit.is_completed ? 'completed' : 'incomplete'}`}
                  style={{ backgroundColor: habit.is_completed ? habit.theme_color : 'transparent' }}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleToggleComplete(habit.habit_id, habit.is_completed)
                  }}
                >
                  <Text className="completion-icon">
                    {habit.is_completed ? '✓' : ''}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      ))}
    </View>
  )
}

export default Timeline