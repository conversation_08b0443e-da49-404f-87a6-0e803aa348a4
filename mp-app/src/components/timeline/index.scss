.timeline {
  padding: 20px 0;

  .timeline-group {
    display: flex;
    margin-bottom: 32px;
    position: relative;

    // 添加竖线连接
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 29px;
      top: 30px;
      bottom: -32px;
      width: 2px;
      background-color: #e0e0e0;
      z-index: 1;
    }

    .timeline-time {
      width: 60px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      padding-top: 8px;
      position: relative;
      z-index: 2;

      // 添加时间节点圆点
      &::before {
        content: '';
        position: absolute;
        left: 23px;
        top: 12px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #8B4513;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e0e0e0;
        z-index: 3;
      }

      .time-text {
        font-size: 14px;
        font-weight: 600;
        color: #8B4513;
        line-height: 1.2;
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 4px;
      }
    }

    // 为"待定"时间添加特殊样式
    // &:has(.time-text:contains("待定")) {
    //   .timeline-time::before {
    //     background-color: #ccc;
    //     box-shadow: 0 0 0 2px #e0e0e0;
    //   }
    // }

    // 最后一个时间组的特殊处理
    &:last-child {
      .timeline-time::before {
        background-color: #ddd;
      }
    }

    .timeline-content {
      flex: 1;
      margin-left: 20px;

      .habit-item {
        display: flex;
        align-items: center;
        border-radius: 20px;
        padding: 16px;
        margin-bottom: 12px;
        position: relative;
        transition: transform 0.2s ease;
        overflow: hidden;

        &:active {
          transform: scale(0.98);
        }

        &.completed {
          .habit-name {
            text-decoration: line-through;
            opacity: 0.7;
          }
        }

        .habit-left-bar {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 6px;
          border-radius: 0 3px 3px 0;
        }

        .habit-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          margin-left: 8px;

          .icon-text {
            font-size: 24px;
          }
        }

        .habit-info {
          flex: 1;

          .habit-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
          }

          .habit-time {
            font-size: 14px;
            color: #666;
            font-weight: 400;
          }
        }

        .habit-actions {
          display: flex;
          align-items: center;
          margin-right: 12px;

          .action-button {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            background-color: rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;

            &:active {
              transform: scale(0.9);
              background-color: rgba(255, 255, 255, 1);
            }

            .action-icon {
              font-size: 16px;
              color: #666;
            }
          }

          .edit-button .action-icon {
            color: #8B4513;
          }

          .menu-button .action-icon {
            color: #666;
            font-weight: bold;
          }
        }

        .completion-button {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          border: 3px solid white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &.completed {
            opacity: 1;
          }

          &.incomplete {
            opacity: 0.8;
            background-color: transparent !important;
            border: 3px solid #ddd;
          }

          .completion-icon {
            color: white;
            font-size: 18px;
            font-weight: bold;
          }

          &:active {
            transform: scale(0.9);
          }
        }
      }
    }
  }
}