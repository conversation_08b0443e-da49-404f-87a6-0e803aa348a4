# Timeline 时间轴组件

一个美观的时间轴组件，用于展示按时间排序的习惯列表。

## 功能特性

- 📅 按时间自动分组和排序
- 🎨 支持自定义主题颜色
- ✅ 习惯完成状态切换
- 📱 响应式设计，适配移动端
- 🎯 无时间的习惯自动排在最后
- 🎭 丰富的图标支持

## 使用方法

```tsx
import Timeline from '../../components/timeline'
import { TimelineHabitItem } from '../../types/timeline'

const habits: TimelineHabitItem[] = [
  {
    "habit_id": "bff4349d-459d-4b0c-be69-a43c2fff1f4c",
    "habit_name": "测试",
    "habit_icon": "apple",
    "theme_color": "#7FB3D3",
    "is_completed": false,
    "preferred_time": "06:00:00"
  },
  // 更多习惯...
]

<Timeline
  habits={habits}
  onHabitClick={(habit) => console.log('点击习惯:', habit)}
  onToggleComplete={(habitId, isCompleted) => {
    // 处理完成状态切换
  }}
/>
```

## 数据格式

### TimelineHabitItem 接口

```typescript
interface TimelineHabitItem {
  habit_id: string           // 习惯唯一标识
  habit_name: string         // 习惯名称
  habit_icon: string         // 习惯图标名称
  theme_color: string        // 主题颜色 (十六进制)
  is_completed: boolean      // 是否已完成
  preferred_time: string | null  // 偏好时间 (HH:mm:ss 格式)
}
```

## 支持的图标

组件内置了丰富的emoji图标映射：

- `apple` → 🍎
- `rabbit` → 🐰
- `ball` → ⚽
- `sleep` → 🌙
- `feeding` → 🍼
- `play` → 🧸
- `learning` → 📚
- `exercise` → 💪
- `water` → 💧
- `book` → 📖
- `music` → 🎵
- `meditation` → 🧘
- 等等...

## 样式特性

- 渐变背景色，根据主题色自动调整
- 左侧彩色边框指示器
- 圆形完成状态按钮
- 编辑和菜单操作按钮
- 浮动添加按钮
- 完成状态的视觉反馈（删除线）

## 时间排序规则

1. 有 `preferred_time` 的习惯按时间升序排列
2. 没有 `preferred_time` (null) 的习惯排在最后，显示为"待定"
3. 相同时间的习惯会分组显示

## 交互功能

- 点击习惯卡片：触发 `onHabitClick` 回调
- 点击完成按钮：切换完成状态，触发 `onToggleComplete` 回调
- 点击编辑按钮：可扩展编辑功能
- 点击菜单按钮：可扩展更多操作
- 点击浮动添加按钮：可扩展添加新习惯功能
