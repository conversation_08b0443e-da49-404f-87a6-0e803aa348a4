import React, { useMemo } from 'react';
import { View, Text } from '@tarojs/components';
import dayjs from 'dayjs';
import './index.scss';

interface CheckinData {
  date: string;
  is_completed: boolean;
}

interface DayData {
  day: number;
  dateStr: string;
  isCompleted: boolean;
}

interface CheckinCalendarProps {
  data: CheckinData[];
  title?: string;
  onDayClick?: (date: string, isCompleted: boolean) => void;
  showStats?: boolean;
}

const CheckinCalendar: React.FC<CheckinCalendarProps> = ({
  data,
  title = 'Check-in History',
  onDayClick,
  showStats = true
}) => {
  // 使用 useMemo 优化性能，避免不必要的重新计算
  const { currentMonth, weeks, checkinStats } = useMemo(() => {
    // 获取当前月份和年份
    const currentDate = dayjs();
    const currentMonth = currentDate.format('MMMM YYYY');

    // 获取月份的第一天和最后一天
    const firstDayOfMonth = currentDate.startOf('month');
    const lastDayOfMonth = currentDate.endOf('month');

    // 获取第一天是星期几 (0=Sunday, 1=Monday, ...)
    const firstDayWeekday = firstDayOfMonth.day();

    // 获取这个月有多少天
    const daysInMonth = lastDayOfMonth.date();

    // 创建日历数据映射
    const checkinMap = new Map<string, boolean>();
    let completedCount = 0;
    let totalCount = 0;

    data.forEach(item => {
      const date = dayjs(item.date).format('YYYY-MM-DD');
      checkinMap.set(date, item.is_completed);
      if (item.is_completed) completedCount++;
      totalCount++;
    });

    // 生成日历网格
    const generateCalendarGrid = (): (DayData | null)[] => {
      const grid: (DayData | null)[] = [];

      // 添加空白格子（月份开始前的空白）
      for (let i = 0; i < firstDayWeekday; i++) {
        grid.push(null);
      }

      // 添加月份的每一天
      for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = currentDate.date(day).format('YYYY-MM-DD');
        const isCompleted = checkinMap.get(dateStr) || false;
        grid.push({
          day,
          dateStr,
          isCompleted
        });
      }

      return grid;
    };

    const calendarGrid = generateCalendarGrid();

    // 将网格分组为周
    const weeks: (DayData | null)[][] = [];
    for (let i = 0; i < calendarGrid.length; i += 7) {
      weeks.push(calendarGrid.slice(i, i + 7));
    }

    return {
      currentMonth,
      weeks,
      checkinStats: {
        completed: completedCount,
        total: totalCount,
        percentage: totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0
      }
    };
  }, [data]);

  const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  return (
    <View className="checkin-calendar">
      <View className="calendar-header">
        <View className="header-left">
          <Text className="calendar-title">{title}</Text>
          {showStats && (
            <Text className="calendar-stats">
              {checkinStats.completed}/{checkinStats.total} days ({checkinStats.percentage}%)
            </Text>
          )}
        </View>
        <Text className="calendar-month">{currentMonth}</Text>
      </View>

      <View className="calendar-weekdays">
        {weekDays.map((day, index) => (
          <View key={index} className="weekday">
            <Text className="weekday-text">{day}</Text>
          </View>
        ))}
      </View>

      <View className="calendar-grid">
        {weeks.map((week, weekIndex) => (
          <View key={weekIndex} className="calendar-week">
            {week.map((dayData, dayIndex) => (
              <View key={dayIndex} className="calendar-day">
                {dayData ? (
                  <View
                    className={`day-cell ${dayData.isCompleted ? 'completed' : 'incomplete'} ${onDayClick ? 'clickable' : ''}`}
                    onClick={() => onDayClick?.(dayData.dateStr, dayData.isCompleted)}
                  >
                    {dayData.isCompleted ? (
                      <View className="footprint-icon">
                        <Text className="footprint">👣</Text>
                      </View>
                    ) : (
                      <Text className="day-number">{dayData.day}</Text>
                    )}
                  </View>
                ) : (
                  <View className="day-cell empty" />
                )}
              </View>
            ))}
          </View>
        ))}
      </View>
    </View>
  );
};

export default CheckinCalendar;