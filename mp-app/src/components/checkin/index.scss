
$theme-color: #7EBBB0;  // 青松绿

.checkin-calendar {
  
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;

    .header-left {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .calendar-title {
        font-size: 32px;
        font-weight: 700;
        color: #2c2c2c;
        letter-spacing: -0.5px;
        margin: 0;
      }

      .calendar-stats {
        font-size: 16px;
        font-weight: 500;
        color: #7dd3c0;
        background: rgba(125, 211, 192, 0.1);
        padding: 6px 12px;
        border-radius: 12px;
        border: 1px solid rgba(125, 211, 192, 0.2);
      }
    }

    .calendar-month {
      font-size: 24px;
      font-weight: 500;
      color: #666;
      opacity: 0.8;
      margin-top: 4px;
    }
  }
  
  .calendar-weekdays {
    display: flex;
    margin-bottom: 16px;
    
    .weekday {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 48px;
      
      .weekday-text {
        font-size: 20px;
        font-weight: 600;
        color: #666;
        opacity: 0.7;
      }
    }
  }
  
  .calendar-grid {
    .calendar-week {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      margin-bottom: 8px;
      
      .calendar-day {
        display: flex;
        justify-content: center;
        align-items: center;

        $size: 40px;
        
        .day-cell {
          width: $size;
          height: $size;
          border-radius: 8px;
          display: flex;
          justify-content: center;
          align-items: center;
          transition: all 0.3s ease;
          
          &.empty {
            background: transparent !important;
          }

          &.clickable {
            cursor: pointer;

            &:active {
              transform: translateY(0);
            }
          }

          &.incomplete {
            background: #E9E8EE;

            .day-number {
              font-size: 18px;
              color: #555962;
            }
          }
          
          &.completed {
            background: $theme-color;
            color: #fff;
            
            .footprint-icon {
              display: flex;
              justify-content: center;
              align-items: center;
              
              .footprint {
                font-size: 24px;
                filter: brightness(0) invert(1);
              }
            }
          }
        }
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .checkin-calendar {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    
    .calendar-header {
      .calendar-title {
        color: #ffffff;
      }
      
      .calendar-month {
        color: #cccccc;
      }
    }
    
    .calendar-weekdays {
      .weekday {
        .weekday-text {
          color: #cccccc;
        }
      }
    }
    
    .calendar-grid {
      .calendar-week {
        .calendar-day {
          .day-cell {
            &.incomplete {
              background: rgba(255, 255, 255, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.2);
              
              .day-number {
                color: #cccccc;
              }
            }
          }
        }
      }
    }
  }
}
