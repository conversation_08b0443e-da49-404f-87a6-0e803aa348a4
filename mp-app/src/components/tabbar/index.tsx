import React from 'react';
import { View, Image, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

// 图标资源
import homeIcon from '@/assets/tabbar/home.png';
import homeActiveIcon from '@/assets/tabbar/home-active.png';
import statisticsIcon from '@/assets/tabbar/statistics.png';
import statisticsActiveIcon from '@/assets/tabbar/tatistics-active.png';
import userIcon from '@/assets/tabbar/user.png';
import userActiveIcon from '@/assets/tabbar/user-active.png';

interface TabBarItem {
  key: string;
  title: string;
  icon: string;
  activeIcon: string;
  path: string;
}

interface TabBarProps {
  current?: string;
  onChange?: (key: string) => void;
}

const TabBar: React.FC<TabBarProps> = ({ current = 'home', onChange }) => {
  const tabItems: TabBarItem[] = [
    {
      key: 'home',
      title: '主页',
      icon: homeIcon,
      activeIcon: homeActiveIcon,
      path: '/pages/index/index'
    },
    {
      key: 'statistics',
      title: '统计',
      icon: statisticsIcon,
      activeIcon: statisticsActiveIcon,
      path: '/pages/statistics/index'
    },
    {
      key: 'user',
      title: '我的',
      icon: userIcon,
      activeIcon: userActiveIcon,
      path: '/pages/user/index'
    }
  ];

  const handleTabClick = (item: TabBarItem) => {
    if (item.key === current) return;

    onChange?.(item.key);

    // 导航到对应页面
    Taro.switchTab({
      url: item.path
    });
  };

  return (
    <View className="tabbar">
      {tabItems.map((item) => {
        const isActive = current === item.key;
        return (
          <View
            key={item.key}
            className={`tabbar-item ${isActive ? 'tabbar-item--active' : ''}`}
            onClick={() => handleTabClick(item)}
          >
            <View className="tabbar-item__icon">
              <Image
                src={isActive ? item.activeIcon : item.icon}
                className="tabbar-item__image"
                mode="aspectFit"
              />
            </View>
            <Text className={`tabbar-item__text ${isActive ? 'tabbar-item__text--active' : ''}`}>
              {item.title}
            </Text>
          </View>
        );
      })}
    </View>
  );
};

export default TabBar;