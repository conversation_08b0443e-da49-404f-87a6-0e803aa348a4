.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);

  &-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 8px 0;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    &__icon {
      width: 48px;
      height: 48px;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__image {
      width: 32px;
      height: 32px;
    }

    &__text {
      font-size: 20px;
      color: #999999;
      line-height: 1.2;
      transition: color 0.2s ease;

      &--active {
        color: #7EBBB0;
        font-weight: 500;
      }
    }

    &--active {
      .tabbar-item__icon {
        transform: scale(1.1);
      }
    }
  }
}

// 适配不同屏幕尺寸
@media screen and (max-width: 375px) {
  .tabbar {
    height: 90px;

    &-item {
      &__icon {
        width: 40px;
        height: 40px;
      }

      &__image {
        width: 28px;
        height: 28px;
      }

      &__text {
        font-size: 18px;
      }
    }
  }
}

@media screen and (min-width: 414px) {
  .tabbar {
    height: 110px;

    &-item {
      &__icon {
        width: 52px;
        height: 52px;
      }

      &__image {
        width: 36px;
        height: 36px;
      }

      &__text {
        font-size: 22px;
      }
    }
  }
}
