# TabBar 组件

小程序底部导航栏组件，支持图标切换和页面导航。

## 功能特性

- 🎨 支持自定义图标和激活状态图标
- 🔄 自动处理页面切换和导航
- 📱 响应式设计，适配不同屏幕尺寸
- 🎯 支持当前页面高亮显示
- ⚡ 基于 Taro 框架，支持多端运行

## 使用方法

### 基础用法

```tsx
import { TabBar } from '@/components';

function MyPage() {
  return (
    <View className="page">
      <View className="content">
        {/* 页面内容 */}
      </View>
      <TabBar current="home" />
    </View>
  );
}
```

### 自定义切换事件

```tsx
import { TabBar } from '@/components';

function MyPage() {
  const handleTabChange = (key: string) => {
    console.log('切换到:', key);
    // 自定义逻辑
  };

  return (
    <View className="page">
      <View className="content">
        {/* 页面内容 */}
      </View>
      <TabBar current="statistics" onChange={handleTabChange} />
    </View>
  );
}
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| current | string | 'home' | 当前激活的标签页 |
| onChange | (key: string) => void | - | 标签切换时的回调函数 |

### 标签页配置

当前支持的标签页：

- `home` - 主页 (`/pages/index/index`)
- `statistics` - 统计 (`/pages/statistics/index`)
- `user` - 我的 (`/pages/user/index`)

## 图标资源

组件使用的图标位于 `src/assets/tabbar/` 目录下：

- `home.png` / `home-active.png` - 主页图标
- `statistics.png` / `tatistics-active.png` - 统计图标
- `user.png` / `user-active.png` - 我的图标

## 样式说明

- TabBar 固定在页面底部
- 高度为 100px（小屏幕 90px，大屏幕 110px）
- 支持安全区域适配
- 包含点击动画效果

## 注意事项

1. 使用 TabBar 的页面需要设置 `padding-bottom` 为 TabBar 高度，避免内容被遮挡
2. 组件会自动调用 `Taro.switchTab` 进行页面切换
3. 确保在 `app.config.ts` 中正确配置了 tabBar 相关页面
