import request from '../utils/request';
import { BabyData } from "../pages/babies/types/baby.request";



export const getAllBabies = () => {
  return request({
    url: "/babies/getAll",
    method: "GET",
  });
};

export const createBaby = (data: BabyData) => {
  return request({
    url: "/babies/create",
    method: "POST",
    data,
  });
};

export const getBabyById = (id: string) => {
  return request({
    url: `/babies/${id}`,
    method: 'GET',
  });
};

export const updateBaby = (id: string, data: Partial<BabyData>) => {
  return request({
    url: `/babies/${id}`,
    method: 'PUT',
    data,
  });
};

export const deleteBaby = (id: string) => {
  return request({
    url: `/babies/${id}`,
    method: 'DELETE',
  });
};
