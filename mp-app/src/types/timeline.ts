/**
 * 时间轴习惯项目接口定义
 */
export interface TimelineHabitItem {
  /** 习惯图标 */
  habit_icon: string
  /** 习惯唯一标识 */
  habit_id: string
  /** 习惯名称 */
  habit_name: string
  /** 是否已完成 */
  is_completed: boolean
  /** 偏好时间 (HH:mm:ss 格式)，null 表示无特定时间 */
  preferred_time: string | null
  /** 主题颜色 (十六进制颜色值) */
  theme_color: string
}

/**
 * 时间轴组件属性接口
 */
export interface TimelineProps {
  /** 时间轴习惯列表 */
  habits: TimelineHabitItem[]
  /** 点击习惯项回调 */
  onHabitClick?: (habit: TimelineHabitItem) => void
  /** 切换完成状态回调 */
  onToggleComplete?: (habitId: string, isCompleted: boolean) => void
}

/**
 * 时间轴分组数据接口
 */
export interface TimelineGroup {
  /** 时间标签 (HH:mm 格式或"待定") */
  time: string
  /** 该时间段的习惯列表 */
  habits: TimelineHabitItem[]
}