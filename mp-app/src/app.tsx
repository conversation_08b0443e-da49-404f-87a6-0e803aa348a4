import React, { useEffect } from 'react'
import Taro, { useDidShow, useDidHide, useLaunch } from '@tarojs/taro'
import { login } from './services/auth';
import { ConfigProvider } from '@nutui/nutui-react-taro'

import './app.scss'
import 'windi.css';

const darkTheme = {
  nutuiColorPrimary: 'green',
  nutuiColorPrimaryStop1: 'green',
  nutuiColorPrimaryStop2: 'green',
}

function App(props) {
  useLaunch(() => {
    console.log('App launched.')
    Taro.login({
      success: res => {
        login({
          code: res.code,
        }).then(res => {
          console.log('登录成功', res);
          Taro.setStorageSync('token', res.data.access_token);
        })
      },
      fail: err => {
        console.log('login fail', err)
      }
    })
  })

  // 可以使用所有的 React Hooks
  useEffect(() => {
    console.log('useEffect')
    
  })

  // 对应 onShow
  useDidShow(() => {
    console.log('onShow')

  })

  // 对应 onHide
  useDidHide(() => {})

  return <ConfigProvider theme={darkTheme}>{props.children}</ConfigProvider>
}

export default App
