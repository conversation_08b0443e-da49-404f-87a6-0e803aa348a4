import React from 'react';
import { View, Text } from '@tarojs/components';
// import { TabBar } from '@/components';
import './index.scss';

const User: React.FC = () => {
  return (
    <View className="user-page">
      <View className="user-content">
        <Text className="user-title">我的</Text>
        <Text className="user-desc">个人中心页面</Text>
      </View>
      
      {/* <TabBar current="user" /> */}
    </View>
  );
};

export default User;
