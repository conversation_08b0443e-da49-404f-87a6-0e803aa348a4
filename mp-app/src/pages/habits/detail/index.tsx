import { Button, <PERSON> } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import dayjs from "dayjs";
import { useEffect, useState } from "react"
import { CheckinCalendar } from "src/components";
import { checkinHabit, getHabitDetail, getHabitsStatistics } from "src/services/habits"
import { Image } from '@nutui/nutui-react-taro';
import './index.scss'
import HabitIcon from '../../../assets/habits/sleep.png';

const HabitDetail = () => {
  const router = useRouter();
  const { id } = router.params;

  const [habit, setHabit] = useState<HabitItem>()
  const [habitStatistics, setHabitStatistics] = useState<HabitStatistics>()

  useEffect(() => {
    getHabitDetail(id).then((res) => {
      console.log(res.data)
      setHabit(res.data)
    })
    getHabitsStatistics(id).then((res) => {
      console.log(res.data)
      setHabitStatistics(res.data)
    })
  }, [])


  const handleDayClick = (check_in_date: string) => {
    console.log('打卡日期', check_in_date)
    // checkinHabit({
    //   habit_id: id,
    //   check_in_date: dayjs().format('YYYY-MM-DD'),
    //   notes: ''
    // })
  };

  return <View className='habit-detail-page'>
    <View className="card">
      <View className="habit-info">
        <Image src={HabitIcon} className="habit-icon" />
        <View className="habit-info-content">
          <View className="habit-name">{habit?.name}</View>
          <View className="habit-description">{habit?.description}</View>
        </View>
      </View>

    </View>
    <View className="card">
      <CheckinCalendar
        data={habitStatistics?.current_month_data ?? []}
        title=""
        onDayClick={handleDayClick}
        showStats={false}
      />
    </View>

  
   

    <Button onClick={handleDayClick}>打卡</Button>
  </View>
}

export default HabitDetail