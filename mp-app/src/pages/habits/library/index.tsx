import { useEffect, useState } from 'react'
import { View, ScrollView, Text } from '@tarojs/components'
import { Tabs, Cell, Button, Loading, Empty } from '@nutui/nutui-react-taro'
import './index.scss'
import { getHabitsLibraryTree } from 'src/services/habits'
import Taro from '@tarojs/taro'
import type {
  HabitCategoryTreeDto,
  HabitSubCategoryTreeDto,
  HabitTemplateInfoDto
} from '../types'

// 视图状态枚举
enum ViewState {
  CATEGORIES = 'categories', // 显示分类列表
  TEMPLATES = 'templates'    // 显示模板列表
}

function HabitLibrary() {
  // 状态管理
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [libraryData, setLibraryData] = useState<HabitCategoryTreeDto[]>([])
  const [activeTabKey, setActiveTabKey] = useState<string>('')
  const [viewState, setViewState] = useState<ViewState>(ViewState.CATEGORIES)
  const [selectedSubCategory, setSelectedSubCategory] = useState<HabitSubCategoryTreeDto | null>(null)

  // 获取当前激活的一级分类
  const activeCategory = libraryData.find(cat => cat.id === activeTabKey)

  // 处理添加习惯模板
  const handleAddHabit = (template: HabitTemplateInfoDto) => {
    console.log('添加习惯模板: ', template)
    Taro.navigateTo({
      url: `/pages/habits/create/index?templateId=${template.id}`,
    })
  }

  // 处理子分类点击
  const handleSubCategoryClick = (subCategory: HabitSubCategoryTreeDto) => {
    setSelectedSubCategory(subCategory)
    setViewState(ViewState.TEMPLATES)
  }

  // 处理返回分类列表
  const handleBackToCategories = () => {
    setViewState(ViewState.CATEGORIES)
    setSelectedSubCategory(null)
  }

  // 处理Tab切换
  const handleTabChange = (tabKey: string) => {
    setActiveTabKey(tabKey)
    setViewState(ViewState.CATEGORIES)
    setSelectedSubCategory(null)
  }

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        const response = await getHabitsLibraryTree() as any
        console.log('习惯库数据:', response)

        // 处理全局拦截器包装的响应格式 { success: true, data: [...] }
        const data = response?.data || response

        if (Array.isArray(data)) {
          setLibraryData(data)
          if (data.length > 0) {
            setActiveTabKey(data[0].id)
          }
        } else {
          setError('数据格式错误')
        }
      } catch (err) {
        console.error('获取习惯库数据失败:', err)
        setError('获取数据失败，请重试')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // 渲染加载状态
  const renderLoading = () => (
    <View className='loading-container'>
      <Loading type="spinner" />
      <Text className='loading-text'>加载中...</Text>
    </View>
  )

  // 渲染错误状态
  const renderError = () => (
    <View className='error-container'>
      <Empty description={error || '加载失败'} />
      <Button
        type='primary'
        size='small'
        onClick={() => window.location.reload()}
        className='retry-btn'
      >
        重试
      </Button>
    </View>
  )

  // 渲染子分类列表
  const renderSubCategories = () => {
    if (!activeCategory || !activeCategory.children.length) {
      return (
        <View className='empty-container'>
          <Empty description='暂无分类' />
        </View>
      )
    }

    return (
      <ScrollView className='sub-category-list' scrollY>
        <Cell.Group>
          {activeCategory.children.map((subCategory) => (
            <Cell
              key={subCategory.id}
              title={
                <View className='sub-category-title'>
                  <Text className='sub-category-icon'>{subCategory.icon}</Text>
                  <Text className='sub-category-name'>{subCategory.name}</Text>
                  <Text className='template-count'>({subCategory.templates.length}个习惯)</Text>
                </View>
              }

              onClick={() => handleSubCategoryClick(subCategory)}
            />
          ))}
        </Cell.Group>
      </ScrollView>
    )
  }

  // 渲染模板列表
  const renderTemplates = () => {
    const templates = selectedSubCategory?.templates || []

    if (templates.length === 0) {
      return (
        <View className='empty-container'>
          <Empty description='暂无习惯模板' />
        </View>
      )
    }

    return (
      <ScrollView className='template-list' scrollY>
        <Cell.Group>
          {templates.map((template) => (
            <Cell
              key={template.id}
              title={
                <View className='template-title'>
                  <Text className='template-icon'>{template.icon}</Text>
                  <Text className='template-name'>{template.name}</Text>
                </View>
              }
              description={template.description}
              extra={
                <Button
                  size='small'
                  type='primary'
                  onClick={() => handleAddHabit(template)}
                >
                  添加
                </Button>
              }
            />
          ))}
        </Cell.Group>
      </ScrollView>
    )
  }

  // 渲染直接模板列表（当一级分类下只有一个子分类或虚拟子分类时）
  const renderDirectTemplates = () => {
    if (!activeCategory || !activeCategory.children.length) {
      return (
        <View className='empty-container'>
          <Empty description='暂无习惯模板' />
        </View>
      )
    }

    // 获取第一个子分类的模板
    const templates = activeCategory.children[0]?.templates || []

    if (templates.length === 0) {
      return (
        <View className='empty-container'>
          <Empty description='暂无习惯模板' />
        </View>
      )
    }

    return (
      <ScrollView className='template-list' scrollY>
        <Cell.Group>
          {templates.map((template) => (
            <Cell
              key={template.id}
              title={
                <View className='template-title'>
                  <Text className='template-icon'>{template.icon}</Text>
                  <Text className='template-name'>{template.name}</Text>
                </View>
              }
              description={template.description}
              extra={
                <Button
                  size='small'
                  type='primary'
                  onClick={() => handleAddHabit(template)}
                >
                  添加
                </Button>
              }
            />
          ))}
        </Cell.Group>
      </ScrollView>
    )
  }

  // 渲染Tab内容
  const renderTabContent = () => {
    if (!activeCategory) return null

    // 判断是否需要显示子分类列表
    const shouldShowSubCategories = activeCategory.children.length > 1 ||
      (activeCategory.children.length === 1 && activeCategory.children[0].level === 2 && activeCategory.children[0].id !== activeCategory.id)

    if (viewState === ViewState.TEMPLATES) {
      return (
        <View className='tab-content'>
          <View className='content-header'>
            <Button
              size='small'
              fill='none'
              onClick={handleBackToCategories}
              className='back-btn'
            >
              ← 返回
            </Button>
            <Text className='content-title'>{selectedSubCategory?.name}</Text>
          </View>
          {renderTemplates()}
        </View>
      )
    }

    if (shouldShowSubCategories) {
      return renderSubCategories()
    } else {
      return renderDirectTemplates()
    }
  }

  // 主渲染
  if (loading) {
    return renderLoading()
  }

  if (error) {
    return renderError()
  }

  if (libraryData.length === 0) {
    return (
      <View className='habit-library-page'>
        <Empty description='暂无习惯库数据' />
      </View>
    )
  }

  return (
    <View className='habit-library-page'>
      {/* Tab 标签 */}
      <Tabs
        activeType="button"
        value={activeTabKey}
        onChange={(val) => handleTabChange(String(val))}
        className='habit-tabs'

      >
        {libraryData.map((category) => (
          <Tabs.TabPane
            key={category.id}
            value={category.id}
            title={`${category.icon} ${category.name}`}
          />
        ))}
      </Tabs>

      {/* Tab 内容 */}
      <View className='tab-content-container'>
        {renderTabContent()}
      </View>
    </View>
  )
}

export default HabitLibrary

