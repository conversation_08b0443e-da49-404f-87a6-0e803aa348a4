# 习惯库页面使用示例

## API 数据示例

以下是 `/api/habits/library/tree` 接口返回的数据示例：

```json
{
  "success": true,
  "data": [
    {
      "id": "category-1",
      "name": "生活自理",
      "icon": "🏠",
      "level": 1,
      "sort_order": 1,
      "children": [
        {
          "id": "subcategory-1",
          "name": "基础自理",
          "icon": "🧼",
          "level": 2,
          "sort_order": 1,
          "templates": [
            {
              "id": "template-1",
              "name": "睡前刷牙",
              "icon": "🦷",
              "theme_color": "#4CAF50",
              "description": "睡前刷干净牙齿，让小牙齿一夜都在休息哦"
            },
            {
              "id": "template-2",
              "name": "饭前洗手",
              "icon": "🧴",
              "theme_color": "#2196F3",
              "description": "小手洗干净，病菌不来烦"
            }
          ]
        },
        {
          "id": "subcategory-2",
          "name": "生活技能",
          "icon": "🍽️",
          "level": 2,
          "sort_order": 2,
          "templates": [
            {
              "id": "template-3",
              "name": "独立吃饭",
              "icon": "🥄",
              "theme_color": "#FF9800",
              "description": "自己动手，丰衣足食"
            }
          ]
        }
      ]
    },
    {
      "id": "category-2",
      "name": "健康习惯",
      "icon": "💪",
      "level": 1,
      "sort_order": 2,
      "children": [
        {
          "id": "category-2",
          "name": "健康习惯",
          "icon": "💪",
          "level": 2,
          "sort_order": 1,
          "templates": [
            {
              "id": "template-4",
              "name": "早睡早起",
              "icon": "🌙",
              "theme_color": "#9C27B0",
              "description": "规律作息，健康成长"
            }
          ]
        }
      ]
    }
  ]
}
```

## 页面行为示例

### 情况一：多个子分类
当用户点击"生活自理" Tab 时：
1. 显示子分类列表：
   - 基础自理 (2个习惯)
   - 生活技能 (1个习惯)
2. 用户点击"基础自理"
3. 显示该子分类下的模板列表：
   - 睡前刷牙
   - 饭前洗手
4. 用户可以点击"添加"按钮添加习惯

### 情况二：单个子分类（虚拟子分类）
当用户点击"健康习惯" Tab 时：
1. 直接显示模板列表（因为只有一个虚拟子分类）：
   - 早睡早起
2. 用户可以直接点击"添加"按钮添加习惯

## 导航流程

```
习惯库首页
├── Tab: 生活自理
│   ├── 子分类列表
│   │   ├── 基础自理 → 模板列表 → 添加习惯
│   │   └── 生活技能 → 模板列表 → 添加习惯
├── Tab: 健康习惯
│   └── 直接显示模板列表 → 添加习惯
```

## 错误处理示例

### 网络错误
```
[错误图标]
获取数据失败，请重试
[重试按钮]
```

### 空数据
```
[空状态图标]
暂无习惯库数据
```

### 加载状态
```
[加载动画]
加载中...
```
