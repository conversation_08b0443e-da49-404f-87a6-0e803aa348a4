import { render, screen } from '@testing-library/react'
import HabitLibrary from '../index'

// Mock Taro
jest.mock('@tarojs/taro', () => ({
  navigateTo: jest.fn(),
}))

// Mock services
jest.mock('../../../../services/habits', () => ({
  getHabitsLibraryTree: jest.fn(),
}))

describe('HabitLibrary', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render loading state initially', () => {
    render(<HabitLibrary />)
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  // 更多测试用例可以在这里添加
})
