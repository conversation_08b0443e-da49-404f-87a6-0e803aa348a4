# 习惯库接口修改说明

## 修改内容

### 后端接口修改

**接口路径**: `/api/habits/library/tree`

**修改前返回格式**:
```json
{
  "data": [
    // 习惯分类数据...
  ]
}
```

**修改后返回格式**:
```json
{
  "success": true,
  "data": [
    // 习惯分类数据...
  ]
}
```

### 原因说明

1. **全局拦截器影响**: 后端使用了全局的 `TransformInterceptor`，会自动将所有响应包装为 `{ success: true, data: ... }` 格式
2. **一致性考虑**: 保持与其他接口的响应格式一致
3. **错误处理**: 统一的响应格式便于前端进行错误处理

### 前端适配修改

**文件**: `mp-app/src/pages/habits/library/index.tsx`

**修改内容**:
```typescript
// 修改前
if (Array.isArray(response)) {
  setLibraryData(response)
  // ...
}

// 修改后
const data = response?.data || response
if (Array.isArray(data)) {
  setLibraryData(data)
  // ...
}
```

**类型定义更新**:
```typescript
// 修改前
export type HabitLibraryTreeResponse = HabitCategoryTreeDto[];

// 修改后
export interface HabitLibraryTreeResponse {
  success: boolean;
  data: HabitCategoryTreeDto[];
}
```

## 测试验证

### 接口测试
```bash
curl -X GET "http://localhost:3000/api/habits/library/tree" | jq .
```

**预期响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "11111111-1111-1111-1111-111111111111",
      "name": "生活自理",
      "icon": "🧸",
      "level": 1,
      "sort_order": 1,
      "children": [
        // 子分类数据...
      ]
    }
    // 更多分类...
  ]
}
```

### 前端兼容性

前端代码现在可以处理两种格式：
1. 新格式：`{ success: true, data: [...] }`
2. 旧格式：直接数组 `[...]`（向后兼容）

## 影响范围

### 已修改文件

**后端**:
- `server/src/habits/controllers/client/habit-library.controller.ts`
- `server/src/habits/dto/library/habit-library-response.dto.ts`

**前端**:
- `mp-app/src/pages/habits/library/index.tsx`
- `mp-app/src/pages/habits/types/index.ts`
- `mp-app/src/pages/habits/library/example.md`

### 无需修改

- 其他接口调用（因为全局拦截器统一处理）
- 数据库结构
- 业务逻辑

## 注意事项

1. **向后兼容**: 前端代码保持了向后兼容性，可以处理两种响应格式
2. **全局影响**: 所有接口都会被 `TransformInterceptor` 包装，这是系统级的设计
3. **错误处理**: 新格式便于统一的错误处理和状态管理

## 后续建议

1. **统一响应格式**: 建议所有前端服务调用都按照新格式处理
2. **类型安全**: 为所有接口响应定义准确的 TypeScript 类型
3. **文档更新**: 更新 API 文档以反映实际的响应格式
