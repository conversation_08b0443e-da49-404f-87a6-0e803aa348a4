# 习惯库页面

## 功能概述

习惯库页面用于展示系统中的习惯模板，用户可以浏览不同分类的习惯模板并添加到自己的习惯列表中。

## 页面结构

### 1. Tab 标签设计
- 使用一级习惯分类作为 Tab 标签
- 每个 Tab 显示分类名称和图标
- 支持左右滑动切换

### 2. Tab 内容逻辑

#### 情况一：直接显示模板
当一级分类下没有子分类或只有一个虚拟子分类时，直接展示该分类下的习惯模板列表。

#### 情况二：先显示子分类
当一级分类下有多个子分类时：
1. 先显示子分类列表，每个子分类显示名称、图标和模板数量
2. 点击子分类后展示对应的习惯模板列表
3. 提供返回按钮回到子分类列表

### 3. 数据结构

```typescript
interface HabitLibraryTreeResponse {
  data: HabitCategoryTreeDto[];
}

interface HabitCategoryTreeDto {
  id: string;
  name: string;
  icon: string;
  level: number;
  sort_order: number;
  children: HabitSubCategoryTreeDto[];
}

interface HabitSubCategoryTreeDto {
  id: string;
  name: string;
  icon: string;
  level: number;
  sort_order: number;
  templates: HabitTemplateInfoDto[];
}

interface HabitTemplateInfoDto {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
}
```

## 主要功能

### 1. 数据加载
- 调用 `/api/habits/library/tree` 接口获取树形结构数据
- 支持加载状态和错误处理
- 自动选择第一个分类作为默认激活状态

### 2. Tab 切换
- 点击不同 Tab 切换分类
- 切换时重置视图状态到分类列表

### 3. 子分类导航
- 显示子分类列表（如果有多个子分类）
- 点击子分类进入模板列表
- 提供返回按钮

### 4. 模板操作
- 显示模板列表（图标、名称、描述）
- 点击"添加"按钮跳转到创建习惯页面
- 传递模板ID参数

## 样式特点

### 1. 响应式设计
- 适配移动端屏幕
- 使用 Tailwind CSS 进行样式设计

### 2. 交互体验
- 流畅的 Tab 切换动画
- 清晰的层级导航
- 友好的加载和错误状态

### 3. 视觉设计
- 统一的图标和颜色主题
- 清晰的信息层级
- 合理的间距和布局

## 使用方法

1. 用户进入页面后自动加载习惯库数据
2. 通过 Tab 切换不同的习惯分类
3. 根据分类结构浏览子分类或直接查看模板
4. 点击"添加"按钮将模板添加为个人习惯

## 错误处理

- 网络请求失败：显示错误信息和重试按钮
- 数据为空：显示空状态提示
- 加载中：显示加载动画

## 技术实现

- React + TypeScript
- Taro 框架
- NutUI 组件库
- SCSS 样式
- 状态管理使用 useState
