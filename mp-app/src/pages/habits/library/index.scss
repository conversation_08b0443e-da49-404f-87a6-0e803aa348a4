.habit-library-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.habit-tabs {
  background: #fff;
  border-bottom: 1px solid #eee;

  .tab-title {
    display: flex;
    align-items: center;
    gap: 4px;

    .tab-icon {
      font-size: 16px;
    }

    .tab-name {
      font-size: 14px;
    }
  }
}

.tab-content-container {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .content-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #eee;

    .back-btn {
      margin-right: 12px;
      color: #1890ff;
    }

    .content-title {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.sub-category-list,
.template-list {
  flex: 1;
  background: #fff;
}

.sub-category-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .sub-category-icon {
    font-size: 18px;
  }

  .sub-category-name {
    font-size: 16px;
    font-weight: 500;
    flex: 1;
  }

  .template-count {
    font-size: 12px;
    color: #999;
  }
}

.template-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .template-icon {
    font-size: 18px;
  }

  .template-name {
    font-size: 16px;
    font-weight: 500;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 12px;

  .loading-text {
    color: #999;
    font-size: 14px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;

  .retry-btn {
    margin-top: 12px;
  }
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #fff;
}

.empty {
  padding: 24px;
  text-align: center;
  color: #999;
}
