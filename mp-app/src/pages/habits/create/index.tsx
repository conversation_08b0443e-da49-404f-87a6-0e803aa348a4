import React, { useEffect, useState } from 'react'
import { View, ScrollView } from '@tarojs/components'
import {
  Input,
  Button,
  TextArea,
  Toast,
  DatePicker
} from '@nutui/nutui-react-taro'
import Taro, { useRouter } from '@tarojs/taro'
import './index.scss'
import { createHabit, getHabitTemplate } from 'src/services/habits'
import dayjs from 'dayjs'

interface HabitForm {
  name: string
  icon: string
  theme_color: string
  description: string
  frequency: number[]
  preferred_time: string
  status: 'active' | 'archived'
}

const HABIT_ICONS = [
  { key: 'bear', emoji: '🐻', name: '熊' },
  { key: 'apple', emoji: '🍎', name: '苹果' },
  { key: 'ball', emoji: '🏀', name: '球' },
  { key: 'car', emoji: '🚗', name: '车' },
  { key: 'star', emoji: '⭐', name: '星星' },
  { key: 'music', emoji: '🎵', name: '音符' },
  { key: 'shirt', emoji: '👕', name: '衣服' },
  { key: 'music2', emoji: '🎶', name: '音符' },
  { key: 'rabbit', emoji: '🐰', name: '兔子' },
  { key: 'ship', emoji: '🚢', name: '船' },
]

const THEME_COLORS = [
  { key: '#7FB3D3', color: '#7FB3D3', name: '蓝色' },
  { key: '#D4A574', color: '#D4A574', name: '粉色' },
  { key: '#A8D8D8', color: '#A8D8D8', name: '青色' },
  { key: '#F4E4A6', color: '#F4E4A6', name: '黄色' },
]

const FREQUENCIES = [
  { key: 1, name: '周一' },
  { key: 2, name: '周二' },
  { key: 3, name: '周三' },
  { key: 4, name: '周四' },
  { key: 5, name: '周五' },
  { key: 6, name: '周六' },
  { key: 7, name: '周日' },
]

const STATUSES = [
  { key: 'active', name: '启用' },
  { key: 'archived', name: '归档' },
]

function CreateHabit() {
  const { params } = useRouter();
  const { templateId } = params;

  const [form, setForm] = useState<HabitForm>({
    name: '',
    icon: 'bear',
    theme_color: '#7FB3D3',
    description: '',
    frequency: [],
    preferred_time: '',
    status: 'active'
  })

  const [showTimePicker, setShowTimePicker] = useState(false)

  const [loading, setLoading] = useState(false)

  const handleInputChange = (field: keyof HabitForm, value: string) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFrequencyToggle = (day: number) => {
    setForm(prev => ({
      ...prev,
      frequency: prev.frequency.includes(day)
        ? prev.frequency.filter(d => d !== day)
        : [...prev.frequency, day]
    }))
  }

  const handleTimeChange = (time: string[]) => {
    console.log('time', time)
    setForm(prev => ({
      ...prev,
      preferred_time: time.join(':')
    }))
    setShowTimePicker(false)
  }

  const handleSave = async () => {
    if (!form.name.trim()) {
      Toast.show({ content: '请输入习惯名称' })
      return
    }

    if (form.frequency.length === 0) {
      Toast.show({ content: '请选择打卡频率' })
      return
    }

    setLoading(true)
    try {
      await createHabit(form)
      Toast.show({ content: '创建成功' })
      Taro.navigateBack()
    } catch (error) {
      Toast.show({ content: '创建失败，请重试' })
      console.error('创建习惯失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (templateId){
      getHabitTemplate(templateId).then(res => {
        setForm((form)=>({
          ...form,
          name: res.name,
          description: res.description,
        }))
      })
    }
    
  }, [templateId])

  return (
    <View className='create-habit-page'>
      <View className='form-container'>
        {/* 习惯名称 */}
        <View className='form-section'>
          <View className='section-title'>习惯名称</View>
          <Input
            className='habit-name-input'
            value={form.name}
            onChange={(value) => handleInputChange('name', value)}
            placeholder='请输入习惯名称'
            maxLength={20}
          />
        </View>

        {/* 习惯图标 */}
        <View className='form-section'>
          <View className='section-title'>习惯图标</View>
          <View className='icon-grid'>
            {HABIT_ICONS.map((icon) => (
              <View
                key={icon.key}
                className={`icon-item ${form.icon === icon.key ? 'selected' : ''}`}
                onClick={() => handleInputChange('icon', icon.key)}
              >
                <View className='icon-emoji'>{icon.emoji}</View>
              </View>
            ))}
          </View>
        </View>

        {/* 主题颜色 */}
        <View className='form-section'>
          <View className='section-title'>主题颜色</View>
          <View className='color-grid'>
            {THEME_COLORS.map((color) => (
              <View
                key={color.key}
                className={`color-item ${form.theme_color === color.key ? 'selected' : ''}`}
                style={{ backgroundColor: color.color }}
                onClick={() => handleInputChange('theme_color', color.key)}
              />
            ))}
          </View>
        </View>

        {/* 打卡频率 */}
        <View className='form-section'>
          <View className='section-title'>打卡频率</View>
          <View className='frequency-grid'>
            {FREQUENCIES.map((freq) => (
              <View
                key={freq.key}
                className={`frequency-item ${form.frequency.includes(freq.key) ? 'selected' : ''}`}
                onClick={() => handleFrequencyToggle(freq.key)}
              >
                {freq.name}
              </View>
            ))}
          </View>
        </View>

        {/* 打卡时间 */}
        <View className='form-section'>
          <View className='section-title'>打卡时间</View>
          <View
            className='time-selector'
            onClick={() => setShowTimePicker(true)}
          >
            <View className='time-display'>
              {form.preferred_time}
            </View>
            <View className='time-arrow'>›</View>
          </View>

          <DatePicker
            visible={showTimePicker}
            title='选择打卡时间'
            type="hour-minutes"
            defaultValue={new Date(form.preferred_time)}
            onConfirm={(_, time) => handleTimeChange(time)}
            onClose={() => setShowTimePicker(false)}
          />
        </View>

        {/* 状态 */}
        <View className='form-section'>
          <View className='section-title'>状态</View>
          <View className='option-buttons'>
            {STATUSES.map((status) => (
              <View
                key={status.key}
                className={`option-button ${form.status === status.key ? 'selected' : ''}`}
                onClick={() => handleInputChange('status', status.key)}
              >
                {status.name}
              </View>
            ))}
          </View>
        </View>

        <View className='form-section'>
          <View className='section-title'>
            描述
            <View className='optional-tag'>选填</View>
          </View>
          <TextArea
            className='description-input'
            value={form.description}
            onChange={(value) => handleInputChange('description', value)}
            placeholder='描述一下这个习惯...'
            maxLength={200}
            rows={3}
          />
        </View>


      </View>

      {/* 保存按钮 */}
      <View className='save-button-container'>
        <Button
          className='save-button'
          type='primary'
          loading={loading}
          onClick={handleSave}
        >
          保存
        </Button>
      </View>
    </View>
  )
}

export default CreateHabit