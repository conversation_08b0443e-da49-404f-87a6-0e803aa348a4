.create-habit-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .form-container {
    flex: 1;
    padding: 20px;
    overflow: auto;
  }

  .form-section {
    margin-bottom: 32px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      .optional-tag {
        margin-left: 8px;
        font-size: 12px;
        color: #999;
        background-color: #f0f0f0;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }

    .habit-name-input {
      background-color: #fff;
      border-radius: 12px;
      border: 1px solid #ECDED5;
    }

    .icon-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;

      .icon-item {
        width: 40px;
        height: 40px;
        background-color: #F5ECE7;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid transparent;
        transition: all 0.2s ease;

        &.selected {
          border-color: #ECDED5;
        }

        .icon-emoji {
          font-size: 24px;
        }
      }
    }

    .color-grid {
      display: flex;
      gap: 16px;

      .color-item {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 3px solid transparent;
        transition: all 0.2s ease;
        cursor: pointer;

        &.selected {
          // border-color: #fff;
          transform: scale(1.2);
        }
      }
    }

    .description-input {
      background-color: #fff;
      border-radius: 12px;
      padding: 16px;
      border: 1px solid #ECDED5;
      min-height: 40px;
    }

    .frequency-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;

      .frequency-item {
        padding: 12px 8px;
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        font-size: 14px;
        color: #666;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;

        &.selected {
          background-color: #7FB3D3;
          color: #fff;
          border-color: #7FB3D3;
        }
      }
    }

    .time-selector {
      background-color: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 12px;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        background-color: #f8f8f8;
      }

      .time-display {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .time-arrow {
        font-size: 18px;
        color: #999;
        transform: rotate(90deg);
      }
    }

    .option-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .option-button {
        padding: 8px 16px;
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 20px;
        font-size: 14px;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;

        &.selected {
          background-color: #7FB3D3;
          color: #fff;
          border-color: #7FB3D3;
        }
      }
    }
  }

  .save-button-container {
    padding: 20px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;

    .save-button {
      width: 100%;
      height: 48px;
      background-color: #7FB3D3;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}