.add-baby-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;

  .form-item {
    padding: 16px 0;

    .form-label {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 500;
      color: #333;

      .required {
        color: #ff4757;
        margin-right: 4px;
        font-size: 16px;
      }
    }

    .error-text {
      color: #ff4757;
      font-size: 12px;
      margin-top: 8px;
      padding-left: 4px;
    }

    .radio-group {
      display: flex;
      gap: 24px;
      margin-top: 8px;
    }
  }

  .submit-section {
    margin-top: 40px;
    padding: 0 20px;
  }
}

// 覆盖NutUI默认样式
.nut-cell-group {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.nut-cell {
  padding: 0 16px;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
}

.nut-input {
  border: none;
  background: transparent;
  font-size: 16px;
  
  &::placeholder {
    color: #999;
  }
}

.nut-radio {
  .nut-radio__label {
    font-size: 16px;
    color: #333;
  }
}

.nut-button {
  border-radius: 12px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}
