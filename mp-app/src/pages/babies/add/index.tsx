import { useState } from 'react'
import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import {
  Form,
  Input,
  DatePicker,
  Radio,
  Button,
  Cell,
  ConfigProvider
} from '@nutui/nutui-react-taro'
import zhCN from '@nutui/nutui-react-taro/dist/locales/zh-CN'
import { createBaby, type BabyData } from '../../../services/baby'
import './index.scss'

interface BabyFormData extends BabyData {}

function AddBaby() {
  const [formData, setFormData] = useState<BabyFormData>({
    nickname: '',
    birthDate: '',
    gender: ''
  })

  const [showDatePicker, setShowDatePicker] = useState(false)
  const [errors, setErrors] = useState<Partial<BabyFormData>>({})

  // 处理表单字段变化
  const handleInputChange = (field: keyof BabyFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // 清除对应字段的错误信息
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  // 日期选择确认
  const handleDateConfirm = (options: any) => {
    const { selectedValue } = options
    const dateStr = `${selectedValue[0]}-${String(selectedValue[1]).padStart(2, '0')}-${String(selectedValue[2]).padStart(2, '0')}`
    handleInputChange('birthDate', dateStr)
    setShowDatePicker(false)
  }

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<BabyFormData> = {}

    if (!formData.nickname.trim()) {
      newErrors.nickname = '请输入宝宝小名'
    }

    if (!formData.birthDate) {
      newErrors.birthDate = '请选择出生日期'
    }

    if (!formData.gender) {
      newErrors.gender = '请选择性别'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    try {
      Taro.showLoading({ title: '保存中...' })

      // 调用API保存数据
      await createBaby(formData)

      Taro.hideLoading()
      Taro.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('保存失败:', error)
      Taro.hideLoading()
      Taro.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  }

  return (
    <ConfigProvider locale={zhCN}>
      <View className='add-baby-page'>
        <Form>
          <Cell.Group>
            <Cell>
              <View className='form-item'>
                <View className='form-label'>
                  <View className='required'>*</View>
                  宝宝小名
                </View>
                <Input
                  placeholder='请输入宝宝小名'
                  value={formData.nickname}
                  onChange={(value) => handleInputChange('nickname', value)}
                  clearable
                />
                {errors.nickname && (
                  <View className='error-text'>{errors.nickname}</View>
                )}
              </View>
            </Cell>

            <Cell>
              <View className='form-item'>
                <View className='form-label'>
                  <View className='required'>*</View>
                  出生日期
                </View>
                <Input
                  placeholder='请选择出生日期'
                  value={formData.birthDate}
                  readOnly
                  onClick={() => setShowDatePicker(true)}
                />
                {errors.birthDate && (
                  <View className='error-text'>{errors.birthDate}</View>
                )}
              </View>
            </Cell>

            <Cell>
              <View className='form-item'>
                <View className='form-label'>
                  <View className='required'>*</View>
                  性别
                </View>
                <Radio.Group
                  value={formData.gender}
                  onChange={(value) => handleInputChange('gender', String(value))}
                >
                  <View className='radio-group'>
                    <Radio value='male'>男孩</Radio>
                    <Radio value='female'>女孩</Radio>
                  </View>
                </Radio.Group>
                {errors.gender && (
                  <View className='error-text'>{errors.gender}</View>
                )}
              </View>
            </Cell>
          </Cell.Group>
        </Form>

        <View className='submit-section'>
          <Button
            type='primary'
            size='large'
            onClick={handleSubmit}
          >
            保存
          </Button>
        </View>

        <DatePicker
          visible={showDatePicker}
          type='date'
          title='选择出生日期'
          onConfirm={handleDateConfirm}
          onClose={() => setShowDatePicker(false)}
        />
      </View>
    </ConfigProvider>
  )
}

export default AddBaby