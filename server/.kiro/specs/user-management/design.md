# 设计文档

## 概述

用户管理模块采用NestJS框架构建RESTful API，使用Supabase作为数据库和认证服务。系统包含两个独立的用户体系：客户端用户（微信小程序用户）和后台管理员用户。客户端支持微信小程序openid认证，提供用户注册、个人资料管理、宝宝档案管理和家庭成员邀请功能。后台管理系统支持管理员账号密码认证，为将来的管理功能提供基础。

## 架构

### 技术栈
- **后端框架**: NestJS (TypeScript)
- **数据库**: Supabase (PostgreSQL)
- **认证**: 微信小程序 + JWT
- **文件存储**: Supabase Storage
- **包管理**: yarn

### 架构模式

- **模块化架构**: 按功能域划分模块 (User, Baby, Family, Admin)
- **分层架构**: Controller -> Service -> Repository
- **依赖注入**: 使用NestJS内置DI容器
- **双用户体系**: 客户端用户和管理员用户分离

## 组件和接口

### 模块结构

```
src/
├── user/                          # 客户端用户模块
│   ├── user.module.ts
│   ├── user.controller.ts
│   ├── user.service.ts
│   ├── user.repository.ts
│   ├── dto/
│   │   ├── create-user.dto.ts
│   │   ├── update-user.dto.ts
│   │   └── user-response.dto.ts
│   └── entities/
│       └── user.entity.ts
├── baby/
│   ├── baby.module.ts
│   ├── baby.controller.ts
│   ├── baby.service.ts
│   ├── baby.repository.ts
│   ├── dto/
│   │   ├── create-baby.dto.ts
│   │   ├── update-baby.dto.ts
│   │   └── baby-response.dto.ts
│   └── entities/
│       └── baby.entity.ts
├── family/
│   ├── family.module.ts
│   ├── family.controller.ts
│   ├── family.service.ts
│   ├── family.repository.ts
│   ├── dto/
│   │   ├── create-invitation.dto.ts
│   │   └── family-member.dto.ts
│   └── entities/
│       ├── family-member.entity.ts
│       └── invitation.entity.ts
├── admin/                         # 后台管理员模块
│   ├── admin.module.ts
│   ├── admin.controller.ts
│   ├── admin.service.ts
│   ├── admin.repository.ts
│   ├── dto/
│   │   ├── create-admin.dto.ts
│   │   ├── update-admin.dto.ts
│   │   ├── admin-login.dto.ts
│   │   └── admin-response.dto.ts
│   └── entities/
│       └── admin.entity.ts

├── auth/
│   ├── auth.module.ts
│   ├── auth.controller.ts         # 客户端认证
│   ├── auth.service.ts
│   ├── admin-auth.controller.ts   # 管理员认证
│   ├── admin-auth.service.ts
│   ├── guards/
│   │   ├── jwt-auth.guard.ts      # 客户端JWT守卫
│   │   └── admin-jwt-auth.guard.ts # 管理员JWT守卫
│   └── strategies/
│       ├── jwt.strategy.ts        # 客户端JWT策略
│       └── admin-jwt.strategy.ts  # 管理员JWT策略
├── upload/
│   ├── upload.module.ts
│   ├── upload.controller.ts
│   ├── upload.service.ts
│   └── dto/
│       └── upload-response.dto.ts
└── common/
    ├── decorators/
    │   ├── current-user.decorator.ts
    │   └── current-admin.decorator.ts
    ├── filters/
    │   └── http-exception.filter.ts
    └── interceptors/
        └── transform.interceptor.ts
```

### API 接口设计

#### 客户端认证模块 (Auth)

```typescript
POST /auth/wechat-login
Body: { code: string }
Response: { access_token: string, user: UserResponse }

POST /auth/refresh
Headers: { Authorization: Bearer <token> }
Response: { access_token: string }
```

#### 管理员认证模块 (Admin Auth)

```typescript
POST /admin/auth/login
Body: { username: string, password: string }
Response: { access_token: string, admin: AdminResponse }

POST /admin/auth/refresh
Headers: { Authorization: Bearer <admin_token> }
Response: { access_token: string }
```

#### 用户模块 (User)
```typescript
GET /users/profile
Headers: { Authorization: Bearer <token> }
Response: UserResponse // 包含 default_baby_id 和 default_baby 信息

PUT /users/profile
Headers: { Authorization: Bearer <token> }
Body: UpdateUserDto // 包含 avatar_url 字段
Response: UserResponse
```

#### 宝宝模块 (Baby)
```typescript
GET /babies
Headers: { Authorization: Bearer <token> }
Response: BabyResponse[] // 返回用户有权限访问的所有宝宝，包含 is_default 标

POST /babies
Headers: { Authorization: Bearer <token> }
Body: CreateBabyDto // 包含 avatar_url 和 is_default 字段
Response: BabyResponse

PUT /babies/:id
Headers: { Authorization: Bearer <token> }
Body: UpdateBabyDto // 包含 avatar_url 和 is_default 字段
Response: BabyResponse

DELETE /babies/:id
Headers: { Authorization: Bearer <token> }
Response: { success: boolean }
```

#### 上传模块 (Upload)
```typescript
POST /upload/image
Headers: { Authorization: Bearer <token> }
Body: FormData (file)
Response: { image_url: string }
```

#### 家庭模块 (Family)

```typescript
POST /family/invite
Headers: { Authorization: Bearer <token> }
Body: { baby_id: string, phone?: string }
Response: { invitation_code: string, expires_at: Date }

POST /family/accept-invitation
Headers: { Authorization: Bearer <token> }
Body: { invitation_code: string, role: number } // 接受邀请时填写自己的角色
Response: { success: boolean }
```

#### 管理员模块 (Admin)

```typescript
GET /admin/profile
Headers: { Authorization: Bearer <admin_token> }
Response: AdminResponse

PUT /admin/profile
Headers: { Authorization: Bearer <admin_token> }
Body: UpdateAdminDto
Response: AdminResponse
```



## 数据模型

### Supabase 数据库表结构

#### users 表
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  openid VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(50),
  phone VARCHAR(20),
  avatar_url TEXT,
  default_baby_id UUID REFERENCES babies(id) ON DELETE SET NULL, -- 默认展示的宝宝
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### babies 表
```sql
CREATE TABLE babies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  nickname VARCHAR(50) NOT NULL,
  birth_date DATE NOT NULL,
  gender INTEGER NOT NULL CHECK (gender IN (1, 2)), -- 1: 男, 2: 女
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### family_members 表
```sql
CREATE TABLE family_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  baby_id UUID REFERENCES babies(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role INTEGER DEFAULT 3 CHECK (role IN (1, 2, 3)), -- 1: 爸爸, 2: 妈妈, 3: 其他照顾者
  is_owner BOOLEAN DEFAULT FALSE, -- 是否为宝宝档案的创建者
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(baby_id, user_id)
);
```

#### invitations 表

```sql
CREATE TABLE invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  baby_id UUID REFERENCES babies(id) ON DELETE CASCADE,
  inviter_id UUID REFERENCES users(id) ON DELETE CASCADE,
  invitation_code VARCHAR(10) UNIQUE NOT NULL,
  phone VARCHAR(20),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE,
  used_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### admin_users 表

```sql
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  email VARCHAR(100),
  full_name VARCHAR(100),
  is_active BOOLEAN DEFAULT TRUE,
  is_super_admin BOOLEAN DEFAULT FALSE,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```



### 枚举常量定义

```typescript
// 性别枚举
export enum Gender {
  MALE = 1,
  FEMALE = 2
}

// 家庭成员角色枚举
export enum FamilyRole {
  FATHER = 1,          // 爸爸
  MOTHER = 2,          // 妈妈
  OTHER_CAREGIVER = 3  // 其他照顾者
}

// 管理员权限枚举
export enum AdminRole {
  ADMIN = 1,           // 普通管理员
  SUPER_ADMIN = 2      // 超级管理员
}
```

### TypeScript 实体类

#### User Entity
```typescript
export class User {
  id: string;
  openid: string;
  username?: string;
  phone?: string;
  avatar_url?: string;
  default_baby_id?: string; // 默认展示的宝宝ID
  created_at: Date;
  updated_at: Date;
}
```

#### Baby Entity
```typescript
export class Baby {
  id: string;
  owner_id: string;
  nickname: string;
  birth_date: Date;
  gender: number; // 1: 男, 2: 女
  avatar_url?: string;
  created_at: Date;
  updated_at: Date;
}
```

#### FamilyMember Entity
```typescript
export class FamilyMember {
  id: string;
  baby_id: string;
  user_id: string;
  role: number; // 1: 爸爸, 2: 妈妈, 3: 其他照顾者
  is_owner: boolean; // 是否为宝宝档案的创建者
  created_at: Date;
}
```

#### Invitation Entity

```typescript
export class Invitation {
  id: string;
  baby_id: string;
  inviter_id: string;
  invitation_code: string;
  phone?: string;
  expires_at: Date;
  used_at?: Date;
  used_by?: string;
  created_at: Date;
}
```

#### AdminUser Entity

```typescript
export class AdminUser {
  id: string;
  username: string;
  password_hash: string;
  email?: string;
  full_name?: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}
```



## 错误处理

### 错误代码规范

错误代码采用5位数字格式：`XYZNN`
- `X`: 模块代码 (1: 认证, 2: 用户, 3: 宝宝, 4: 家庭, 5: 上传, 6: 管理员)
- `Y`: 错误类型 (0: 通用, 1: 验证, 2: 权限, 3: 业务逻辑, 4: 资源)
- `Z`: 操作类型 (0: 通用, 1: 创建, 2: 查询, 3: 更新, 4: 删除)
- `NN`: 具体错误序号 (01-99)

### 错误类型定义
```typescript
export enum ErrorCode {
  // 认证错误 (10xxx)
  INVALID_WECHAT_CODE = 10101,    // 微信授权码无效
  UNAUTHORIZED = 10201,           // 未授权访问
  TOKEN_EXPIRED = 10202,          // 令牌过期
  
  // 用户错误 (20xxx)
  USER_NOT_FOUND = 20401,         // 用户不存在
  USERNAME_TAKEN = 21301,         // 用户名已被占用
  INVALID_PHONE_FORMAT = 21302,   // 手机号格式无效
  
  // 宝宝错误 (30xxx)
  BABY_NOT_FOUND = 30401,         // 宝宝档案不存在
  BABY_LIMIT_EXCEEDED = 31101,    // 宝宝数量超限(最多5个)
  BABY_ACCESS_DENIED = 30201,     // 无权访问宝宝档案
  INVALID_BABY_DATA = 31102,      // 宝宝数据无效
  
  // 家庭错误 (40xxx)
  INVITATION_NOT_FOUND = 40401,   // 邀请不存在
  INVITATION_EXPIRED = 43101,     // 邀请已过期
  INVITATION_ALREADY_USED = 43102, // 邀请已被使用
  ALREADY_FAMILY_MEMBER = 43103,  // 已是家庭成员
  INVALID_FAMILY_ROLE = 41301,    // 无效的家庭角色
  
  // 上传错误 (50xxx)
  INVALID_FILE_FORMAT = 51101,    // 文件格式无效
  FILE_TOO_LARGE = 51102,         // 文件过大
  UPLOAD_FAILED = 50301,          // 上传失败
  
  // 管理员错误 (60xxx)
  ADMIN_NOT_FOUND = 60401,        // 管理员不存在
  INVALID_ADMIN_CREDENTIALS = 61101, // 管理员凭据无效
  ADMIN_ACCESS_DENIED = 60201,    // 管理员权限不足
  ADMIN_USERNAME_TAKEN = 61301    // 管理员用户名已被占用
}
```

### 自定义业务异常类
```typescript
export class BusinessException extends HttpException {
  constructor(
    public readonly errorCode: ErrorCode,
    message: string,
    httpStatus: HttpStatus = HttpStatus.BAD_REQUEST
  ) {
    super(message, httpStatus);
  }
}
```

### 全局异常过滤器
```typescript
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorCode = 50000; // 系统错误
    let message = 'Internal server error';
    
    if (exception instanceof BusinessException) {
      status = exception.getStatus();
      errorCode = exception.errorCode;
      message = exception.message;
    } else if (exception instanceof HttpException) {
      status = exception.getStatus();
      errorCode = status * 1000; // HTTP状态码转换
      message = exception.getResponse() as string;
    }
    
    response.status(status).json({
      success: false,
      error: {
        code: errorCode,
        message,
        timestamp: new Date().toISOString()
      }
    });
  }
}
```

### 上传服务设计

#### UploadService 公共方法
```typescript
export class UploadService {
  async uploadImage(
    file: Express.Multer.File,
    userId: string // 用于生成唯一的文件路径
  ): Promise<string> {
    // 1. 验证文件格式和大小
    // 2. 生成唯一文件名：{userId}/{timestamp}.{ext}
    // 3. 上传到Supabase Storage
    // 4. 返回图片URL
  }
  
  private validateImageFile(file: Express.Multer.File): void {
    // 验证文件格式和大小
  }
  
  private generateFileName(userId: string, originalName: string): string {
    // 生成唯一文件名
  }
}
```

#### 文件验证规则
- **支持格式**: jpg, jpeg, png, webp
- **文件大小**: 最大5MB
- **图片尺寸**: 自动压缩到合适尺寸
- **存储路径**: `images/{userId}/{timestamp}.{ext}`

### 默认宝宝业务规则

#### 创建宝宝 (POST /babies)
1. **首个宝宝**: 用户的第一个宝宝自动设为默认（忽略is_default参数）
2. **后续宝宝**: 根据is_default参数决定是否设为默认
3. **设为默认**: 如果is_default=true，则取消其他宝宝的默认状态
4. **权限验证**: 只有宝宝的创建者可以设置默认状态

#### 更新宝宝 (PUT /babies/:id)
1. **设为默认**: 如果is_default=true，则取消其他宝宝的默认状态，更新用户的default_baby_id
2. **取消默认**: 如果is_default=false且该宝宝是当前默认，则选择最早创建的宝宝作为新默认
3. **权限验证**: 只有宝宝的所有者或有权限的家庭成员可以更新
4. **家庭成员限制**: 家庭成员不能修改默认宝宝设置，只有所有者可以

#### 删除宝宝 (DELETE /babies/:id)
1. **删除默认宝宝**: 自动选择下一个宝宝作为默认（按创建时间）
2. **删除最后宝宝**: 用户的default_baby_id设为NULL
3. **权限验证**: 只有宝宝的所有者可以删除

#### 查询逻辑
1. **GET /babies**: 返回所有有权限的宝宝，包含`is_default`标识
2. **GET /babies/default**: 直接返回默认宝宝信息
3. **GET /users/profile**: 包含默认宝宝的基本信息



## 安全考虑

### 认证和授权
- JWT token过期时间设置为7天
- 实现token刷新机制
- 使用Guards保护需要认证的路由

### 数据验证
- 使用class-validator进行输入验证
- 文件上传大小和格式限制
- SQL注入防护（使用参数化查询）

### 权限控制

- 客户端用户只能访问自己的数据
- 家庭成员权限验证
- 宝宝档案访问权限检查
- 管理员权限控制
- 管理员账户通过数据库直接创建和管理

### 数据保护

- 敏感数据加密存储
- 管理员密码使用bcrypt加密
- 定期清理过期邀请码
- 审计日志记录重要操作

## 管理员系统设计

### 认证机制

管理员系统采用独立的JWT认证机制，与客户端用户完全分离：

- **登录方式**: 用户名 + 密码
- **密码加密**: 使用bcrypt进行哈希存储
- **Token机制**: 独立的JWT secret和过期时间
- **权限验证**: 基于角色的访问控制(RBAC)

### 初始化数据

系统启动时需要初始化以下数据：

#### 默认管理员账户

管理员账户通过数据库迁移脚本创建，不提供API接口：

```sql
INSERT INTO admin_users (username, password_hash, full_name, is_super_admin) 
VALUES ('admin', '$2b$10$...', '系统管理员', true);
```

管理员账户的创建、删除和权限管理都通过数据库操作完成。

