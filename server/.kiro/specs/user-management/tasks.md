# 实现计划

- [x] 1. 设置项目基础结构和核心接口
  - 创建模块目录结构 (user, baby, family, admin, auth, upload, common)
  - 定义系统边界的核心接口和类型
  - 配置Supabase连接和基础配置
  - _需求: 1.1, 2.1, 3.1, 4.1_

- [x] 2. 实现数据模型和验证
- [x] 2.1 创建核心数据模型接口和类型
  - 编写所有数据模型的TypeScript接口
  - 实现数据完整性验证函数
  - _需求: 1.1, 2.1, 3.1, 4.1_

- [x] 2.2 实现User实体和验证
  - 编写User类和验证方法
  - 创建User模型的单元测试
  - _需求: 1.1, 2.1_

- [x] 2.3 实现Baby实体和关系处理
  - 编写Baby类和关系管理
  - 编写关系管理的单元测试
  - _需求: 3.1_

- [x] 2.4 实现FamilyMember和Invitation实体
  - 编写家庭成员和邀请相关的数据模型
  - 创建相关的单元测试
  - _需求: 4.1_

- [x] 2.5 实现AdminUser实体
  - 编写管理员用户数据模型
  - 实现密码哈希和验证逻辑
  - _需求: 管理员系统_

- [x] 3. 创建存储机制
- [x] 3.1 实现数据库连接工具
  - 编写连接管理代码
  - 创建数据库操作的错误处理工具
  - _需求: 1.1, 2.1, 3.1, 4.1_

- [x] 3.2 实现Repository模式的数据访问
  - 编写基础Repository接口
  - 实现具体Repository的CRUD操作
  - 编写Repository操作的单元测试
  - _需求: 1.1, 2.1, 3.1, 4.1_

- [x] 4. 实现认证系统
- [x] 4.1 实现微信小程序认证
  - 编写微信openid验证逻辑
  - 实现JWT token生成和验证
  - 创建客户端认证的单元测试
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 4.2 实现管理员认证系统
  - 编写管理员用户名密码认证
  - 实现独立的管理员JWT策略
  - 创建管理员认证的单元测试
  - _需求: 管理员系统_

- [x] 4.3 实现认证守卫和装饰器
  - 编写JWT认证守卫
  - 实现@CurrentUser和@CurrentAdmin装饰器
  - 创建守卫和装饰器的单元测试
  - _需求: 1.1, 管理员系统_

- [x] 5. 实现用户管理服务
- [x] 5.1 实现用户注册和登录服务
  - 编写微信openid注册逻辑
  - 实现用户登录和token刷新
  - 创建用户认证服务的单元测试
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 5.2 实现用户资料管理服务
  - 编写个人资料更新逻辑
  - 实现头像上传和存储
  - 创建用户资料服务的单元测试
  - _需求: 2.1, 2.2_

- [x] 6. 实现宝宝档案管理服务
- [x] 6.1 实现宝宝档案CRUD操作
  - 编写宝宝档案创建、查询、更新、删除逻辑
  - 实现宝宝数量限制验证（最多5个）
  - 创建宝宝档案服务的单元测试
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 6.2 实现默认宝宝管理逻辑
  - 编写默认宝宝设置和切换逻辑
  - 实现删除默认宝宝时的自动选择
  - 创建默认宝宝逻辑的单元测试
  - _需求: 3.1, 3.4, 3.5, 3.6_

- [x] 7. 实现家庭成员管理服务
- [x] 7.1 实现邀请码生成和管理
  - 编写邀请码生成逻辑
  - 实现邀请过期和使用状态管理
  - 创建邀请管理的单元测试
  - _需求: 4.1, 4.6_

- [x] 7.2 实现家庭成员邀请和接受
  - 编写发送邀请和接受邀请逻辑
  - 实现家庭成员权限验证
  - 创建家庭成员服务的单元测试
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 8. 实现文件上传服务

- [x] 8.1 实现图片上传功能
  - 编写文件验证和上传逻辑
  - 实现Supabase Storage集成
  - 创建文件上传服务的单元测试
  - _需求: 2.2, 3.2_

- [x] 9. 实现管理员系统服务
- [x] 9.1 实现管理员认证服务
  - 编写管理员登录和token管理
  - 实现管理员资料查询和更新
  - 创建管理员服务的单元测试
  - _需求: 管理员系统_

- [x] 10. 实现API控制器
- [x] 10.1 实现客户端认证控制器
  - 编写微信登录和token刷新接口
  - 实现请求验证和响应格式化
  - 创建认证控制器的单元测试
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 10.2 实现用户管理控制器
  - 编写用户资料查询和更新接口
  - 实现请求参数验证和错误处理
  - 创建用户控制器的单元测试
  - _需求: 2.1, 2.2_

- [x] 10.3 实现宝宝管理控制器
  - 编写宝宝档案CRUD接口
  - 实现权限验证和业务规则检查
  - 创建宝宝控制器的单元测试
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 10.4 实现家庭管理控制器
  - 编写邀请发送和接受接口
  - 实现家庭成员管理接口
  - 创建家庭控制器的单元测试
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6_

- [x] 10.5 实现文件上传控制器
  - 编写图片上传接口
  - 实现文件类型和大小验证
  - 创建上传控制器的单元测试
  - _需求: 2.2, 3.2_

- [x] 10.6 实现管理员控制器
  - 编写管理员认证和资料管理接口
  - 实现管理员权限验证
  - 创建管理员控制器的单元测试
  - _需求: 管理员系统_

- [x] 11. 实现全局中间件和过滤器
- [x] 11.1 实现全局异常过滤器
  - 编写统一的错误响应格式
  - 实现业务异常和HTTP异常处理
  - 创建异常过滤器的单元测试
  - _需求: 所有模块的错误处理_

- [x] 11.2 实现响应转换拦截器
  - 编写统一的响应格式转换
  - 实现成功响应的标准化
  - 创建响应拦截器的单元测试
  - _需求: 所有API接口_

- [x] 12. 创建数据库迁移脚本
- [x] 12.1 创建数据库表结构迁移
  - 编写所有表的创建脚本
  - 实现表关系和约束
  - 创建迁移脚本的验证测试
  - _需求: 1.1, 2.1, 3.1, 4.1, 管理员系统_

- [x] 12.2 创建初始数据迁移
  - 编写默认管理员账户插入脚本
  - 实现基础配置数据初始化
  - 创建数据初始化的验证测试
  - _需求: 管理员系统_

- [x] 13. 集成所有模块
- [x] 13.1 配置模块依赖和注入
  - 配置所有模块的依赖关系
  - 实现服务的依赖注入
  - 创建模块集成的测试
  - _需求: 所有模块_

- [x] 13.2 实现应用启动和配置
  - 配置应用启动流程
  - 实现环境变量和配置管理
  - 创建应用启动的测试
  - _需求: 所有模块_
