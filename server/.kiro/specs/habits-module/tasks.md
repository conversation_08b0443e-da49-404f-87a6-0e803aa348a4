# 实施计划

- [x] 1. 设置项目结构和核心接口
  - 创建习惯模块的目录结构（src/habits）
  - 定义核心接口和类型定义
  - 创建基础模块文件结构
  - _需求: 1.1, 2.1, 3.1_

- [x] 2. 创建数据库迁移和表结构
  - 编写习惯分类表迁移脚本
  - 编写习惯模板表迁移脚本
  - 编写用户习惯表迁移脚本
  - 编写打卡记录表迁移脚本
  - 设置表约束和索引
  - _需求: 所有数据存储需求_

- [x] 3. 添加习惯模块错误代码
  - 在 ErrorCode 枚举中添加习惯相关错误代码（70xxx系列）
  - 定义所有业务异常类型
  - _需求: 所有错误处理需求_

- [x] 4. 实现数据模型和实体
- [x] 4.1 创建习惯分类实体
  - 实现 HabitCategory 实体类，支持层级结构
  - 添加父子关系和层级验证
  - 添加数据验证和业务方法
  - 编写实体单元测试
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7_

- [x] 4.2 创建习惯模板实体
  - 实现 HabitTemplate 实体类
  - 添加数据验证和业务方法
  - 编写实体单元测试
  - _需求: 2.1, 2.2, 2.3_

- [x] 4.3 创建用户习惯实体
  - 实现 UserHabit 实体类
  - 添加数据验证和业务方法
  - 编写实体单元测试
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 4.4 创建打卡记录实体
  - 实现 HabitCheckIn 实体类
  - 添加数据验证和业务方法
  - 编写实体单元测试
  - _需求: 7.1, 7.2, 7.3, 7.4_

- [x] 5. 实现数据访问层（Repository）
- [x] 5.1 实现习惯分类仓储
  - 创建 HabitCategoryRepository 类
  - 实现分类的 CRUD 操作，支持层级查询
  - 添加树形结构查询和子分类查询功能
  - 添加分类查询和排序功能
  - 编写仓储层单元测试
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7_

- [x] 5.2 实现习惯模板仓储
  - 创建 HabitTemplateRepository 类
  - 实现模板的 CRUD 操作
  - 添加按分类查询模板功能
  - 编写仓储层单元测试
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 5.3 实现用户习惯仓储
  - 创建 UserHabitRepository 类
  - 实现用户习惯的 CRUD 操作
  - 添加按用户和宝宝查询功能
  - 编写仓储层单元测试
  - _需求: 5.1, 5.2, 6.6_

- [x] 5.4 实现打卡记录仓储
  - 创建 HabitCheckInRepository 类
  - 实现打卡记录的创建和查询
  - 添加统计查询功能
  - 编写仓储层单元测试
  - _需求: 7.1, 7.3, 8.1, 9.1_

- [x] 6. 创建DTO和验证
  - 创建所有API的请求和响应DTO
  - 添加数据验证装饰器
  - 实现输入数据清理和转换
  - _需求: 所有数据验证需求_

- [-] 7. 实现业务逻辑层（Service）
- [x] 7.1 实现习惯分类服务
  - 创建 HabitCategoryService 类
  - 实现层级分类管理业务逻辑
  - 添加分类删除前的依赖检查（子分类和模板）
  - 添加层级验证和父分类验证
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7_

- [x] 7.2 实现习惯模板服务
  - 创建 HabitTemplateService 类
  - 实现模板管理业务逻辑
  - 添加模板名称重复检查
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 7.3 实现用户习惯服务
  - 创建 UserHabitService 类
  - 实现用户习惯管理业务逻辑
  - 添加权限验证和数据隔离
  - _需求: 5.1, 5.2, 5.3, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 7.4 实现打卡服务
  - 创建 HabitCheckInService 类
  - 实现打卡业务逻辑和验证
  - 添加重复打卡防护
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 7.5 实现统计分析服务
  - 创建 HabitStatisticsService 类
  - 实现日、周、月统计功能
  - 添加首页概览统计
  - _需求: 8.1, 8.2, 8.4, 9.1, 9.2, 9.3, 9.4, 9.5, 10.3, 10.5_

- [x] 8. 实现管理员API控制器
- [x] 8.1 实现管理员习惯分类控制器
  - 创建 AdminHabitCategoryController
  - 实现层级分类管理的所有API端点
  - 添加树形结构和子分类查询端点
  - 添加管理员权限验证
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7_

- [x] 8.2 实现管理员习惯模板控制器
  - 创建 AdminHabitTemplateController
  - 实现模板管理的所有API端点
  - 添加管理员权限验证
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 9. 实现客户端API控制器
- [x] 9.1 实现习惯库控制器
  - 创建 HabitLibraryController
  - 实现层级习惯库浏览API端点
  - 添加分类树形结构和模板查询功能
  - 添加面包屑导航支持
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6_

- [x] 9.2 实现用户习惯控制器
  - 创建 UserHabitController
  - 实现用户习惯管理API端点
  - 添加用户权限验证
  - _需求: 5.1, 5.2, 5.3, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 9.3 实现打卡控制器
  - 创建 HabitCheckInController
  - 实现打卡相关API端点
  - 添加打卡验证和权限检查
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 9.4 实现统计控制器
  - 创建 HabitStatisticsController
  - 实现统计分析API端点
  - 添加数据权限验证
  - _需求: 8.1, 8.2, 8.3, 8.4, 9.1, 9.2, 9.3, 9.4, 9.5_

- [x] 10. 创建数据库种子数据
  - 编写种子数据脚本，支持层级分类结构
  - 插入默认一级和二级习惯分类
  - 插入默认习惯模板，关联到相应分类
  - 验证种子数据正确性和层级关系
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8_

- [-] 11. 模块集成和配置
- [x] 11.1 创建习惯主模块
  - 创建 HabitModule 主模块
  - 配置所有子模块导入
  - 设置依赖注入
  - _需求: 所有模块集成需求_

- [x] 11.2 更新习惯主模块配置
  - 在 HabitModule 中添加所有缺失的服务和控制器
  - 配置完整的依赖注入
  - 更新模块导出
  - _需求: 所有模块集成需求_

- [x] 11.3 集成到应用主模块
  - 在 AppModule 中导入 HabitModule
  - 配置路由和中间件
  - 更新应用配置
  - _需求: 所有应用集成需求_

- [x] 12. 实现首页集成功能
  - 创建首页习惯统计API
  - 实现今日打卡率计算
  - 添加今日打卡时间轴
  - _需求: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6_

- [x] 13. 数据库迁移和数据转换
  - 创建数据库迁移脚本，添加parent_id和level字段
  - 编写数据转换脚本，将现有分类转换为一级分类
  - 更新现有分类的层级结构
  - 验证数据迁移的正确性
  - _需求: 层级分类结构支持_
