# 设计文档

## 概述

习惯模块是小脚步应用的核心功能之一，旨在帮助家长培养孩子的良好习惯。该模块采用分层架构设计，包含后台管理系统和客户端系统两个主要部分。后台管理系统负责管理习惯库（分类和模板），客户端系统提供用户习惯创建、跟踪和统计分析功能。

## 架构

### 整体架构

```mermaid
graph TB
    subgraph "客户端层"
        A[首页] --> B[习惯库]
        A --> C[我的习惯]
        A --> D[统计页面]
        B --> E[新建习惯]
        C --> F[习惯详情]
        C --> G[打卡页面]
    end
    
    subgraph "API层"
        H[HabitController] --> I[HabitService]
        J[HabitCategoryController] --> K[HabitCategoryService]
        L[HabitCheckInController] --> M[HabitCheckInService]
        N[AdminHabitController] --> O[AdminHabitService]
    end
    
    subgraph "业务逻辑层"
        I --> P[HabitRepository]
        K --> Q[HabitCategoryRepository]
        M --> R[HabitCheckInRepository]
        O --> P
        O --> Q
    end
    
    subgraph "数据层"
        P --> S[(Supabase)]
        Q --> S
        R --> S
    end
```

### 模块结构

习惯模块将包含以下子模块：

1. **HabitModule** - 主模块，整合所有习惯相关功能
2. **HabitCategoryModule** - 习惯分类管理
3. **HabitTemplateModule** - 习惯模板管理
4. **UserHabitModule** - 用户习惯管理
5. **HabitCheckInModule** - 打卡功能
6. **HabitStatisticsModule** - 统计分析

## 组件和接口

### 核心实体

#### 1. HabitCategory (习惯分类)

```typescript
interface IHabitCategory {
  id: string;
  name: string;
  icon: string;
  parent_id?: string; // 父分类ID，null表示一级分类
  level: number; // 分类层级：1表示一级分类，2表示二级分类
  sort_order: number;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  
  // 关联关系
  parent?: IHabitCategory; // 父分类
  children?: IHabitCategory[]; // 子分类列表
}
```

#### 2. HabitTemplate (习惯模板)
```typescript
interface IHabitTemplate {
  id: string;
  category_id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
```

#### 3. UserHabit (用户习惯)
```typescript
interface IUserHabit {
  id: string;
  user_id: string;
  baby_id: string;
  template_id?: string; // 可选，如果基于模板创建
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[]; // 1-7 表示周一到周日
  preferred_time?: string; // HH:mm 格式
  reward_stars: number;
  status: 'active' | 'archived';
  created_at: Date;
  updated_at: Date;
}
```

#### 4. HabitCheckIn (习惯打卡)
```typescript
interface IHabitCheckIn {
  id: string;
  habit_id: string;
  user_id: string;
  baby_id: string;
  check_in_date: Date; // 打卡日期（不含时间）
  check_in_time: Date; // 实际打卡时间
  notes?: string;
  created_at: Date;
}
```

### API 接口设计

#### 管理员接口 (Admin API)

```typescript
// 习惯分类管理
GET    /admin/habit-categories          // 获取分类列表（支持层级查询）
GET    /admin/habit-categories/tree     // 获取分类树结构
POST   /admin/habit-categories          // 创建分类
PUT    /admin/habit-categories/:id      // 更新分类
DELETE /admin/habit-categories/:id      // 删除分类

// 习惯模板管理
GET    /admin/habit-templates           // 获取模板列表
POST   /admin/habit-templates           // 创建模板
PUT    /admin/habit-templates/:id       // 更新模板
DELETE /admin/habit-templates/:id       // 删除模板

// 数据初始化
POST   /admin/habits/initialize         // 初始化默认数据
```

#### 客户端接口 (Client API)

```typescript
// 习惯库
GET    /habits/library                  // 获取习惯库（按分类层级）
GET    /habits/library/categories       // 获取分类列表（支持层级）
GET    /habits/library/categories/tree  // 获取分类树结构
GET    /habits/library/templates/:categoryId // 获取分类下的模板

// 用户习惯管理
GET    /habits/my-habits               // 获取我的习惯列表
POST   /habits/my-habits               // 创建新习惯
PUT    /habits/my-habits/:id           // 更新习惯
DELETE /habits/my-habits/:id           // 删除习惯
GET    /habits/my-habits/:id           // 获取习惯详情

// 打卡功能
POST   /habits/:id/check-in            // 执行打卡
GET    /habits/:id/check-ins           // 获取打卡记录
GET    /habits/check-ins/today         // 获取今日打卡情况

// 统计分析
GET    /habits/statistics/daily        // 日统计
GET    /habits/statistics/weekly       // 周统计
GET    /habits/statistics/monthly      // 月统计
GET    /habits/statistics/overview     // 首页概览统计
```

## 数据模型

### 数据库表设计

#### habit_categories 表

```sql
CREATE TABLE habit_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  parent_id UUID REFERENCES habit_categories(id) ON DELETE CASCADE,
  level INTEGER NOT NULL DEFAULT 1 CHECK (level IN (1, 2)),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 确保层级结构的一致性
  CONSTRAINT check_parent_level CHECK (
    (level = 1 AND parent_id IS NULL) OR 
    (level = 2 AND parent_id IS NOT NULL)
  )
);
```

#### habit_templates 表
```sql
CREATE TABLE habit_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category_id UUID NOT NULL REFERENCES habit_categories(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  theme_color VARCHAR(7) NOT NULL, -- HEX颜色值
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### user_habits 表
```sql
CREATE TABLE user_habits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
  template_id UUID REFERENCES habit_templates(id) ON DELETE SET NULL,
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  theme_color VARCHAR(7) NOT NULL,
  description TEXT,
  frequency INTEGER[] NOT NULL, -- 数组存储1-7的数字
  preferred_time TIME,
  reward_stars INTEGER DEFAULT 1 CHECK (reward_stars >= 1 AND reward_stars <= 5),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### habit_check_ins 表
```sql
CREATE TABLE habit_check_ins (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  habit_id UUID NOT NULL REFERENCES user_habits(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
  check_in_date DATE NOT NULL,
  check_in_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 确保同一习惯同一天只能打卡一次
  UNIQUE(habit_id, check_in_date)
);
```

### 索引设计

```sql
-- 习惯分类索引
CREATE INDEX idx_habit_categories_active ON habit_categories(is_active, sort_order);
CREATE INDEX idx_habit_categories_parent ON habit_categories(parent_id, level, sort_order);
CREATE INDEX idx_habit_categories_level ON habit_categories(level, is_active, sort_order);

-- 习惯模板索引
CREATE INDEX idx_habit_templates_category ON habit_templates(category_id, is_active);

-- 用户习惯索引
CREATE INDEX idx_user_habits_user_baby ON user_habits(user_id, baby_id, status);
CREATE INDEX idx_user_habits_template ON user_habits(template_id);

-- 打卡记录索引
CREATE INDEX idx_habit_check_ins_habit_date ON habit_check_ins(habit_id, check_in_date DESC);
CREATE INDEX idx_habit_check_ins_user_date ON habit_check_ins(user_id, baby_id, check_in_date DESC);
```

## 错误处理

### 业务异常定义

习惯模块的错误代码将添加到现有的 `ErrorCode` 枚举中，使用 70xxx 系列：

```typescript
// 在 src/common/enums/index.ts 中添加
export enum ErrorCode {
  // ... 现有错误代码 ...
  
  // 习惯错误 (70xxx)
  HABIT_CATEGORY_NOT_FOUND = 70401,      // 习惯分类不存在
  HABIT_CATEGORY_HAS_TEMPLATES = 71101,  // 分类下存在模板，无法删除
  HABIT_CATEGORY_HAS_CHILDREN = 71107,   // 分类下存在子分类，无法删除
  HABIT_CATEGORY_NAME_EXISTS = 71301,    // 分类名称已存在
  HABIT_CATEGORY_LEVEL_INVALID = 71108,  // 分类层级无效
  HABIT_CATEGORY_PARENT_INVALID = 71109, // 父分类无效（不存在或层级错误）
  
  HABIT_TEMPLATE_NOT_FOUND = 70402,      // 习惯模板不存在
  HABIT_TEMPLATE_NAME_EXISTS = 71302,    // 模板名称已存在
  
  USER_HABIT_NOT_FOUND = 70403,          // 用户习惯不存在
  USER_HABIT_ACCESS_DENIED = 70201,      // 无权访问习惯
  USER_HABIT_NAME_EXISTS = 71303,        // 习惯名称已存在
  USER_HABIT_LIMIT_EXCEEDED = 71102,     // 习惯数量超限
  
  HABIT_ALREADY_CHECKED_IN = 71103,      // 今日已打卡
  HABIT_CHECK_IN_DATE_INVALID = 71104,   // 打卡日期无效
  HABIT_NOT_SCHEDULED_TODAY = 71105,     // 今日未安排此习惯
  HABIT_BACKDATE_NOT_ALLOWED = 71106,    // 不允许补打卡
}
```

### 错误处理策略

使用现有的 `BusinessException` 类进行错误处理：

```typescript
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

// 示例用法
throw new BusinessException(
  ErrorCode.HABIT_CATEGORY_NOT_FOUND,
  '习惯分类不存在',
  HttpStatus.NOT_FOUND
);
```

### 错误响应格式

所有错误响应将遵循现有的统一格式：

```typescript
{
  success: false,
  error: {
    code: 70401,
    message: "习惯分类不存在",
    timestamp: "2024-01-01T00:00:00.000Z"
  }
}
```

### 常见错误场景处理

1. **资源不存在错误** (404):
   - `HABIT_CATEGORY_NOT_FOUND`: 习惯分类不存在
   - `HABIT_TEMPLATE_NOT_FOUND`: 习惯模板不存在
   - `USER_HABIT_NOT_FOUND`: 用户习惯不存在

2. **权限错误** (403):
   - `USER_HABIT_ACCESS_DENIED`: 无权访问他人的习惯
   - `ADMIN_ACCESS_DENIED`: 非管理员访问管理接口

3. **业务逻辑错误** (400):
   - `HABIT_ALREADY_CHECKED_IN`: 重复打卡
   - `HABIT_NOT_SCHEDULED_TODAY`: 今日未安排的习惯
   - `HABIT_BACKDATE_NOT_ALLOWED`: 尝试补打卡

4. **冲突错误** (409):
   - `HABIT_CATEGORY_NAME_EXISTS`: 分类名称重复
   - `USER_HABIT_NAME_EXISTS`: 用户习惯名称重复

5. **限制错误** (400):
   - `USER_HABIT_LIMIT_EXCEEDED`: 超出习惯数量限制
   - `HABIT_CATEGORY_HAS_TEMPLATES`: 分类下有模板无法删除

## 测试策略

### 单元测试

1. **实体测试**: 测试实体类的业务逻辑方法
2. **服务测试**: 测试业务逻辑的正确性
3. **仓储测试**: 测试数据访问层的CRUD操作
4. **控制器测试**: 测试API接口的输入输出

### 集成测试

1. **API集成测试**: 测试完整的API调用流程
2. **数据库集成测试**: 测试数据库操作的正确性
3. **模块集成测试**: 测试模块间的协作

### 测试数据

创建测试用的种子数据：
- 默认习惯分类和模板
- 测试用户和宝宝数据
- 模拟打卡记录

## 性能考虑

### 缓存策略

1. **习惯库缓存**: 习惯分类和模板数据相对稳定，可以使用Redis缓存
2. **用户习惯缓存**: 用户的活跃习惯列表可以短期缓存
3. **统计数据缓存**: 统计结果可以按天缓存，减少重复计算

### 数据库优化

1. **分页查询**: 所有列表查询都支持分页
2. **索引优化**: 为常用查询字段创建合适的索引
3. **查询优化**: 避免N+1查询问题，使用JOIN或批量查询

### API性能

1. **响应压缩**: 启用gzip压缩
2. **字段选择**: 支持字段选择，减少不必要的数据传输
3. **批量操作**: 支持批量创建和更新操作

## 安全考虑

### 数据访问控制

1. **用户隔离**: 确保用户只能访问自己的习惯数据
2. **宝宝权限**: 验证用户对宝宝的访问权限
3. **管理员权限**: 管理员接口需要管理员身份验证

### 数据验证

1. **输入验证**: 所有用户输入都需要严格验证
2. **SQL注入防护**: 使用参数化查询
3. **XSS防护**: 对用户输入进行适当的转义和过滤

### 审计日志

记录关键操作的审计日志：
- 习惯创建、修改、删除
- 打卡记录
- 管理员操作

## 部署和监控

### 数据库迁移

使用数据库迁移脚本管理表结构变更：
1. 创建表结构
2. 插入默认数据
3. 创建索引
4. 设置权限

### 监控指标

1. **业务指标**: 
   - 日活跃用户数
   - 习惯创建数量
   - 打卡完成率
   
2. **技术指标**:
   - API响应时间
   - 数据库查询性能
   - 错误率

3. **告警设置**:
   - API错误率超过阈值
   - 数据库连接异常
   - 系统资源使用率过高