# 需求文档

## 介绍

习惯模块是小脚步应用中管理儿童习惯养成的综合系统。它包含两个主要组件：用于管理习惯库（分类和模板）的后台管理系统，以及供用户创建、跟踪和分析孩子习惯的客户端系统。该模块支持习惯分类、可定制的习惯创建、打卡跟踪和统计分析，帮助家长培养孩子的良好习惯。

## 需求

### 需求 1

**用户故事：** 作为管理员，我希望管理层级习惯分类，以便为用户组织习惯库

#### 验收标准

1. 当管理员访问习惯分类管理时，系统应显示所有现有习惯分类的层级结构
2. 当管理员创建一级分类时，系统应要求提供分类名称和分类图标
3. 当管理员创建二级分类时，系统应要求提供分类名称、图标和父分类选择
4. 当管理员更新习惯分类时，系统应允许修改分类名称和图标，但不允许修改层级关系
5. 当管理员删除一级分类时，如果存在子分类或关联习惯，系统应阻止删除
6. 当管理员删除二级分类时，如果存在关联习惯，系统应阻止删除或要求确认
7. 当显示分类列表时，系统应支持树形结构展示和平铺列表展示两种模式

### 需求 2

**用户故事：** 作为管理员，我希望管理习惯模板，以便为用户提供预配置的习惯选择

#### 验收标准

1. 当管理员访问习惯管理时，系统应显示所有习惯模板及其属性
2. 当管理员创建习惯模板时，系统应要求习惯名称、图标、主题色、描述和分类分配
3. 当管理员更新习惯模板时，系统应允许修改所有习惯属性
4. 当管理员删除习惯模板时，系统应从习惯库中移除该模板
5. 当系统初始化时，系统应按需求规范创建默认习惯分类和模板

### 需求 3

**用户故事：** 作为管理员，我希望系统有预配置的层级习惯分类和模板，以便用户有一个全面的起始库

#### 验收标准

1. 当系统初始化时，系统应创建"生活自理"一级分类，下设"个人卫生"和"生活技能"二级分类
2. 当系统初始化时，"个人卫生"分类应包含习惯：睡前刷牙、饭前洗手
3. 当系统初始化时，"生活技能"分类应包含习惯：独立吃饭、自己穿鞋、上厕所
4. 当系统初始化时，系统应创建"健康习惯"一级分类，包含习惯：规律作息、户外活动
5. 当系统初始化时，系统应创建"学习认知"一级分类，包含习惯：亲子阅读、绘画、拼图/积木
6. 当系统初始化时，系统应创建"社交与情绪"一级分类，包含习惯：分享玩具
7. 当系统初始化时，系统应创建"整理与责任"一级分类，包含习惯：整理玩具
8. 当创建默认习惯时，每个习惯应有适当的描述和默认属性

### 需求 4

**用户故事：** 作为用户，我希望浏览层级习惯库，以便发现并为我的孩子添加预配置的习惯

#### 验收标准

1. 当用户访问习惯库时，系统应按层级分类显示习惯
2. 当用户查看一级分类时，系统应显示其下的二级分类列表
3. 当用户查看二级分类时，系统应显示该分类中所有可用的习惯模板
4. 当用户点击习惯模板时，系统应导航到创建习惯页面，并预填充模板属性
5. 当显示习惯模板时，系统应显示习惯名称、图标和描述
6. 当用户浏览分类时，系统应提供面包屑导航显示当前位置

### 需求 5

**用户故事：** 作为用户，我希望查看我创建的习惯，以便管理和跟踪我孩子的习惯进度

#### 验收标准

1. 当用户访问"我的习惯"时，系统应按层级分类显示所有用户创建的习惯
2. 当显示用户习惯时，系统应显示习惯名称、图标、当前状态和最近进度
3. 当用户点击习惯时，系统应导航到习惯详情页面
4. 当没有习惯存在时，系统应显示适当的空状态和指导
5. 当用户查看习惯列表时，系统应支持按一级分类或二级分类进行筛选

### 需求 6

**用户故事：** 作为用户，我希望创建自定义习惯，以便跟踪我孩子的特定行为

#### 验收标准

1. 当用户创建新习惯时，系统应要求习惯名称、图标、主题色和描述
2. 当创建习惯时，系统应允许设置打卡频率（多选工作日）
3. 当创建习惯时，系统应允许设置首选打卡时间
4. 当创建习惯时，系统应允许设置习惯状态（启用/归档）
5. 当创建习惯时，系统应允许设置奖励星星数量
6. 当习惯创建时，系统应将其与当前用户和选定的宝宝关联

### 需求 7

**用户故事：** 作为用户，我希望为我的孩子打卡习惯，以便记录他们的日常习惯完成情况

#### 验收标准

1. 当用户执行打卡时，系统应记录当前时间戳
2. 当打卡时，系统应允许添加可选的备注/说明
3. 当习惯被打卡时，系统应更新该习惯当天的完成状态
4. 当用户尝试为已完成的习惯打卡时，系统应防止同一天重复打卡
5. 如果用户尝试为过去日期打卡，系统应阻止补打卡（未来需求：需要积分）

### 需求 8

**用户故事：** 作为用户，我希望查看详细的习惯统计，以便监控我孩子的习惯养成进度

#### 验收标准

1. 当用户访问习惯详情时，系统应显示当月的打卡统计
2. 当查看习惯详情时，系统应显示日历视图，突出显示打卡日期
3. 当用户点击日历日期时，系统应导航到该习惯的打卡页面
4. 当显示统计时，系统应显示完成率和连续天数信息

### 需求 9

**用户故事：** 作为用户，我希望查看综合习惯统计，以便分析我孩子的整体习惯进度

#### 验收标准

1. 当用户访问统计页面时，系统应提供日、周、月视图选项
2. 当查看日统计时，系统应显示每个习惯的完成状态和视觉指示器
3. 当查看周统计时，系统应显示7天趋势图表，显示完成模式
4. 当查看月统计时，系统应显示日历热力图，颜色强度代表完成频率
5. 当显示统计时，系统应计算并显示整体完成率

### 需求 10

**用户故事：** 作为用户，我希望从首页访问习惯功能，以便快速导航到习惯相关功能

#### 验收标准

1. 当用户查看首页时，系统应在头部显示习惯库图标
2. 当点击习惯库图标时，系统应导航到习惯库页面
3. 当用户查看首页时，系统应显示今日打卡完成率
4. 当点击完成率时，系统应导航到统计页面
5. 当用户查看首页时，系统应显示今日打卡时间轴
6. 当点击时间轴项目时，系统应导航到打卡页面
