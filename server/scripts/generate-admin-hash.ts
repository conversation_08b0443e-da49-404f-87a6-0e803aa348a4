import * as bcrypt from 'bcrypt';

/**
 * 生成管理员密码的bcrypt哈希
 * 默认密码: admin123
 */
async function generateAdminHash(): Promise<void> {
  const password = 'admin123';
  const saltRounds = 10;
  
  try {
    const hash = await bcrypt.hash(password, saltRounds);
    console.log('Generated bcrypt hash for admin password:');
    console.log(hash);
    console.log('\nUse this hash in your migration file.');
    
    // 验证哈希是否正确
    const isValid = await bcrypt.compare(password, hash);
    console.log(`\nHash validation: ${isValid ? '✓ Valid' : '✗ Invalid'}`);
    
  } catch (error) {
    console.error('Error generating hash:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  generateAdminHash();
}

export { generateAdminHash };