import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

interface MigrationResult {
  file: string;
  success: boolean;
  error?: any;
}

async function validateEnvironment(): Promise<{ url: string; key: string }> {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  }

  return { url: supabaseUrl, key: supabaseKey };
}

async function testConnection(supabase: any): Promise<void> {
  console.log('Testing database connection...');
  const { error } = await supabase.from('information_schema.tables').select('table_name').limit(1);
  
  if (error) {
    throw new Error(`Database connection failed: ${error.message}`);
  }
  
  console.log('✓ Database connection successful');
}

async function getMigrationFiles(migrationsDir: string): Promise<string[]> {
  if (!fs.existsSync(migrationsDir)) {
    throw new Error(`Migrations directory not found: ${migrationsDir}`);
  }

  const files = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  if (files.length === 0) {
    throw new Error('No migration files found');
  }

  return files;
}

async function executeMigration(supabase: any, file: string, sql: string): Promise<void> {
  // 分割SQL语句以处理多个语句
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  for (const statement of statements) {
    if (statement.trim()) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
      if (error) {
        throw error;
      }
    }
  }
}

async function runMigrations(): Promise<void> {
  try {
    // 验证环境变量
    const { url, key } = await validateEnvironment();
    
    // 创建Supabase客户端
    const supabase = createClient(url, key);
    
    // 测试连接
    await testConnection(supabase);
    
    // 获取迁移文件
    const migrationsDir = path.join(__dirname, '../migrations');
    const files = await getMigrationFiles(migrationsDir);
    
    console.log(`Found ${files.length} migration files`);
    console.log('Running migrations...\n');
    
    const results: MigrationResult[] = [];
    
    // 执行迁移
    for (const file of files) {
      try {
        console.log(`Running migration: ${file}`);
        const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
        
        await executeMigration(supabase, file, sql);
        
        results.push({ file, success: true });
        console.log(`✓ Migration ${file} completed successfully\n`);
        
      } catch (error) {
        results.push({ file, success: false, error });
        console.error(`✗ Migration ${file} failed:`, error);
        throw new Error(`Migration ${file} failed: ${error.message}`);
      }
    }
    
    // 输出总结
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('Migration Summary:');
    console.log(`✓ Successful: ${successful}`);
    console.log(`✗ Failed: ${failed}`);
    console.log('\nAll migrations completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Migration process failed:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMigrations();
}

export { runMigrations, validateEnvironment, testConnection };