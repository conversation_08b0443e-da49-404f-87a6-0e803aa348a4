import { createClient } from '@supabase/supabase-js';
import * as bcrypt from 'bcrypt';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

interface ValidationResult {
  test: string;
  passed: boolean;
  error?: string;
}

class MigrationValidator {
  private supabase: any;
  private results: ValidationResult[] = [];

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  private addResult(test: string, passed: boolean, error?: string): void {
    this.results.push({ test, passed, error });
    const status = passed ? '✓' : '✗';
    const message = error ? ` - ${error}` : '';
    console.log(`${status} ${test}${message}`);
  }

  async validateTableExists(tableName: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_name', tableName)
        .eq('table_schema', 'public');

      if (error) throw error;
      return data && data.length > 0;
    } catch (error) {
      return false;
    }
  }

  async validateTableStructure(): Promise<void> {
    console.log('\n=== 验证表结构 ===');

    const tables = ['users', 'babies', 'family_members', 'invitations', 'admin_users'];
    
    for (const table of tables) {
      try {
        const exists = await this.validateTableExists(table);
        this.addResult(`表 ${table} 存在`, exists);
      } catch (error) {
        this.addResult(`表 ${table} 存在`, false, error.message);
      }
    }
  }

  async validateForeignKeys(): Promise<void> {
    console.log('\n=== 验证外键约束 ===');

    try {
      const { data, error } = await this.supabase
        .from('information_schema.table_constraints')
        .select('constraint_name, table_name')
        .eq('constraint_type', 'FOREIGN KEY');

      if (error) throw error;

      const expectedForeignKeys = [
        'fk_users_default_baby_id',
        'babies_owner_id_fkey',
        'family_members_baby_id_fkey',
        'family_members_user_id_fkey',
        'invitations_baby_id_fkey',
        'invitations_inviter_id_fkey'
      ];

      for (const fkName of expectedForeignKeys) {
        const exists = data.some(fk => fk.constraint_name === fkName);
        this.addResult(`外键约束 ${fkName}`, exists);
      }
    } catch (error) {
      this.addResult('外键约束验证', false, error.message);
    }
  }

  async validateIndexes(): Promise<void> {
    console.log('\n=== 验证索引 ===');

    try {
      const { data, error } = await this.supabase
        .from('pg_indexes')
        .select('indexname, tablename')
        .in('tablename', ['users', 'babies', 'family_members', 'invitations', 'admin_users']);

      if (error) throw error;

      const expectedIndexes = [
        'idx_users_openid',
        'idx_babies_owner_id',
        'idx_family_members_baby_id',
        'idx_family_members_user_id',
        'idx_invitations_code',
        'idx_admin_users_username'
      ];

      for (const indexName of expectedIndexes) {
        const exists = data.some(idx => idx.indexname === indexName);
        this.addResult(`索引 ${indexName}`, exists);
      }
    } catch (error) {
      this.addResult('索引验证', false, error.message);
    }
  }

  async validateCheckConstraints(): Promise<void> {
    console.log('\n=== 验证检查约束 ===');

    try {
      // 验证性别检查约束
      const { data: genderConstraints, error: genderError } = await this.supabase
        .from('information_schema.check_constraints')
        .select('constraint_name, check_clause')
        .like('check_clause', '%gender%');

      if (genderError) throw genderError;
      this.addResult('性别检查约束', genderConstraints.length > 0);

      // 验证角色检查约束
      const { data: roleConstraints, error: roleError } = await this.supabase
        .from('information_schema.check_constraints')
        .select('constraint_name, check_clause')
        .like('check_clause', '%role%');

      if (roleError) throw roleError;
      this.addResult('角色检查约束', roleConstraints.length > 0);

    } catch (error) {
      this.addResult('检查约束验证', false, error.message);
    }
  }

  async validateDefaultAdmin(): Promise<void> {
    console.log('\n=== 验证默认管理员 ===');

    try {
      const { data, error } = await this.supabase
        .from('admin_users')
        .select('*')
        .eq('username', 'admin')
        .single();

      if (error) {
        this.addResult('默认管理员存在', false, error.message);
        return;
      }

      this.addResult('默认管理员存在', true);
      this.addResult('管理员用户名正确', data.username === 'admin');
      this.addResult('管理员是超级管理员', data.is_super_admin === true);
      this.addResult('管理员账户激活', data.is_active === true);
      this.addResult('管理员全名设置', data.full_name === '系统管理员');

      // 验证密码哈希
      if (data.password_hash) {
        const isValidHash = data.password_hash.match(/^\$2[aby]\$\d{2}\$.{53}$/);
        this.addResult('密码哈希格式正确', !!isValidHash);

        try {
          const isValidPassword = await bcrypt.compare('admin123', data.password_hash);
          this.addResult('默认密码验证', isValidPassword);
        } catch (error) {
          this.addResult('默认密码验证', false, error.message);
        }
      } else {
        this.addResult('密码哈希存在', false);
      }

    } catch (error) {
      this.addResult('默认管理员验证', false, error.message);
    }
  }

  async validateDataIntegrity(): Promise<void> {
    console.log('\n=== 验证数据完整性 ===');

    try {
      // 验证管理员表中没有重复的用户名
      const { data: adminUsers, error: adminError } = await this.supabase
        .from('admin_users')
        .select('username')
        .eq('username', 'admin');

      if (adminError) throw adminError;
      this.addResult('管理员用户名唯一', adminUsers.length === 1);

      // 验证时间戳字段
      const { data: adminWithTimestamps, error: timestampError } = await this.supabase
        .from('admin_users')
        .select('created_at, updated_at')
        .eq('username', 'admin')
        .single();

      if (timestampError) throw timestampError;
      
      const createdAt = new Date(adminWithTimestamps.created_at);
      const updatedAt = new Date(adminWithTimestamps.updated_at);
      
      this.addResult('创建时间有效', !isNaN(createdAt.getTime()));
      this.addResult('更新时间有效', !isNaN(updatedAt.getTime()));
      this.addResult('时间戳逻辑正确', createdAt.getTime() <= updatedAt.getTime());

    } catch (error) {
      this.addResult('数据完整性验证', false, error.message);
    }
  }

  async runAllValidations(): Promise<void> {
    console.log('开始验证数据库迁移...\n');

    await this.validateTableStructure();
    await this.validateForeignKeys();
    await this.validateIndexes();
    await this.validateCheckConstraints();
    await this.validateDefaultAdmin();
    await this.validateDataIntegrity();

    this.printSummary();
  }

  private printSummary(): void {
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;

    console.log('\n=== 验证总结 ===');
    console.log(`总计: ${total}`);
    console.log(`通过: ${passed}`);
    console.log(`失败: ${failed}`);
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n失败的测试:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`  ✗ ${r.test}: ${r.error || '未知错误'}`));
    }

    if (failed === 0) {
      console.log('\n🎉 所有验证都通过了！数据库迁移成功。');
    } else {
      console.log('\n❌ 部分验证失败，请检查数据库迁移。');
      process.exit(1);
    }
  }
}

async function validateMigrations(): Promise<void> {
  try {
    const validator = new MigrationValidator();
    await validator.runAllValidations();
  } catch (error) {
    console.error('验证过程失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  validateMigrations();
}

export { MigrationValidator, validateMigrations };