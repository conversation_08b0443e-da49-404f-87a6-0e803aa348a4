# 数据库迁移系统

本目录包含小脚步应用的数据库迁移脚本，用于创建和初始化数据库结构。

## 迁移文件

迁移文件按照数字顺序执行：

1. **001_create_users_table.sql** - 创建用户表
2. **002_create_babies_table.sql** - 创建宝宝表和用户表外键约束
3. **003_create_family_members_table.sql** - 创建家庭成员表
4. **004_create_invitations_table.sql** - 创建邀请表
5. **005_create_admin_users_table.sql** - 创建管理员用户表
6. **006_insert_default_admin.sql** - 插入默认管理员账户

## 使用方法

### 运行迁移

```bash
# 运行所有迁移
yarn migrate

# 或使用 npm
npm run migrate
```

### 验证迁移

```bash
# 验证数据库结构和数据
yarn migrate:validate

# 或使用 npm
npm run migrate:validate
```

### 生成管理员密码哈希

```bash
# 生成新的管理员密码哈希
yarn admin:hash

# 或使用 npm
npm run admin:hash
```

## 环境配置

确保在 `.env` 文件中配置了以下环境变量：

```env
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 数据库结构

### 用户系统

- **users** - 客户端用户（微信小程序用户）
- **admin_users** - 后台管理员用户

### 宝宝管理

- **babies** - 宝宝档案信息
- **family_members** - 家庭成员关系
- **invitations** - 家庭邀请

### 关系说明

- 每个用户可以拥有多个宝宝档案（最多5个）
- 每个用户有一个默认宝宝（default_baby_id）
- 家庭成员可以共享宝宝档案的访问权限
- 邀请系统支持通过邀请码添加家庭成员

## 默认数据

### 默认管理员账户

- **用户名**: admin
- **密码**: admin123
- **权限**: 超级管理员
- **状态**: 激活

> ⚠️ **安全提醒**: 在生产环境中，请立即修改默认管理员密码！

## 测试

迁移系统包含完整的测试套件：

```bash
# 运行迁移相关测试
yarn test scripts/__tests__/migrations.spec.ts
yarn test scripts/__tests__/initial-data.spec.ts
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 Supabase URL 和 Service Role Key 是否正确
   - 确认网络连接正常

2. **权限错误**
   - 确保使用的是 Service Role Key，不是 anon key
   - 检查 Supabase 项目的 RLS 策略

3. **迁移失败**
   - 查看具体错误信息
   - 检查数据库中是否已存在相关表或约束
   - 使用 `yarn migrate:validate` 验证当前状态

### 重置数据库

如果需要重置数据库（⚠️ 谨慎操作）：

1. 在 Supabase Dashboard 中删除所有表
2. 重新运行迁移：`yarn migrate`
3. 验证结果：`yarn migrate:validate`

## 开发指南

### 添加新迁移

1. 创建新的 SQL 文件，使用递增的数字前缀
2. 在文件中添加必要的 SQL 语句
3. 更新本 README 文件
4. 添加相应的测试用例
5. 运行测试确保迁移正确

### 最佳实践

- 迁移文件应该是幂等的（可重复执行）
- 使用事务确保数据一致性
- 添加适当的索引提高查询性能
- 包含必要的约束确保数据完整性
- 为重要操作添加验证和错误处理

## 监控和维护

- 定期备份数据库
- 监控迁移执行时间
- 检查数据库性能指标
- 定期更新管理员密码
- 清理过期的邀请记录