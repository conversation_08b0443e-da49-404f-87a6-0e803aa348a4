-- 创建习惯模板表
CREATE TABLE habit_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category_id UUID NOT NULL REFERENCES habit_categories(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  theme_color VARCHAR(7) NOT NULL, -- HEX颜色值
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_habit_templates_category ON habit_templates(category_id, is_active);
CREATE INDEX idx_habit_templates_name ON habit_templates(name);
CREATE INDEX idx_habit_templates_active ON habit_templates(is_active);