-- 创建家庭成员表
CREATE TABLE family_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  baby_id UUID REFERENCES babies(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role INTEGER DEFAULT 3 CHECK (role IN (1, 2, 3)), -- 1: 爸爸, 2: 妈妈, 3: 其他照顾者
  is_owner BOOLEAN DEFAULT FALSE, -- 是否为宝宝档案的创建者
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(baby_id, user_id)
);

-- 创建索引
CREATE INDEX idx_family_members_baby_id ON family_members(baby_id);
CREATE INDEX idx_family_members_user_id ON family_members(user_id);
CREATE INDEX idx_family_members_is_owner ON family_members(is_owner);