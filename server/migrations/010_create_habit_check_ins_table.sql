-- 创建习惯打卡记录表
CREATE TABLE habit_check_ins (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  habit_id UUID NOT NULL REFERENCES user_habits(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
  check_in_date DATE NOT NULL,
  check_in_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 确保同一习惯同一天只能打卡一次
  UNIQUE(habit_id, check_in_date)
);

-- 创建索引
CREATE INDEX idx_habit_check_ins_habit_date ON habit_check_ins(habit_id, check_in_date DESC);
CREATE INDEX idx_habit_check_ins_user_date ON habit_check_ins(user_id, baby_id, check_in_date DESC);
CREATE INDEX idx_habit_check_ins_date ON habit_check_ins(check_in_date DESC);
CREATE INDEX idx_habit_check_ins_time ON habit_check_ins(check_in_time DESC);