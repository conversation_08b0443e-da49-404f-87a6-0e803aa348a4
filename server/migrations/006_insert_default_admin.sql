-- 插入默认管理员账户
-- 用户名: admin
-- 密码: admin123 (使用bcrypt加密)
INSERT INTO admin_users (username, password_hash, full_name, is_super_admin, is_active) 
VALUES (
  'admin', 
  '$2b$10$p.dxE6F9IwPPicBA0TpjBeSVcHFrLdjHasCGOOl/tDzIg0Vuv2RH6', -- bcrypt hash for 'admin123'
  '系统管理员', 
  true,
  true
) ON CONFLICT (username) DO NOTHING;

-- 验证插入结果
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM admin_users WHERE username = 'admin') THEN
    RAISE NOTICE '✓ 默认管理员账户创建成功';
  ELSE
    RAISE EXCEPTION '✗ 默认管理员账户创建失败';
  END IF;
END $$;