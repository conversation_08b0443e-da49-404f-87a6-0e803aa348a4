-- 创建用户习惯表
CREATE TABLE user_habits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  baby_id UUID NOT NULL REFERENCES babies(id) ON DELETE CASCADE,
  template_id UUID REFERENCES habit_templates(id) ON DELETE SET NULL,
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  theme_color VARCHAR(7) NOT NULL,
  description TEXT,
  frequency INTEGER[] NOT NULL, -- 数组存储1-7的数字
  preferred_time TIME,
  reward_stars INTEGER DEFAULT 1 CHECK (reward_stars >= 1 AND reward_stars <= 5),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_user_habits_user_baby ON user_habits(user_id, baby_id, status);
CREATE INDEX idx_user_habits_template ON user_habits(template_id);
CREATE INDEX idx_user_habits_status ON user_habits(status);
CREATE INDEX idx_user_habits_frequency ON user_habits USING GIN(frequency);