-- 创建习惯分类表（支持两级层级结构）
CREATE TABLE habit_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  parent_id UUID REFERENCES habit_categories(id) ON DELETE CASCADE,
  level INTEGER NOT NULL DEFAULT 1,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加约束确保层级结构的一致性
ALTER TABLE habit_categories 
ADD CONSTRAINT check_parent_level CHECK (
  (level = 1 AND parent_id IS NULL) OR 
  (level = 2 AND parent_id IS NOT NULL)
);

-- 添加层级检查约束
ALTER TABLE habit_categories 
ADD CONSTRAINT check_level_range CHECK (level IN (1, 2));

-- 创建索引
CREATE INDEX idx_habit_categories_active ON habit_categories(is_active, level, sort_order);
CREATE INDEX idx_habit_categories_name ON habit_categories(name);
CREATE INDEX idx_habit_categories_parent ON habit_categories(parent_id, level, sort_order);
CREATE INDEX idx_habit_categories_level ON habit_categories(level, is_active, sort_order);

-- 添加字段注释
COMMENT ON COLUMN habit_categories.parent_id IS '父分类ID，NULL表示一级分类';
COMMENT ON COLUMN habit_categories.level IS '分类层级：1表示一级分类，2表示二级分类';