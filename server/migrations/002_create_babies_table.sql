-- 创建宝宝表
CREATE TABLE babies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  nickname VARCHAR(50) NOT NULL,
  birth_date DATE NOT NULL,
  gender INTEGER NOT NULL CHECK (gender IN (1, 2)), -- 1: 男, 2: 女
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_babies_owner_id ON babies(owner_id);
CREATE INDEX idx_babies_birth_date ON babies(birth_date);

-- 添加用户表的外键约束
ALTER TABLE users ADD CONSTRAINT fk_users_default_baby_id 
  FOREIGN KEY (default_baby_id) REFERENCES babies(id) ON DELETE SET NULL;