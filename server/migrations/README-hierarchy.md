# 习惯分类层级结构迁移说明

## 概述

本迁移将现有的平面习惯分类结构升级为支持两级层级的结构，以满足产品需求中的分类组织要求。

## 迁移文件

### 1. 结构迁移
**文件**: `012_add_hierarchy_to_habit_categories.sql`

**功能**:
- 添加 `parent_id` 字段（UUID，外键引用自身）
- 添加 `level` 字段（INTEGER，1=一级分类，2=二级分类）
- 添加约束确保层级结构一致性
- 创建相关索引优化查询性能
- 更新现有数据为一级分类

### 2. 数据迁移
**文件**: `013_insert_hierarchical_habit_data.sql`

**功能**:
- 插入默认的层级分类结构
- 插入对应的习惯模板数据
- 清理可能存在的冲突数据

## 默认分类结构

```
生活自理 (一级)
├── 个人卫生 (二级)
│   ├── 睡前刷牙
│   └── 饭前洗手
└── 生活技能 (二级)
    ├── 独立吃饭
    ├── 自己穿鞋
    └── 上厕所

健康习惯 (一级)
├── 规律作息
└── 户外活动

学习认知 (一级)
├── 亲子阅读
├── 绘画
└── 拼图/积木

社交与情绪 (一级)
└── 分享玩具

整理与责任 (一级)
└── 整理玩具
```

## 执行步骤

### 方法一：使用脚本（推荐）

1. **测试数据库连接**:
   ```bash
   yarn test:migration
   ```

2. **执行迁移**:
   ```bash
   yarn migrate:hierarchy
   ```

3. **验证结果**:
   ```bash
   yarn migrate:hierarchy:validate
   ```

### 方法二：手动执行SQL

1. **执行结构迁移**:
   ```sql
   \i migrations/012_add_hierarchy_to_habit_categories.sql
   ```

2. **执行数据迁移**:
   ```sql
   \i migrations/013_insert_hierarchical_habit_data.sql
   ```

3. **验证结果**:
   ```sql
   SELECT 
     c1.name as "一级分类",
     c2.name as "二级分类",
     t.name as "习惯模板"
   FROM habit_categories c1
   LEFT JOIN habit_categories c2 ON c2.parent_id = c1.id
   LEFT JOIN habit_templates t ON t.category_id = COALESCE(c2.id, c1.id)
   WHERE c1.level = 1
   ORDER BY c1.sort_order, c2.sort_order, t.name;
   ```

## 验证检查项

迁移完成后，系统会自动验证以下内容：

1. **表结构验证**
   - ✅ `parent_id` 字段存在且类型正确
   - ✅ `level` 字段存在且类型正确
   - ✅ 约束条件正确创建

2. **层级结构验证**
   - ✅ 一级分类的 `parent_id` 为 NULL
   - ✅ 二级分类的 `parent_id` 指向有效的一级分类
   - ✅ 层级关系一致性

3. **数据完整性验证**
   - ✅ 没有孤立的模板（category_id 无效）
   - ✅ 没有孤立的二级分类（parent_id 无效）
   - ✅ 所有期望的分类和模板都存在

4. **索引验证**
   - ✅ 层级查询相关索引已创建
   - ✅ 性能优化索引正常工作

## 回滚方案

如果迁移出现问题，可以执行回滚：

```bash
yarn migrate:hierarchy:rollback
```

**警告**: 回滚会删除所有层级相关的数据和结构！

## 注意事项

1. **备份数据**: 迁移前请备份数据库
2. **测试环境**: 建议先在测试环境验证
3. **停机时间**: 迁移过程可能需要短暂停机
4. **现有数据**: 现有的用户习惯不会受到影响
5. **API兼容性**: 新的API支持层级查询，旧的查询方式仍然兼容

## 故障排除

### 常见问题

1. **字段已存在错误**
   - 检查是否已经执行过迁移
   - 使用 `yarn test:migration` 检查当前状态

2. **约束违反错误**
   - 检查现有数据是否有不一致的地方
   - 手动清理问题数据后重新执行

3. **外键约束错误**
   - 确保 `parent_id` 引用的分类确实存在
   - 检查数据插入顺序（先插入一级分类）

### 检查命令

```bash
# 检查表结构
yarn test:migration

# 检查数据状态
yarn migrate:hierarchy:validate

# 测试层级功能
yarn test:hierarchy
```

## 相关文档

- [习惯模块设计文档](../docs/habits-module/design.md)
- [层级迁移指南](../docs/hierarchy-migration-guide.md)
- [API文档](../docs/api-documentation.md)