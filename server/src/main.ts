import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT') || 3000;
    const nodeEnv = configService.get<string>('NODE_ENV') || 'development';

    // 启用 CORS（如果需要）
    app.enableCors({
      origin: true,
      credentials: true,
    });

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        disableErrorMessages: nodeEnv === 'production',
      }),
    );

    // 设置全局路由前缀
    app.setGlobalPrefix('api', {
      exclude: ['/'],
    });

    // 启动应用
    await app.listen(port);

    logger.log(`🚀 Application is running on: http://localhost:${port}/api`);
    logger.log(`📝 Environment: ${nodeEnv}`);
    logger.log(`🔧 Global prefix: /api`);

    // 优雅关闭处理
    process.on('SIGTERM', () => {
      logger.log('SIGTERM received, shutting down gracefully');
      void app.close().then(() => process.exit(0));
    });

    process.on('SIGINT', () => {
      logger.log('SIGINT received, shutting down gracefully');
      void app.close().then(() => process.exit(0));
    });
  } catch (error) {
    logger.error('Failed to start application', error);
    process.exit(1);
  }
}

void bootstrap();
