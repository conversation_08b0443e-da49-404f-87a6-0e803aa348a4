#!/usr/bin/env ts-node

/**
 * 习惯分类层级结构迁移脚本
 *
 * 此脚本用于将现有的习惯分类数据迁移到新的层级结构
 * 执行步骤：
 * 1. 添加新字段（parent_id, level）
 * 2. 将现有分类设置为一级分类
 * 3. 根据产品需求重新组织分类结构
 * 4. 更新相关的习惯模板关联
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SupabaseService } from '../database/supabase.service';
import { Logger } from '@nestjs/common';

interface CategoryMigrationData {
  id: string;
  name: string;
  icon: string;
  parent_id?: string;
  level: number;
  sort_order: number;
  is_active: boolean;
}

interface TemplateMigrationData {
  id: string;
  category_id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  is_active: boolean;
}

class HabitCategoryMigration {
  private readonly logger = new Logger(HabitCategoryMigration.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * 执行完整的迁移流程
   */
  async migrate(): Promise<void> {
    try {
      this.logger.log('开始习惯分类层级结构迁移...');

      // 1. 检查表结构
      await this.checkTableStructure();

      // 2. 备份现有数据
      await this.backupExistingData();

      // 3. 检查层级结构
      const hasHierarchy = await this.checkHierarchyStructure();

      if (!hasHierarchy) {
        throw new Error('层级结构不存在，请先执行基础迁移');
      }

      // 4. 检查并补充数据
      await this.ensureHierarchicalData();

      // 5. 验证迁移结果
      await this.validateMigration();

      this.logger.log('习惯分类层级结构迁移完成！');
    } catch (error) {
      this.logger.error('迁移过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 检查表结构
   */
  private async checkTableStructure(): Promise<void> {
    this.logger.log('检查表结构...');

    const { data, error } = await this.supabaseService
      .query('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'habit_categories')
      .eq('table_schema', 'public');

    if (error) {
      throw new Error(`检查表结构失败: ${error.message}`);
    }

    const columns = data?.map((row) => row.column_name) || [];
    this.logger.log(`现有字段: ${columns.join(', ')}`);

    const hasParentId = columns.includes('parent_id');
    const hasLevel = columns.includes('level');

    if (!hasParentId || !hasLevel) {
      this.logger.warn('表结构需要更新，将添加缺失的字段');
    }
  }

  /**
   * 备份现有数据
   */
  private async backupExistingData(): Promise<void> {
    this.logger.log('备份现有数据...');

    // 创建备份表
    const backupTableName = `habit_categories_backup_${Date.now()}`;

    try {
      // Create backup using a simple approach - we'll skip backup for now
      this.logger.log(`备份表名: ${backupTableName} (跳过实际备份)`);
      this.logger.log('备份操作已跳过，如需备份请手动执行');
    } catch (error) {
      this.logger.warn(`创建备份表失败: ${error.message}`);
    }
  }

  /**
   * 检查层级结构
   */
  private async checkHierarchyStructure(): Promise<boolean> {
    this.logger.log('检查层级结构...');

    try {
      // 检查字段是否已存在
      const { error } = await this.supabaseService
        .getClient()
        .from('habit_categories')
        .select('parent_id, level')
        .limit(1);

      if (!error) {
        this.logger.log('✅ 层级字段已存在');
        return true;
      } else {
        this.logger.warn('⚠️  层级字段不存在，请先执行基础迁移');
        this.logger.log(
          '请执行: migrations/007_create_habit_categories_table.sql',
        );
        this.logger.log(
          '然后执行: migrations/011_insert_default_habit_data.sql',
        );
        return false;
      }
    } catch (error) {
      this.logger.warn(`层级结构检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 确保层级数据存在
   */
  private async ensureHierarchicalData(): Promise<void> {
    this.logger.log('检查并补充层级数据...');

    try {
      // 检查是否已有数据
      const { data: existingCategories } = await this.supabaseService
        .query('habit_categories')
        .select('id, name')
        .eq('is_active', true);

      if (existingCategories && existingCategories.length > 0) {
        this.logger.log(
          `已存在 ${existingCategories.length} 个分类，跳过数据插入`,
        );
        return;
      }

      // 如果没有数据，提示用户执行基础迁移
      this.logger.warn('没有找到分类数据，请执行基础迁移:');
      this.logger.log('migrations/011_insert_default_habit_data.sql');
    } catch (error) {
      this.logger.error(`数据检查失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  private async validateMigration(): Promise<void> {
    this.logger.log('验证迁移结果...');

    try {
      // 使用专门的验证脚本
      const {
        HierarchyMigrationValidator,
      } = require('./validate-hierarchy-migration');
      const validator = new HierarchyMigrationValidator(this.supabaseService);

      const success = await validator.validate();

      if (!success) {
        throw new Error('迁移验证失败，请检查详细日志');
      }

      this.logger.log('迁移验证完成');
    } catch (error) {
      this.logger.error(`验证失败: ${error.message}`);
      throw error;
    }
  }
}

/**
 * 主执行函数
 */
async function main() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);

  const migration = new HabitCategoryMigration(supabaseService);

  try {
    await migration.migrate();
    console.log('✅ 迁移成功完成');
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { HabitCategoryMigration };
