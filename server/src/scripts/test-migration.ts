#!/usr/bin/env ts-node

/**
 * 简单的迁移测试脚本
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SupabaseService } from '../database/supabase.service';
import { Logger } from '@nestjs/common';

async function testMigration() {
  const logger = new Logger('TestMigration');
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const supabaseService = app.get(SupabaseService);

    logger.log('🧪 开始测试数据库连接和基本功能...');

    // 1. 测试数据库连接
    const isConnected = await supabaseService.testConnection();
    if (!isConnected) {
      throw new Error('数据库连接失败');
    }
    logger.log('✅ 数据库连接正常');

    // 2. 检查表是否存在
    const { data: tables } = await supabaseService
      .getClient()
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['habit_categories', 'habit_templates']);

    const tableNames = tables?.map((t) => t.table_name) || [];
    logger.log(`找到表: ${tableNames.join(', ')}`);

    if (!tableNames.includes('habit_categories')) {
      throw new Error('habit_categories 表不存在');
    }

    if (!tableNames.includes('habit_templates')) {
      throw new Error('habit_templates 表不存在');
    }

    // 3. 检查字段结构
    const { data: columns } = await supabaseService
      .getClient()
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'habit_categories')
      .eq('table_schema', 'public');

    const columnNames = columns?.map((c) => c.column_name) || [];
    logger.log(`habit_categories 字段: ${columnNames.join(', ')}`);

    const hasParentId = columnNames.includes('parent_id');
    const hasLevel = columnNames.includes('level');

    if (hasParentId && hasLevel) {
      logger.log('✅ 层级字段已存在');
    } else {
      logger.warn('⚠️  层级字段不存在，需要执行结构迁移');
    }

    // 4. 检查现有数据
    const { data: categories } = await supabaseService
      .query('habit_categories')
      .select('id, name, level, parent_id')
      .eq('is_active', true);

    logger.log(`当前分类数量: ${categories?.length || 0}`);

    if (categories && categories.length > 0) {
      const level1Count = categories.filter((c) => c.level === 1).length;
      const level2Count = categories.filter((c) => c.level === 2).length;
      logger.log(`一级分类: ${level1Count}, 二级分类: ${level2Count}`);
    }

    const { data: templates } = await supabaseService
      .query('habit_templates')
      .select('id, name')
      .eq('is_active', true);

    logger.log(`当前模板数量: ${templates?.length || 0}`);

    logger.log('✅ 测试完成！');
  } catch (error) {
    logger.error('❌ 测试失败:', error);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testMigration().catch(console.error);
}

export { testMigration };
