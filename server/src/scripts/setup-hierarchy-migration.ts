#!/usr/bin/env ts-node

/**
 * 层级迁移设置脚本
 *
 * 此脚本帮助用户准备和执行层级分类迁移
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SupabaseService } from '../database/supabase.service';
import { Logger } from '@nestjs/common';

class HierarchyMigrationSetup {
  private readonly logger = new Logger(HierarchyMigrationSetup.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * 检查迁移准备状态
   */
  async checkMigrationReadiness(): Promise<void> {
    try {
      this.logger.log('🔍 检查层级迁移准备状态...');

      // 1. 测试数据库连接
      const isConnected = await this.testDatabaseConnection();
      if (!isConnected) {
        this.logger.error('❌ 数据库连接失败');
        this.printConnectionHelp();
        return;
      }

      // 2. 检查基础表是否存在
      const tablesExist = await this.checkBaseTables();
      if (!tablesExist) {
        this.logger.error('❌ 基础表不存在');
        this.printBaseMigrationHelp();
        return;
      }

      // 3. 检查层级字段是否存在
      const hierarchyExists = await this.checkHierarchyFields();
      if (hierarchyExists) {
        this.logger.log('✅ 层级字段已存在');
        this.printDataMigrationHelp();
      } else {
        this.logger.log('⚠️  层级字段不存在');
        this.printStructureMigrationHelp();
      }

      // 4. 检查数据状态
      await this.checkDataStatus();
    } catch (error) {
      this.logger.error('检查过程中发生错误:', error);
    }
  }

  /**
   * 测试数据库连接
   */
  private async testDatabaseConnection(): Promise<boolean> {
    try {
      const isConnected = await this.supabaseService.testConnection();
      if (isConnected) {
        this.logger.log('✅ 数据库连接正常');
        return true;
      } else {
        this.logger.error('❌ 数据库连接失败');
        return false;
      }
    } catch (error) {
      this.logger.error('❌ 数据库连接测试失败:', error.message);
      return false;
    }
  }

  /**
   * 检查基础表是否存在
   */
  private async checkBaseTables(): Promise<boolean> {
    try {
      const requiredTables = ['habit_categories', 'habit_templates'];

      for (const tableName of requiredTables) {
        const { data, error } = await this.supabaseService
          .getClient()
          .from(tableName)
          .select('count', { count: 'exact', head: true });

        if (error) {
          this.logger.error(
            `❌ 表 ${tableName} 不存在或无法访问: ${error.message}`,
          );
          return false;
        }
      }

      this.logger.log('✅ 基础表存在');
      return true;
    } catch (error) {
      this.logger.error('检查基础表失败:', error.message);
      return false;
    }
  }

  /**
   * 检查层级字段是否存在
   */
  private async checkHierarchyFields(): Promise<boolean> {
    try {
      const { data: columns } = await this.supabaseService
        .getClient()
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'habit_categories')
        .eq('table_schema', 'public');

      const columnNames = columns?.map((c) => c.column_name) || [];
      const hasParentId = columnNames.includes('parent_id');
      const hasLevel = columnNames.includes('level');

      return hasParentId && hasLevel;
    } catch (error) {
      this.logger.error('检查层级字段失败:', error.message);
      return false;
    }
  }

  /**
   * 检查数据状态
   */
  private async checkDataStatus(): Promise<void> {
    try {
      const { data: categories } = await this.supabaseService
        .query('habit_categories')
        .select('id, name, level, parent_id')
        .eq('is_active', true);

      const { data: templates } = await this.supabaseService
        .query('habit_templates')
        .select('id, name')
        .eq('is_active', true);

      this.logger.log(`📊 当前数据状态:`);
      this.logger.log(`   分类数量: ${categories?.length || 0}`);
      this.logger.log(`   模板数量: ${templates?.length || 0}`);

      if (categories && categories.length > 0) {
        const level1Count = categories.filter((c) => c.level === 1).length;
        const level2Count = categories.filter((c) => c.level === 2).length;
        this.logger.log(`   一级分类: ${level1Count}`);
        this.logger.log(`   二级分类: ${level2Count}`);
      }
    } catch (error) {
      this.logger.warn('检查数据状态失败:', error.message);
    }
  }

  /**
   * 打印连接帮助信息
   */
  private printConnectionHelp(): void {
    this.logger.log(`
📋 数据库连接问题解决方案:

1. 检查环境变量配置:
   - SUPABASE_URL
   - SUPABASE_SERVICE_KEY

2. 确认 Supabase 项目状态:
   - 项目是否正常运行
   - 网络连接是否正常

3. 检查配置文件:
   - .env 文件是否存在
   - 配置值是否正确
    `);
  }

  /**
   * 打印基础迁移帮助信息
   */
  private printBaseMigrationHelp(): void {
    this.logger.log(`
📋 基础表迁移指南:

需要先执行基础迁移创建 habit_categories 和 habit_templates 表:

1. 手动执行 SQL 文件:
   psql -d your_database -f migrations/007_create_habit_categories_table.sql
   psql -d your_database -f migrations/008_create_habit_templates_table.sql

2. 或者在 Supabase Dashboard 中执行:
   - 打开 SQL Editor
   - 复制并执行 migrations/007_create_habit_categories_table.sql
   - 复制并执行 migrations/008_create_habit_templates_table.sql

3. 执行完成后重新运行此脚本检查状态
    `);
  }

  /**
   * 打印结构迁移帮助信息
   */
  private printStructureMigrationHelp(): void {
    this.logger.log(`
📋 层级结构迁移指南:

需要执行基础迁移创建层级结构:

1. 手动执行 SQL 文件:
   psql -d your_database -f migrations/007_create_habit_categories_table.sql
   psql -d your_database -f migrations/008_create_habit_templates_table.sql
   psql -d your_database -f migrations/011_insert_default_habit_data.sql

2. 或者在 Supabase Dashboard 中执行:
   - 打开 SQL Editor
   - 依次复制并执行上述三个文件的内容

3. 执行完成后验证结果:
   yarn migrate:hierarchy:validate
    `);
  }

  /**
   * 打印数据迁移帮助信息
   */
  private printDataMigrationHelp(): void {
    this.logger.log(`
📋 数据迁移指南:

层级字段已存在，数据迁移已完成:

1. 验证迁移结果:
   yarn migrate:hierarchy:validate

2. 测试层级功能:
   yarn test:hierarchy

3. 查看迁移指南:
   docs/habit-migration-simple-guide.md
    `);
  }
}

/**
 * 主执行函数
 */
async function main() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);

  const setup = new HierarchyMigrationSetup(supabaseService);

  try {
    await setup.checkMigrationReadiness();
  } catch (error) {
    console.error('❌ 设置检查失败:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { HierarchyMigrationSetup };
