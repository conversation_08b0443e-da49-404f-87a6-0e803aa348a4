import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

describe('Database Migrations', () => {
  let supabase: any;

  beforeAll(() => {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration for tests');
    }

    supabase = createClient(supabaseUrl, supabaseKey);
  });

  describe('Table Structure Validation', () => {
    it('should have users table with correct structure', async () => {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'users')
        .order('ordinal_position');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      const expectedColumns = [
        { column_name: 'id', data_type: 'uuid', is_nullable: 'NO' },
        {
          column_name: 'openid',
          data_type: 'character varying',
          is_nullable: 'NO',
        },
        {
          column_name: 'username',
          data_type: 'character varying',
          is_nullable: 'YES',
        },
        {
          column_name: 'phone',
          data_type: 'character varying',
          is_nullable: 'YES',
        },
        { column_name: 'avatar_url', data_type: 'text', is_nullable: 'YES' },
        {
          column_name: 'default_baby_id',
          data_type: 'uuid',
          is_nullable: 'YES',
        },
        {
          column_name: 'created_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
        {
          column_name: 'updated_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
      ];

      expectedColumns.forEach((expectedCol) => {
        const actualCol = data.find(
          (col) => col.column_name === expectedCol.column_name,
        );
        expect(actualCol).toBeDefined();
        expect(actualCol.data_type).toBe(expectedCol.data_type);
        expect(actualCol.is_nullable).toBe(expectedCol.is_nullable);
      });
    });

    it('should have babies table with correct structure', async () => {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'babies')
        .order('ordinal_position');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      const expectedColumns = [
        { column_name: 'id', data_type: 'uuid', is_nullable: 'NO' },
        { column_name: 'owner_id', data_type: 'uuid', is_nullable: 'YES' },
        {
          column_name: 'nickname',
          data_type: 'character varying',
          is_nullable: 'NO',
        },
        { column_name: 'birth_date', data_type: 'date', is_nullable: 'NO' },
        { column_name: 'gender', data_type: 'integer', is_nullable: 'NO' },
        { column_name: 'avatar_url', data_type: 'text', is_nullable: 'YES' },
        {
          column_name: 'created_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
        {
          column_name: 'updated_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
      ];

      expectedColumns.forEach((expectedCol) => {
        const actualCol = data.find(
          (col) => col.column_name === expectedCol.column_name,
        );
        expect(actualCol).toBeDefined();
        expect(actualCol.data_type).toBe(expectedCol.data_type);
        expect(actualCol.is_nullable).toBe(expectedCol.is_nullable);
      });
    });

    it('should have family_members table with correct structure', async () => {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'family_members')
        .order('ordinal_position');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      const expectedColumns = [
        { column_name: 'id', data_type: 'uuid', is_nullable: 'NO' },
        { column_name: 'baby_id', data_type: 'uuid', is_nullable: 'YES' },
        { column_name: 'user_id', data_type: 'uuid', is_nullable: 'YES' },
        { column_name: 'role', data_type: 'integer', is_nullable: 'YES' },
        { column_name: 'is_owner', data_type: 'boolean', is_nullable: 'YES' },
        {
          column_name: 'created_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
      ];

      expectedColumns.forEach((expectedCol) => {
        const actualCol = data.find(
          (col) => col.column_name === expectedCol.column_name,
        );
        expect(actualCol).toBeDefined();
        expect(actualCol.data_type).toBe(expectedCol.data_type);
        expect(actualCol.is_nullable).toBe(expectedCol.is_nullable);
      });
    });

    it('should have invitations table with correct structure', async () => {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'invitations')
        .order('ordinal_position');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      const expectedColumns = [
        { column_name: 'id', data_type: 'uuid', is_nullable: 'NO' },
        { column_name: 'baby_id', data_type: 'uuid', is_nullable: 'YES' },
        { column_name: 'inviter_id', data_type: 'uuid', is_nullable: 'YES' },
        {
          column_name: 'invitation_code',
          data_type: 'character varying',
          is_nullable: 'NO',
        },
        {
          column_name: 'phone',
          data_type: 'character varying',
          is_nullable: 'YES',
        },
        {
          column_name: 'expires_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'NO',
        },
        {
          column_name: 'used_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
        { column_name: 'used_by', data_type: 'uuid', is_nullable: 'YES' },
        {
          column_name: 'created_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
      ];

      expectedColumns.forEach((expectedCol) => {
        const actualCol = data.find(
          (col) => col.column_name === expectedCol.column_name,
        );
        expect(actualCol).toBeDefined();
        expect(actualCol.data_type).toBe(expectedCol.data_type);
        expect(actualCol.is_nullable).toBe(expectedCol.is_nullable);
      });
    });

    it('should have admin_users table with correct structure', async () => {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'admin_users')
        .order('ordinal_position');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      const expectedColumns = [
        { column_name: 'id', data_type: 'uuid', is_nullable: 'NO' },
        {
          column_name: 'username',
          data_type: 'character varying',
          is_nullable: 'NO',
        },
        {
          column_name: 'password_hash',
          data_type: 'character varying',
          is_nullable: 'NO',
        },
        {
          column_name: 'email',
          data_type: 'character varying',
          is_nullable: 'YES',
        },
        {
          column_name: 'full_name',
          data_type: 'character varying',
          is_nullable: 'YES',
        },
        { column_name: 'is_active', data_type: 'boolean', is_nullable: 'YES' },
        {
          column_name: 'is_super_admin',
          data_type: 'boolean',
          is_nullable: 'YES',
        },
        {
          column_name: 'last_login_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
        {
          column_name: 'created_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
        {
          column_name: 'updated_at',
          data_type: 'timestamp with time zone',
          is_nullable: 'YES',
        },
      ];

      expectedColumns.forEach((expectedCol) => {
        const actualCol = data.find(
          (col) => col.column_name === expectedCol.column_name,
        );
        expect(actualCol).toBeDefined();
        expect(actualCol.data_type).toBe(expectedCol.data_type);
        expect(actualCol.is_nullable).toBe(expectedCol.is_nullable);
      });
    });
  });

  describe('Foreign Key Constraints', () => {
    it('should have correct foreign key constraints', async () => {
      const { data, error } = await supabase
        .from('information_schema.table_constraints')
        .select('constraint_name, table_name, constraint_type')
        .eq('constraint_type', 'FOREIGN KEY');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      // 验证主要的外键约束存在
      const expectedConstraints = [
        'fk_users_default_baby_id',
        'babies_owner_id_fkey',
        'family_members_baby_id_fkey',
        'family_members_user_id_fkey',
        'invitations_baby_id_fkey',
        'invitations_inviter_id_fkey',
        'invitations_used_by_fkey',
      ];

      expectedConstraints.forEach((constraintName) => {
        const constraint = data.find(
          (c) => c.constraint_name === constraintName,
        );
        expect(constraint).toBeDefined();
      });
    });
  });

  describe('Unique Constraints', () => {
    it('should have correct unique constraints', async () => {
      const { data, error } = await supabase
        .from('information_schema.table_constraints')
        .select('constraint_name, table_name, constraint_type')
        .eq('constraint_type', 'UNIQUE');

      expect(error).toBeNull();
      expect(data).toBeDefined();

      // 验证唯一约束
      const uniqueConstraints = data.filter(
        (c) =>
          c.table_name === 'users' ||
          c.table_name === 'family_members' ||
          c.table_name === 'invitations' ||
          c.table_name === 'admin_users',
      );

      expect(uniqueConstraints.length).toBeGreaterThan(0);
    });
  });

  describe('Check Constraints', () => {
    it('should have gender check constraint on babies table', async () => {
      const { data, error } = await supabase
        .from('information_schema.check_constraints')
        .select('constraint_name, check_clause')
        .like('check_clause', '%gender%');

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.length).toBeGreaterThan(0);
    });

    it('should have role check constraint on family_members table', async () => {
      const { data, error } = await supabase
        .from('information_schema.check_constraints')
        .select('constraint_name, check_clause')
        .like('check_clause', '%role%');

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.length).toBeGreaterThan(0);
    });
  });

  describe('Indexes', () => {
    it('should have required indexes', async () => {
      const { data, error } = await supabase
        .from('pg_indexes')
        .select('indexname, tablename')
        .in('tablename', [
          'users',
          'babies',
          'family_members',
          'invitations',
          'admin_users',
        ]);

      expect(error).toBeNull();
      expect(data).toBeDefined();

      // 验证关键索引存在
      const expectedIndexes = [
        'idx_users_openid',
        'idx_babies_owner_id',
        'idx_family_members_baby_id',
        'idx_family_members_user_id',
        'idx_invitations_code',
        'idx_admin_users_username',
      ];

      expectedIndexes.forEach((indexName) => {
        const index = data.find((i) => i.indexname === indexName);
        expect(index).toBeDefined();
      });
    });
  });
});
