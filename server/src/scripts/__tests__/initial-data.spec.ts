import { createClient } from '@supabase/supabase-js';
import * as bcrypt from 'bcrypt';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

describe('Initial Data Migration', () => {
  let supabase: any;

  beforeAll(() => {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration for tests');
    }

    supabase = createClient(supabaseUrl, supabaseKey);
  });

  describe('Default Admin User', () => {
    it('should have default admin user created', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('username', 'admin')
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.username).toBe('admin');
      expect(data.full_name).toBe('系统管理员');
      expect(data.is_super_admin).toBe(true);
      expect(data.is_active).toBe(true);
      expect(data.password_hash).toBeDefined();
      expect(data.created_at).toBeDefined();
    });

    it('should have correct password hash for default admin', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('password_hash')
        .eq('username', 'admin')
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.password_hash).toBeDefined();

      // 验证密码哈希是否正确
      const isValidPassword = await bcrypt.compare(
        'admin123',
        data.password_hash,
      );
      expect(isValidPassword).toBe(true);
    });

    it('should not create duplicate admin users', async () => {
      // 尝试再次插入相同的管理员用户
      const { error } = await supabase.from('admin_users').insert({
        username: 'admin',
        password_hash: '$2b$10$test',
        full_name: 'Test Admin',
        is_super_admin: false,
      });

      // 应该因为唯一约束而失败
      expect(error).toBeDefined();
      expect(error.code).toBe('23505'); // PostgreSQL unique violation error code
    });

    it('should have only one admin user with username "admin"', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('id')
        .eq('username', 'admin');

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.length).toBe(1);
    });
  });

  describe('Data Integrity', () => {
    it('should have valid timestamps for default admin', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('created_at, updated_at')
        .eq('username', 'admin')
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.created_at).toBeDefined();
      expect(data.updated_at).toBeDefined();

      const createdAt = new Date(data.created_at);
      const updatedAt = new Date(data.updated_at);

      expect(createdAt).toBeInstanceOf(Date);
      expect(updatedAt).toBeInstanceOf(Date);
      expect(createdAt.getTime()).toBeLessThanOrEqual(updatedAt.getTime());
    });

    it('should have valid boolean fields for default admin', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('is_active, is_super_admin')
        .eq('username', 'admin')
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(typeof data.is_active).toBe('boolean');
      expect(typeof data.is_super_admin).toBe('boolean');
      expect(data.is_active).toBe(true);
      expect(data.is_super_admin).toBe(true);
    });
  });

  describe('Security Validation', () => {
    it('should have strong password hash', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('password_hash')
        .eq('username', 'admin')
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();

      const passwordHash = data.password_hash;

      // 验证bcrypt格式
      expect(passwordHash).toMatch(/^\$2[aby]\$\d{2}\$.{53}$/);

      // 验证不是明文密码
      expect(passwordHash).not.toBe('admin123');
      expect(passwordHash).not.toContain('admin123');

      // 验证哈希长度
      expect(passwordHash.length).toBe(60);
    });

    it('should not expose sensitive information in other fields', async () => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('username', 'admin')
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();

      // 确保没有在其他字段中存储明文密码
      expect(data.email).not.toContain('admin123');
      expect(data.full_name).not.toContain('admin123');
    });
  });
});
