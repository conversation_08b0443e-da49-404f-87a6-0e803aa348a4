#!/usr/bin/env ts-node

/**
 * 测试层级分类功能的脚本
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { HabitCategoryService } from '../habits/services/habit-category.service';
import { Logger } from '@nestjs/common';

async function testHierarchy() {
  const logger = new Logger('TestHierarchy');
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const habitCategoryService = app.get(HabitCategoryService);

    logger.log('🧪 开始测试层级分类功能...');

    // 1. 测试获取分类树
    logger.log('📊 测试获取分类树...');
    const tree = await habitCategoryService.findCategoryTree({
      is_active: true,
    });
    logger.log(`找到 ${tree.length} 个一级分类`);

    tree.forEach((category) => {
      logger.log(`  📁 ${category.name} (Level ${category.level})`);
      if (category.children && category.children.length > 0) {
        category.children.forEach((child) => {
          logger.log(`    📂 ${child.name} (Level ${child.level})`);
        });
      }
    });

    // 2. 测试获取一级分类
    logger.log('📊 测试获取一级分类...');
    const topLevel = await habitCategoryService.findTopLevelCategories(true);
    logger.log(`找到 ${topLevel.length} 个一级分类`);

    // 3. 测试获取子分类
    if (topLevel.length > 0) {
      const firstCategory = topLevel[0];
      logger.log(`📊 测试获取 "${firstCategory.name}" 的子分类...`);
      const children = await habitCategoryService.findChildren(
        firstCategory.id,
      );
      logger.log(`找到 ${children.length} 个子分类`);
    }

    // 4. 测试创建层级分类
    logger.log('📊 测试创建层级分类...');

    try {
      // 创建一级分类
      const newTopCategory = await habitCategoryService.create({
        name: '测试一级分类',
        icon: '🧪',
        level: 1,
        is_active: true,
      });
      logger.log(`✅ 成功创建一级分类: ${newTopCategory.name}`);

      // 创建二级分类
      const newSubCategory = await habitCategoryService.create({
        name: '测试二级分类',
        icon: '🔬',
        parent_id: newTopCategory.id,
        level: 2,
        is_active: true,
      });
      logger.log(`✅ 成功创建二级分类: ${newSubCategory.name}`);

      // 清理测试数据
      await habitCategoryService.delete(newSubCategory.id);
      await habitCategoryService.delete(newTopCategory.id);
      logger.log('🧹 测试数据已清理');
    } catch (error) {
      logger.error('❌ 创建测试失败:', error.message);
    }

    logger.log('✅ 层级分类功能测试完成！');
  } catch (error) {
    logger.error('❌ 测试过程中发生错误:', error);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testHierarchy().catch(console.error);
}

export { testHierarchy };
