#!/usr/bin/env ts-node

/**
 * 验证层级分类迁移结果的脚本
 *
 * 此脚本用于验证数据库迁移后的层级分类结构是否正确
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SupabaseService } from '../database/supabase.service';
import { Logger } from '@nestjs/common';

interface ValidationResult {
  success: boolean;
  message: string;
  details?: any;
}

class HierarchyMigrationValidator {
  private readonly logger = new Logger(HierarchyMigrationValidator.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * 执行完整的验证流程
   */
  async validate(): Promise<boolean> {
    try {
      this.logger.log('🔍 开始验证层级分类迁移结果...');

      const validations = [
        () => this.validateTableStructure(),
        () => this.validateCategoryHierarchy(),
        () => this.validateCategoryData(),
        () => this.validateTemplateData(),
        () => this.validateDataIntegrity(),
        () => this.validateConstraints(),
        () => this.validateIndexes(),
      ];

      let allPassed = true;

      for (const validation of validations) {
        const result = await validation();
        if (!result.success) {
          this.logger.error(`❌ ${result.message}`);
          if (result.details) {
            this.logger.error('详细信息:', result.details);
          }
          allPassed = false;
        } else {
          this.logger.log(`✅ ${result.message}`);
        }
      }

      if (allPassed) {
        this.logger.log('🎉 所有验证通过！层级分类迁移成功！');
      } else {
        this.logger.error('💥 验证失败，请检查迁移结果');
      }

      return allPassed;
    } catch (error) {
      this.logger.error('验证过程中发生错误:', error);
      return false;
    }
  }

  /**
   * 验证表结构
   */
  private async validateTableStructure(): Promise<ValidationResult> {
    try {
      // Check if hierarchy fields exist by trying to query them
      let hasParentId = false;
      let hasLevel = false;

      try {
        const { error: parentError } = await this.supabaseService
          .getClient()
          .from('habit_categories')
          .select('parent_id')
          .limit(1);
        hasParentId = !parentError;
      } catch (error) {
        hasParentId = false;
      }

      try {
        const { error: levelError } = await this.supabaseService
          .getClient()
          .from('habit_categories')
          .select('level')
          .limit(1);
        hasLevel = !levelError;
      } catch (error) {
        hasLevel = false;
      }

      const data = null;
      const error = null;
      console.log('error', error);

      if (error) {
        return {
          success: false,
          message: '无法检查表结构',
          // details: error?.message || '',
        };
      }

      // Check if required fields exist
      if (!hasParentId) {
        return {
          success: false,
          message: '表结构验证失败：缺少字段 parent_id',
        };
      }

      if (!hasLevel) {
        return {
          success: false,
          message: '表结构验证失败：缺少字段 level',
        };
      }

      return {
        success: true,
        message: '表结构验证通过',
      };
    } catch (error) {
      return {
        success: false,
        message: '表结构验证失败',
        details: error.message,
      };
    }
  }

  /**
   * 验证分类层级结构
   */
  private async validateCategoryHierarchy(): Promise<ValidationResult> {
    try {
      const { data: categories, error } = await this.supabaseService
        .query('habit_categories')
        .select('id, name, parent_id, level')
        .eq('is_active', true)
        .order('level')
        .order('sort_order');

      if (error) {
        return {
          success: false,
          message: '无法查询分类数据',
          details: error.message,
        };
      }

      const level1Categories = categories?.filter((c) => c.level === 1) || [];
      const level2Categories = categories?.filter((c) => c.level === 2) || [];

      // 验证一级分类
      if (level1Categories.length === 0) {
        return {
          success: false,
          message: '层级结构验证失败：没有找到一级分类',
        };
      }

      // 验证一级分类的parent_id都为null
      const invalidLevel1 = level1Categories.filter(
        (c) => c.parent_id !== null,
      );
      if (invalidLevel1.length > 0) {
        return {
          success: false,
          message: '层级结构验证失败：一级分类的parent_id应为null',
          details: invalidLevel1.map((c) => c.name),
        };
      }

      // 验证二级分类的parent_id都不为null且存在于一级分类中
      const level1Ids = new Set(level1Categories.map((c) => c.id));
      const invalidLevel2 = level2Categories.filter(
        (c) => c.parent_id === null || !level1Ids.has(c.parent_id),
      );

      if (invalidLevel2.length > 0) {
        return {
          success: false,
          message: '层级结构验证失败：二级分类的parent_id无效',
          details: invalidLevel2.map((c) => ({
            name: c.name,
            parent_id: c.parent_id,
          })),
        };
      }

      return {
        success: true,
        message: `层级结构验证通过 (一级分类: ${level1Categories.length}, 二级分类: ${level2Categories.length})`,
      };
    } catch (error) {
      return {
        success: false,
        message: '层级结构验证失败',
        details: error.message,
      };
    }
  }

  /**
   * 验证分类数据
   */
  private async validateCategoryData(): Promise<ValidationResult> {
    try {
      const expectedCategories = [
        { name: '生活自理', level: 1, children: ['个人卫生', '生活技能'] },
        { name: '健康习惯', level: 1, children: [] },
        { name: '学习认知', level: 1, children: [] },
        { name: '社交与情绪', level: 1, children: [] },
        { name: '整理与责任', level: 1, children: [] },
      ];

      const { data: categories, error } = await this.supabaseService
        .query('habit_categories')
        .select('id, name, parent_id, level')
        .eq('is_active', true);

      if (error) {
        return {
          success: false,
          message: '无法查询分类数据',
          details: error.message,
        };
      }

      // 验证每个期望的分类是否存在
      for (const expected of expectedCategories) {
        const category = categories?.find(
          (c) => c.name === expected.name && c.level === expected.level,
        );
        if (!category) {
          return {
            success: false,
            message: `分类数据验证失败：缺少分类 "${expected.name}"`,
          };
        }

        // 验证子分类
        for (const childName of expected.children) {
          const child = categories?.find(
            (c) =>
              c.name === childName &&
              c.level === 2 &&
              c.parent_id === category.id,
          );
          if (!child) {
            return {
              success: false,
              message: `分类数据验证失败：缺少子分类 "${childName}" (父分类: ${expected.name})`,
            };
          }
        }
      }

      return {
        success: true,
        message: '分类数据验证通过',
      };
    } catch (error) {
      return {
        success: false,
        message: '分类数据验证失败',
        details: error.message,
      };
    }
  }

  /**
   * 验证模板数据
   */
  private async validateTemplateData(): Promise<ValidationResult> {
    try {
      const expectedTemplates = [
        { name: '睡前刷牙', category: '个人卫生' },
        { name: '饭前洗手', category: '个人卫生' },
        { name: '独立吃饭', category: '生活技能' },
        { name: '自己穿鞋', category: '生活技能' },
        { name: '上厕所', category: '生活技能' },
        { name: '规律作息', category: '健康习惯' },
        { name: '户外活动', category: '健康习惯' },
        { name: '亲子阅读', category: '学习认知' },
        { name: '绘画', category: '学习认知' },
        { name: '拼图/积木', category: '学习认知' },
        { name: '分享玩具', category: '社交与情绪' },
        { name: '整理玩具', category: '整理与责任' },
      ];

      // Get templates with category names using separate queries
      const { data: templates, error } = await this.supabaseService
        .query('habit_templates')
        .select('name, category_id, habit_categories!inner(name)')
        .eq('is_active', true);

      if (error) {
        return {
          success: false,
          message: '无法查询模板数据',
          details: error.message,
        };
      }

      // 验证每个期望的模板是否存在
      for (const expected of expectedTemplates) {
        const template = templates?.find(
          (t: any) =>
            t.name === expected.name &&
            t.habit_categories?.name === expected.category,
        );
        if (!template) {
          return {
            success: false,
            message: `模板数据验证失败：缺少模板 "${expected.name}" (分类: ${expected.category})`,
          };
        }
      }

      return {
        success: true,
        message: `模板数据验证通过 (共 ${templates?.length || 0} 个模板)`,
      };
    } catch (error) {
      return {
        success: false,
        message: '模板数据验证失败',
        details: error.message,
      };
    }
  }

  /**
   * 验证数据完整性
   */
  private async validateDataIntegrity(): Promise<ValidationResult> {
    try {
      // 检查孤立的模板（没有对应分类的模板）
      const { data: allTemplates } = await this.supabaseService
        .query('habit_templates')
        .select('id, name, category_id');

      const { data: allCategories } = await this.supabaseService
        .query('habit_categories')
        .select('id, level');

      const categoryIds = new Set(allCategories?.map((c) => c.id) || []);
      const orphanTemplates =
        allTemplates?.filter((t) => !categoryIds.has(t.category_id)) || [];

      if (orphanTemplates && orphanTemplates.length > 0) {
        return {
          success: false,
          message: '数据完整性验证失败：发现孤立的模板',
          details: orphanTemplates.map((t) => t.name),
        };
      }

      // 检查孤立的二级分类（parent_id指向不存在的分类）
      const { data: level2Categories } = await this.supabaseService
        .query('habit_categories')
        .select('id, name, parent_id')
        .eq('level', 2)
        .not('parent_id', 'is', null);

      const level1CategoryIds = new Set(
        allCategories?.filter((c) => c.level === 1).map((c) => c.id) || [],
      );
      const orphanCategories =
        level2Categories?.filter((c) => !level1CategoryIds.has(c.parent_id)) ||
        [];

      if (orphanCategories && orphanCategories.length > 0) {
        return {
          success: false,
          message: '数据完整性验证失败：发现孤立的二级分类',
          details: orphanCategories.map((c) => c.name),
        };
      }

      return {
        success: true,
        message: '数据完整性验证通过',
      };
    } catch (error) {
      return {
        success: false,
        message: '数据完整性验证失败',
        details: error.message,
      };
    }
  }

  /**
   * 验证约束条件
   */
  private async validateConstraints(): Promise<ValidationResult> {
    try {
      // 检查约束是否存在
      const { data: constraints, error } = await this.supabaseService
        .query('information_schema.table_constraints')
        .select('constraint_name, constraint_type')
        .eq('table_name', 'habit_categories')
        .eq('table_schema', 'public');

      if (error) {
        return {
          success: false,
          message: '无法检查约束条件',
          details: error.message,
        };
      }

      const constraintNames = constraints?.map((c) => c.constraint_name) || [];

      const expectedConstraints = ['check_parent_level', 'check_level_range'];

      for (const expectedConstraint of expectedConstraints) {
        if (!constraintNames.includes(expectedConstraint)) {
          return {
            success: false,
            message: `约束验证失败：缺少约束 ${expectedConstraint}`,
          };
        }
      }

      return {
        success: true,
        message: '约束条件验证通过',
      };
    } catch (error) {
      return {
        success: false,
        message: '约束条件验证失败',
        details: error.message,
      };
    }
  }

  /**
   * 验证索引
   */
  private async validateIndexes(): Promise<ValidationResult> {
    try {
      const { data: indexes, error } = await this.supabaseService
        .query('pg_indexes')
        .select('indexname')
        .eq('tablename', 'habit_categories')
        .eq('schemaname', 'public');

      if (error) {
        return {
          success: false,
          message: '无法检查索引',
          details: error.message,
        };
      }

      const indexNames = indexes?.map((i) => i.indexname) || [];

      const expectedIndexes = [
        'idx_habit_categories_parent',
        'idx_habit_categories_level',
        'idx_habit_categories_active',
      ];

      for (const expectedIndex of expectedIndexes) {
        if (!indexNames.includes(expectedIndex)) {
          return {
            success: false,
            message: `索引验证失败：缺少索引 ${expectedIndex}`,
          };
        }
      }

      return {
        success: true,
        message: '索引验证通过',
      };
    } catch (error) {
      return {
        success: false,
        message: '索引验证失败',
        details: error.message,
      };
    }
  }
}

/**
 * 主执行函数
 */
async function main() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);

  const validator = new HierarchyMigrationValidator(supabaseService);

  try {
    const success = await validator.validate();
    if (success) {
      console.log('✅ 验证成功完成');
      process.exit(0);
    } else {
      console.log('❌ 验证失败');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { HierarchyMigrationValidator };
