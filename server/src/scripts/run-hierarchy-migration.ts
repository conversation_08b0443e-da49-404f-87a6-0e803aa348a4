#!/usr/bin/env ts-node

/**
 * 层级分类迁移主脚本
 *
 * 此脚本协调执行完整的层级分类迁移流程：
 * 1. 运行数据库结构迁移
 * 2. 运行数据迁移
 * 3. 验证迁移结果
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { Logger } from '@nestjs/common';
import { HabitCategoryMigration } from './migrate-habit-categories';
import { HierarchyMigrationValidator } from './validate-hierarchy-migration';
import { SupabaseService } from '../database/supabase.service';

class HierarchyMigrationRunner {
  private readonly logger = new Logger(HierarchyMigrationRunner.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * 执行完整的迁移流程
   */
  async run(): Promise<void> {
    try {
      this.logger.log('🚀 开始层级分类迁移流程...');

      // 1. 检查当前状态
      await this.checkCurrentState();

      // 2. 执行迁移
      const migration = new HabitCategoryMigration(this.supabaseService);
      await migration.migrate();

      // 3. 验证结果
      const validator = new HierarchyMigrationValidator(this.supabaseService);
      const success = await validator.validate();

      if (success) {
        this.logger.log('🎉 层级分类迁移流程完成！');
      } else {
        throw new Error('迁移验证失败');
      }
    } catch (error) {
      this.logger.error('❌ 迁移流程失败:', error);
      throw error;
    }
  }

  /**
   * 检查当前状态
   */
  private async checkCurrentState(): Promise<void> {
    this.logger.log('🔍 检查当前数据库状态...');

    try {
      // 检查表是否存在 - 通过尝试查询表来检查
      const requiredTables = ['habit_categories', 'habit_templates'];

      for (const tableName of requiredTables) {
        try {
          const { error } = await this.supabaseService
            .getClient()
            .from(tableName)
            .select('count', { count: 'exact', head: true });

          if (error) {
            throw new Error(
              `${tableName} 表不存在或无法访问，请先运行基础迁移`,
            );
          }
        } catch (error) {
          throw new Error(`${tableName} 表不存在或无法访问，请先运行基础迁移`);
        }
      }

      // 检查是否已经有层级字段 - 通过尝试查询字段来检查
      let hasHierarchy = false;
      try {
        const { data, error } = await this.supabaseService
          .getClient()
          .from('habit_categories')
          .select('parent_id, level')
          .limit(1);

        hasHierarchy = !error;
      } catch (error) {
        hasHierarchy = false;
      }

      if (hasHierarchy) {
        this.logger.log('⚠️  检测到已存在层级字段，将执行数据更新');
      } else {
        this.logger.log('✅ 准备添加层级字段');
      }

      // 检查现有数据
      const { data: categories, error: categoryError } =
        await this.supabaseService
          .query('habit_categories')
          .select('id, name')
          .eq('is_active', true);

      if (categoryError) {
        this.logger.warn(`检查现有分类失败: ${categoryError.message}`);
      } else {
        this.logger.log(`当前活跃分类数量: ${categories?.length || 0}`);
      }

      const { data: templates, error: templateError } =
        await this.supabaseService
          .query('habit_templates')
          .select('id, name')
          .eq('is_active', true);

      if (templateError) {
        this.logger.warn(`检查现有模板失败: ${templateError.message}`);
      } else {
        this.logger.log(`当前活跃模板数量: ${templates?.length || 0}`);
      }
    } catch (error) {
      this.logger.error('状态检查失败:', error);
      throw error;
    }
  }

  /**
   * 回滚迁移（紧急情况使用）
   */
  async rollback(): Promise<void> {
    this.logger.warn('⚠️  开始回滚层级分类迁移...');

    try {
      // 删除新增的约束
      await this.supabaseService.query(`
        ALTER TABLE habit_categories 
        DROP CONSTRAINT IF EXISTS check_parent_level,
        DROP CONSTRAINT IF EXISTS check_level_range;
      `);

      // 删除新增的索引
      await this.supabaseService.query(`
        DROP INDEX IF EXISTS idx_habit_categories_parent;
        DROP INDEX IF EXISTS idx_habit_categories_level;
      `);

      // 删除新增的字段
      await this.supabaseService.query(`
        ALTER TABLE habit_categories 
        DROP COLUMN IF EXISTS parent_id,
        DROP COLUMN IF EXISTS level;
      `);

      // 恢复原始索引
      await this.supabaseService.query(`
        DROP INDEX IF EXISTS idx_habit_categories_active;
        CREATE INDEX idx_habit_categories_active ON habit_categories(is_active, sort_order);
      `);

      this.logger.log('✅ 回滚完成');
    } catch (error) {
      this.logger.error('❌ 回滚失败:', error);
      throw error;
    }
  }
}

/**
 * 主执行函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'migrate';

  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);

  const runner = new HierarchyMigrationRunner(supabaseService);

  try {
    switch (command) {
      case 'migrate':
        await runner.run();
        console.log('✅ 迁移成功完成');
        break;

      case 'rollback':
        await runner.rollback();
        console.log('✅ 回滚成功完成');
        break;

      case 'validate':
        const validator = new HierarchyMigrationValidator(supabaseService);
        const success = await validator.validate();
        if (success) {
          console.log('✅ 验证成功');
        } else {
          console.log('❌ 验证失败');
          process.exit(1);
        }
        break;

      default:
        console.log(
          '用法: ts-node run-hierarchy-migration.ts [migrate|rollback|validate]',
        );
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ 操作失败:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { HierarchyMigrationRunner };
