import { Injectable } from '@nestjs/common';
import { HabitStatisticsService } from './habits/services/habit-statistics.service';
import { OverviewStatisticsResponseDto } from './habits/dto/statistics/statistics-response.dto';

@Injectable()
export class AppService {
  constructor(
    private readonly habitStatisticsService: HabitStatisticsService,
  ) {}

  getHello(): string {
    return 'Hello World!';
  }

  /**
   * 获取首页习惯统计概览
   * 包含今日打卡率和时间轴数据
   */
  async getHomepageHabitsOverview(
    userId: string,
    babyId: string,
  ): Promise<OverviewStatisticsResponseDto> {
    return this.habitStatisticsService.getOverviewStatistics(userId, babyId);
  }
}
