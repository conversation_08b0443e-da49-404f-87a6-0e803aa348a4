import { Controller, Get, UseGuards, Query } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppService } from './app.service';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { CurrentUser } from './common/decorators/current-user.decorator';
import { OverviewStatisticsResponseDto } from './habits/dto/statistics/statistics-response.dto';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly configService: ConfigService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: this.configService.get('app.nodeEnv'),
      version: '1.0.0',
    };
  }

  /**
   * 获取首页习惯统计概览
   * 包含今日打卡率和时间轴
   */
  @Get('homepage/habits-overview')
  @UseGuards(JwtAuthGuard)
  async getHomepageHabitsOverview(
    @CurrentUser() user: any,
    @Query('baby_id') babyId: string,
  ): Promise<OverviewStatisticsResponseDto> {
    return this.appService.getHomepageHabitsOverview(user.id, babyId);
  }
}
