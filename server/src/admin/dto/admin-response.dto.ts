import { IAdminUser } from '../../common/interfaces/entities';

export class AdminResponseDto {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;

  constructor(admin: IAdminUser) {
    this.id = admin.id;
    this.username = admin.username;
    this.email = admin.email;
    this.full_name = admin.full_name;
    this.is_active = admin.is_active;
    this.is_super_admin = admin.is_super_admin;
    this.last_login_at = admin.last_login_at;
    this.created_at = admin.created_at;
    this.updated_at = admin.updated_at;
  }
}
