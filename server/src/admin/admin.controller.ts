import {
  Controller,
  Get,
  Put,
  Post,
  Param,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminJwtAuthGuard } from '../auth/guards/admin-jwt-auth.guard';
import { CurrentAdmin } from '../common/decorators/current-admin.decorator';
import { IAdminUser } from '../common/interfaces/entities';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { AdminResponseDto } from './dto/admin-response.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@Controller('admin')
@UseGuards(AdminJwtAuthGuard)
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  /**
   * 获取当前管理员资料
   */
  @Get('profile')
  async getProfile(
    @CurrentAdmin() admin: IAdminUser,
  ): Promise<AdminResponseDto> {
    return this.adminService.getProfile(admin.id);
  }

  /**
   * 更新当前管理员资料
   */
  @Put('profile')
  async updateProfile(
    @CurrentAdmin() admin: IAdminUser,
    @Body() updateData: UpdateAdminDto,
  ): Promise<AdminResponseDto> {
    return this.adminService.updateProfile(admin.id, updateData);
  }

  /**
   * 修改当前管理员密码
   */
  @Post('change-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  async changePassword(
    @CurrentAdmin() admin: IAdminUser,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    return this.adminService.changePassword(admin.id, changePasswordDto);
  }

  /**
   * 获取管理员列表（仅超级管理员）
   */
  @Get('list')
  async getAdminList(
    @CurrentAdmin() admin: IAdminUser,
  ): Promise<AdminResponseDto[]> {
    return this.adminService.getAdminList(admin.id);
  }

  /**
   * 停用管理员账户（仅超级管理员）
   */
  @Post(':id/deactivate')
  async deactivateAdmin(
    @CurrentAdmin() admin: IAdminUser,
    @Param('id') targetAdminId: string,
  ): Promise<AdminResponseDto> {
    return this.adminService.deactivateAdmin(admin.id, targetAdminId);
  }

  /**
   * 激活管理员账户（仅超级管理员）
   */
  @Post(':id/activate')
  async activateAdmin(
    @CurrentAdmin() admin: IAdminUser,
    @Param('id') targetAdminId: string,
  ): Promise<AdminResponseDto> {
    return this.adminService.activateAdmin(admin.id, targetAdminId);
  }
}
