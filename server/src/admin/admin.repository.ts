import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../database/base.repository';
import { SupabaseService } from '../database/supabase.service';
import { DatabaseUtils } from '../database/database.utils';
import {
  IAdminUser,
  ICreateAdminDto,
  IUpdateAdminDto,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminRepository extends BaseRepository<IAdminUser> {
  protected readonly tableName = 'admin_users';
  protected readonly entityName = 'Admin';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 根据用户名查找管理员
   */
  async findByUsername(username: string): Promise<IAdminUser | null> {
    try {
      DatabaseUtils.logDatabaseOperation('FIND_BY_USERNAME', this.tableName, {
        username,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('username', username)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find admin by username',
        );
      }

      return result.data as IAdminUser | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find admin by username',
        this.entityName,
      );
    }
  }

  /**
   * 根据邮箱查找管理员
   */
  async findByEmail(email: string): Promise<IAdminUser | null> {
    try {
      DatabaseUtils.logDatabaseOperation('FIND_BY_EMAIL', this.tableName, {
        email,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('email', email)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find admin by email',
        );
      }

      return result.data as IAdminUser | null;
    } catch (error) {
      DatabaseUtils.handleError(error, 'find admin by email', this.entityName);
    }
  }

  /**
   * 创建管理员用户
   */
  async createAdmin(adminData: ICreateAdminDto): Promise<IAdminUser> {
    try {
      // 验证用户名是否已存在
      const existingAdmin = await this.findByUsername(adminData.username);
      if (existingAdmin) {
        throw new BusinessException(
          ErrorCode.ADMIN_USERNAME_TAKEN,
          'Admin with this username already exists',
        );
      }

      // 如果提供了邮箱，验证唯一性
      if (adminData.email) {
        const existingEmailAdmin = await this.findByEmail(adminData.email);
        if (existingEmailAdmin) {
          throw new BusinessException(
            ErrorCode.ADMIN_USERNAME_TAKEN,
            'Admin with this email already exists',
          );
        }
      }

      // 加密密码
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(adminData.password, saltRounds);

      const createData = {
        ...adminData,
        password_hash: passwordHash,
      };

      // 移除明文密码
      delete (createData as any).password;

      return await this.create(createData);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'create admin', this.entityName);
    }
  }

  /**
   * 更新管理员信息
   */
  async updateAdmin(
    id: string,
    adminData: IUpdateAdminDto,
  ): Promise<IAdminUser> {
    try {
      // 如果更新邮箱，验证唯一性
      if (adminData.email) {
        const existingEmailAdmin = await this.findByEmail(adminData.email);
        if (existingEmailAdmin && existingEmailAdmin.id !== id) {
          throw new BusinessException(
            ErrorCode.ADMIN_USERNAME_TAKEN,
            'Admin with this email already exists',
          );
        }
      }

      return await this.update(id, adminData);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'update admin', this.entityName);
    }
  }

  /**
   * 验证管理员密码
   */
  async validatePassword(
    username: string,
    password: string,
  ): Promise<IAdminUser | null> {
    try {
      const admin = await this.findByUsername(username);
      if (!admin) {
        return null;
      }

      const isPasswordValid = await bcrypt.compare(
        password,
        admin.password_hash,
      );
      if (!isPasswordValid) {
        return null;
      }

      return admin;
    } catch (error) {
      this.logger.warn('Failed to validate admin password', error);
      return null;
    }
  }

  /**
   * 更新管理员密码
   */
  async updatePassword(id: string, newPassword: string): Promise<IAdminUser> {
    try {
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(newPassword, saltRounds);

      return await this.update(id, {
        password_hash: passwordHash,
      } as IUpdateAdminDto);
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'update admin password',
        this.entityName,
      );
    }
  }

  /**
   * 更新最后登录时间
   */
  async updateLastLogin(id: string): Promise<IAdminUser> {
    try {
      return await this.update(id, {
        last_login_at: new Date(),
      } as IUpdateAdminDto);
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'update admin last login',
        this.entityName,
      );
    }
  }

  /**
   * 获取活跃的管理员列表
   */
  async findActiveAdmins(): Promise<IAdminUser[]> {
    try {
      return await this.findMany({ is_active: true } as Partial<IAdminUser>, {
        sortBy: 'created_at',
        sortOrder: 'desc',
      });
    } catch (error) {
      DatabaseUtils.handleError(error, 'find active admins', this.entityName);
    }
  }

  /**
   * 获取超级管理员列表
   */
  async findSuperAdmins(): Promise<IAdminUser[]> {
    try {
      return await this.findMany(
        { is_super_admin: true, is_active: true } as Partial<IAdminUser>,
        { sortBy: 'created_at', sortOrder: 'desc' },
      );
    } catch (error) {
      DatabaseUtils.handleError(error, 'find super admins', this.entityName);
    }
  }

  /**
   * 停用管理员账户
   */
  async deactivateAdmin(id: string): Promise<IAdminUser> {
    try {
      return await this.update(id, { is_active: false } as IUpdateAdminDto);
    } catch (error) {
      DatabaseUtils.handleError(error, 'deactivate admin', this.entityName);
    }
  }

  /**
   * 激活管理员账户
   */
  async activateAdmin(id: string): Promise<IAdminUser> {
    try {
      return await this.update(id, { is_active: true } as IUpdateAdminDto);
    } catch (error) {
      DatabaseUtils.handleError(error, 'activate admin', this.entityName);
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IAdminUser>): void {
    DatabaseUtils.validateRequiredFields(
      data,
      ['username', 'password_hash'] as (keyof IAdminUser)[],
      this.entityName,
    );

    const createData = data as ICreateAdminDto;

    if (!createData.username || createData.username.trim() === '') {
      throw new BusinessException(
        ErrorCode.INVALID_ADMIN_CREDENTIALS,
        'Username is required and cannot be empty',
      );
    }

    if (createData.username.length < 3 || createData.username.length > 50) {
      throw new BusinessException(
        ErrorCode.INVALID_ADMIN_CREDENTIALS,
        'Username must be between 3 and 50 characters',
      );
    }

    // 验证邮箱格式
    if (createData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(createData.email)) {
        throw new BusinessException(
          ErrorCode.INVALID_ADMIN_CREDENTIALS,
          'Invalid email format',
        );
      }
    }
  }
}
