import * as bcrypt from 'bcrypt';
import {
  IAd<PERSON><PERSON>ser,
  ICreateAdminDto,
  IUpdateAdminDto,
  IAdminResponse,
  IAdminLoginDto,
} from '../../common/interfaces';
import { AdminValidator } from '../../common/validators';

export class <PERSON><PERSON><PERSON>ser implements IAdminUser {
  id: string;
  username: string;
  password_hash: string;
  email?: string;
  full_name?: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;

  constructor(partial: Partial<AdminUser>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新管理员实例
   */
  static async create(data: ICreateAdminDto): Promise<AdminUser> {
    const validation = AdminValidator.validateAdminData({
      username: data.username,
      password: data.password,
      email: data.email,
      full_name: data.full_name,
    });

    if (!validation.isValid) {
      throw new Error(`Invalid admin data: ${validation.errors.join(', ')}`);
    }

    const passwordHash = await AdminUser.hashPassword(data.password);
    const now = new Date();

    return new AdminUser({
      id: '', // 将由数据库生成
      username: data.username,
      password_hash: passwordHash,
      email: data.email,
      full_name: data.full_name,
      is_active: true,
      is_super_admin: data.is_super_admin || false,
      created_at: now,
      updated_at: now,
    });
  }

  /**
   * 更新管理员信息
   */
  update(data: IUpdateAdminDto): void {
    // 验证更新数据
    const updateData = {
      username: this.username, // 保持原有username用于验证
      email: data.email ?? this.email,
      full_name: data.full_name ?? this.full_name,
    };

    const validation = AdminValidator.validateAdminData(updateData);
    if (!validation.isValid) {
      throw new Error(`Invalid update data: ${validation.errors.join(', ')}`);
    }

    // 应用更新
    if (data.email !== undefined) this.email = data.email;
    if (data.full_name !== undefined) this.full_name = data.full_name;
    if (data.is_active !== undefined) this.is_active = data.is_active;

    this.updated_at = new Date();
  }

  /**
   * 验证管理员数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return AdminValidator.validateAdminData({
      username: this.username,
      email: this.email,
      full_name: this.full_name,
    });
  }

  /**
   * 哈希密码
   */
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * 验证密码
   */
  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password_hash);
  }

  /**
   * 更新密码
   */
  async updatePassword(newPassword: string): Promise<void> {
    if (!AdminValidator.validatePassword(newPassword)) {
      throw new Error('Invalid password format');
    }

    this.password_hash = await AdminUser.hashPassword(newPassword);
    this.updated_at = new Date();
  }

  /**
   * 验证登录凭据
   */
  static async validateLogin(
    admin: AdminUser,
    loginData: IAdminLoginDto,
  ): Promise<boolean> {
    if (!admin.is_active) {
      return false;
    }

    return admin.validatePassword(loginData.password);
  }

  /**
   * 记录登录时间
   */
  recordLogin(): void {
    this.last_login_at = new Date();
    this.updated_at = new Date();
  }

  /**
   * 激活管理员账户
   */
  activate(): void {
    this.is_active = true;
    this.updated_at = new Date();
  }

  /**
   * 停用管理员账户
   */
  deactivate(): void {
    this.is_active = false;
    this.updated_at = new Date();
  }

  /**
   * 设置为超级管理员
   */
  setSuperAdmin(isSuperAdmin: boolean): void {
    this.is_super_admin = isSuperAdmin;
    this.updated_at = new Date();
  }

  /**
   * 检查是否为超级管理员
   */
  isSuperAdmin(): boolean {
    return this.is_super_admin;
  }

  /**
   * 检查是否为活跃管理员
   */
  isActive(): boolean {
    return this.is_active;
  }

  /**
   * 检查是否可以执行管理操作
   */
  canPerformAdminActions(): boolean {
    return this.is_active;
  }

  /**
   * 检查是否可以执行超级管理员操作
   */
  canPerformSuperAdminActions(): boolean {
    return this.is_active && this.is_super_admin;
  }

  /**
   * 获取最后登录时间描述
   */
  getLastLoginDescription(): string {
    if (!this.last_login_at) {
      return '从未登录';
    }

    const now = new Date();
    const diffMs = now.getTime() - this.last_login_at.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks}周前`;
    } else {
      const months = Math.floor(diffDays / 30);
      return `${months}个月前`;
    }
  }

  /**
   * 转换为响应DTO格式（不包含密码哈希）
   */
  toResponse(): IAdminResponse {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      full_name: this.full_name,
      is_active: this.is_active,
      is_super_admin: this.is_super_admin,
      last_login_at: this.last_login_at,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }

  /**
   * 转换为详细响应格式（包含权限和状态信息）
   */
  toDetailedResponse(): IAdminResponse & {
    last_login_description: string;
    permissions: {
      can_perform_admin_actions: boolean;
      can_perform_super_admin_actions: boolean;
    };
  } {
    return {
      ...this.toResponse(),
      last_login_description: this.getLastLoginDescription(),
      permissions: {
        can_perform_admin_actions: this.canPerformAdminActions(),
        can_perform_super_admin_actions: this.canPerformSuperAdminActions(),
      },
    };
  }
}
