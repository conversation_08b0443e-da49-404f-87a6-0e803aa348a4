import { AdminUser } from '../admin.entity';
import {
  ICreateAdminDto,
  IUpdateAdminDto,
  IAdminLoginDto,
} from '../../../common/interfaces';

describe('AdminUser Entity', () => {
  const validAdminData: ICreateAdminDto = {
    username: 'admin',
    password: 'password123',
    email: '<EMAIL>',
    full_name: 'System Administrator',
  };

  describe('constructor', () => {
    it('should create admin user instance with partial data', () => {
      const adminData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        username: 'admin',
        password_hash: 'hashed_password',
        email: '<EMAIL>',
        full_name: 'System Administrator',
        is_active: true,
        is_super_admin: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const admin = new AdminUser(adminData);

      expect(admin.id).toBe(adminData.id);
      expect(admin.username).toBe(adminData.username);
      expect(admin.password_hash).toBe(adminData.password_hash);
      expect(admin.email).toBe(adminData.email);
      expect(admin.full_name).toBe(adminData.full_name);
      expect(admin.is_active).toBe(adminData.is_active);
      expect(admin.is_super_admin).toBe(adminData.is_super_admin);
    });
  });

  describe('create', () => {
    it('should create admin with valid data', async () => {
      const admin = await AdminUser.create(validAdminData);

      expect(admin.username).toBe(validAdminData.username);
      expect(admin.email).toBe(validAdminData.email);
      expect(admin.full_name).toBe(validAdminData.full_name);
      expect(admin.is_active).toBe(true);
      expect(admin.is_super_admin).toBe(false);
      expect(admin.password_hash).toBeDefined();
      expect(admin.password_hash).not.toBe(validAdminData.password);
      expect(admin.created_at).toBeInstanceOf(Date);
      expect(admin.updated_at).toBeInstanceOf(Date);
    });

    it('should create super admin', async () => {
      const superAdminData: ICreateAdminDto = {
        ...validAdminData,
        is_super_admin: true,
      };

      const admin = await AdminUser.create(superAdminData);

      expect(admin.is_super_admin).toBe(true);
    });

    it('should create admin with minimal data', async () => {
      const minimalData: ICreateAdminDto = {
        username: 'admin',
        password: 'password123',
      };

      const admin = await AdminUser.create(minimalData);

      expect(admin.username).toBe(minimalData.username);
      expect(admin.email).toBeUndefined();
      expect(admin.full_name).toBeUndefined();
      expect(admin.is_super_admin).toBe(false);
    });

    it('should throw error for invalid data', async () => {
      const invalidData: ICreateAdminDto = {
        username: 'ab', // Too short
        password: '123', // Too weak
        email: 'invalid-email',
        full_name: 'a'.repeat(101), // Too long
      };

      await expect(AdminUser.create(invalidData)).rejects.toThrow(
        'Invalid admin data',
      );
    });
  });

  describe('update', () => {
    let admin: AdminUser;

    beforeEach(async () => {
      admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';
    });

    it('should update admin with valid data', () => {
      const updateData: IUpdateAdminDto = {
        email: '<EMAIL>',
        full_name: 'New Administrator',
        is_active: false,
      };

      const originalUpdatedAt = admin.updated_at;

      admin.update(updateData);

      expect(admin.email).toBe(updateData.email);
      expect(admin.full_name).toBe(updateData.full_name);
      expect(admin.is_active).toBe(updateData.is_active);
      expect(admin.updated_at).not.toBe(originalUpdatedAt);
    });

    it('should update only provided fields', () => {
      const originalEmail = admin.email;
      const originalFullName = admin.full_name;

      const updateData: IUpdateAdminDto = {
        is_active: false,
      };

      admin.update(updateData);

      expect(admin.email).toBe(originalEmail);
      expect(admin.full_name).toBe(originalFullName);
      expect(admin.is_active).toBe(false);
    });

    it('should throw error for invalid update data', () => {
      const invalidUpdateData: IUpdateAdminDto = {
        email: 'invalid-email',
        full_name: 'a'.repeat(101), // Too long
      };

      expect(() => admin.update(invalidUpdateData)).toThrow(
        'Invalid update data',
      );
    });
  });

  describe('validate', () => {
    it('should return valid for correct admin data', async () => {
      const admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';

      const result = admin.validate();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect admin data', () => {
      const admin = new AdminUser({
        id: '123e4567-e89b-12d3-a456-426614174000',
        username: 'ab', // Too short
        password_hash: 'hashed_password',
        email: 'invalid-email',
        full_name: 'a'.repeat(101), // Too long
        is_active: true,
        is_super_admin: false,
        created_at: new Date(),
        updated_at: new Date(),
      });

      const result = admin.validate();

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('password management', () => {
    let admin: AdminUser;

    beforeEach(async () => {
      admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';
    });

    describe('hashPassword', () => {
      it('should hash password correctly', async () => {
        const password = 'testpassword123';
        const hash = await AdminUser.hashPassword(password);

        expect(hash).toBeDefined();
        expect(hash).not.toBe(password);
        expect(hash.length).toBeGreaterThan(50); // bcrypt hashes are typically 60 characters
      });

      it('should generate different hashes for same password', async () => {
        const password = 'testpassword123';
        const hash1 = await AdminUser.hashPassword(password);
        const hash2 = await AdminUser.hashPassword(password);

        expect(hash1).not.toBe(hash2);
      });
    });

    describe('validatePassword', () => {
      it('should validate correct password', async () => {
        const isValid = await admin.validatePassword(validAdminData.password);
        expect(isValid).toBe(true);
      });

      it('should reject incorrect password', async () => {
        const isValid = await admin.validatePassword('wrongpassword');
        expect(isValid).toBe(false);
      });
    });

    describe('updatePassword', () => {
      it('should update password with valid new password', async () => {
        const newPassword = 'newpassword123';
        const originalHash = admin.password_hash;
        const originalUpdatedAt = admin.updated_at;

        await admin.updatePassword(newPassword);

        expect(admin.password_hash).not.toBe(originalHash);
        expect(admin.updated_at).not.toBe(originalUpdatedAt);

        // Should validate with new password
        const isValid = await admin.validatePassword(newPassword);
        expect(isValid).toBe(true);
      });

      it('should throw error for invalid password', async () => {
        const invalidPassword = '123'; // Too weak

        await expect(admin.updatePassword(invalidPassword)).rejects.toThrow(
          'Invalid password format',
        );
      });
    });
  });

  describe('login validation', () => {
    let admin: AdminUser;

    beforeEach(async () => {
      admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';
    });

    it('should validate login with correct credentials', async () => {
      const loginData: IAdminLoginDto = {
        username: validAdminData.username,
        password: validAdminData.password,
      };

      const isValid = await AdminUser.validateLogin(admin, loginData);
      expect(isValid).toBe(true);
    });

    it('should reject login with incorrect password', async () => {
      const loginData: IAdminLoginDto = {
        username: validAdminData.username,
        password: 'wrongpassword',
      };

      const isValid = await AdminUser.validateLogin(admin, loginData);
      expect(isValid).toBe(false);
    });

    it('should reject login for inactive admin', async () => {
      admin.deactivate();

      const loginData: IAdminLoginDto = {
        username: validAdminData.username,
        password: validAdminData.password,
      };

      const isValid = await AdminUser.validateLogin(admin, loginData);
      expect(isValid).toBe(false);
    });
  });

  describe('status management', () => {
    let admin: AdminUser;

    beforeEach(async () => {
      admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';
    });

    describe('recordLogin', () => {
      it('should record login time', () => {
        expect(admin.last_login_at).toBeUndefined();

        admin.recordLogin();

        expect(admin.last_login_at).toBeInstanceOf(Date);
        expect(admin.updated_at).toBeInstanceOf(Date);
      });
    });

    describe('activate/deactivate', () => {
      it('should activate admin', () => {
        admin.is_active = false;
        const originalUpdatedAt = admin.updated_at;

        admin.activate();

        expect(admin.is_active).toBe(true);
        expect(admin.updated_at).not.toBe(originalUpdatedAt);
      });

      it('should deactivate admin', () => {
        const originalUpdatedAt = admin.updated_at;

        admin.deactivate();

        expect(admin.is_active).toBe(false);
        expect(admin.updated_at).not.toBe(originalUpdatedAt);
      });
    });

    describe('setSuperAdmin', () => {
      it('should set super admin status', () => {
        expect(admin.is_super_admin).toBe(false);
        const originalUpdatedAt = admin.updated_at;

        admin.setSuperAdmin(true);

        expect(admin.is_super_admin).toBe(true);
        expect(admin.updated_at).not.toBe(originalUpdatedAt);
      });

      it('should remove super admin status', () => {
        admin.is_super_admin = true;
        const originalUpdatedAt = admin.updated_at;

        admin.setSuperAdmin(false);

        expect(admin.is_super_admin).toBe(false);
        expect(admin.updated_at).not.toBe(originalUpdatedAt);
      });
    });
  });

  describe('permission checks', () => {
    let admin: AdminUser;
    let superAdmin: AdminUser;

    beforeEach(async () => {
      admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';

      superAdmin = await AdminUser.create({
        ...validAdminData,
        username: 'superadmin',
        is_super_admin: true,
      });
      superAdmin.id = '123e4567-e89b-12d3-a456-426614174001';
    });

    describe('isSuperAdmin', () => {
      it('should return true for super admin', () => {
        expect(superAdmin.isSuperAdmin()).toBe(true);
      });

      it('should return false for regular admin', () => {
        expect(admin.isSuperAdmin()).toBe(false);
      });
    });

    describe('isActive', () => {
      it('should return true for active admin', () => {
        expect(admin.isActive()).toBe(true);
      });

      it('should return false for inactive admin', () => {
        admin.deactivate();
        expect(admin.isActive()).toBe(false);
      });
    });

    describe('canPerformAdminActions', () => {
      it('should return true for active admin', () => {
        expect(admin.canPerformAdminActions()).toBe(true);
      });

      it('should return false for inactive admin', () => {
        admin.deactivate();
        expect(admin.canPerformAdminActions()).toBe(false);
      });
    });

    describe('canPerformSuperAdminActions', () => {
      it('should return true for active super admin', () => {
        expect(superAdmin.canPerformSuperAdminActions()).toBe(true);
      });

      it('should return false for regular admin', () => {
        expect(admin.canPerformSuperAdminActions()).toBe(false);
      });

      it('should return false for inactive super admin', () => {
        superAdmin.deactivate();
        expect(superAdmin.canPerformSuperAdminActions()).toBe(false);
      });
    });
  });

  describe('getLastLoginDescription', () => {
    let admin: AdminUser;

    beforeEach(async () => {
      admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';
    });

    it('should return "从未登录" for never logged in admin', () => {
      expect(admin.getLastLoginDescription()).toBe('从未登录');
    });

    it('should return "今天" for today login', () => {
      admin.last_login_at = new Date();
      expect(admin.getLastLoginDescription()).toBe('今天');
    });

    it('should return "昨天" for yesterday login', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      admin.last_login_at = yesterday;
      expect(admin.getLastLoginDescription()).toBe('昨天');
    });

    it('should return days ago for recent login', () => {
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
      admin.last_login_at = threeDaysAgo;
      expect(admin.getLastLoginDescription()).toBe('3天前');
    });
  });

  describe('toResponse', () => {
    it('should return admin data without password hash', async () => {
      const admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';

      const response = admin.toResponse();

      expect(response).toEqual({
        id: admin.id,
        username: admin.username,
        email: admin.email,
        full_name: admin.full_name,
        is_active: admin.is_active,
        is_super_admin: admin.is_super_admin,
        last_login_at: admin.last_login_at,
        created_at: admin.created_at,
        updated_at: admin.updated_at,
      });

      // Ensure password hash is not included
      expect(response).not.toHaveProperty('password_hash');
    });
  });

  describe('toDetailedResponse', () => {
    it('should return admin data with additional information', async () => {
      const admin = await AdminUser.create(validAdminData);
      admin.id = '123e4567-e89b-12d3-a456-426614174000';
      admin.recordLogin();

      const response = admin.toDetailedResponse();

      expect(response).toHaveProperty('id', admin.id);
      expect(response).toHaveProperty('username', admin.username);
      expect(response).toHaveProperty('last_login_description');
      expect(response).toHaveProperty('permissions');
      expect(response.permissions).toEqual({
        can_perform_admin_actions: true,
        can_perform_super_admin_actions: false,
      });
      expect(typeof response.last_login_description).toBe('string');
    });

    it('should return correct permissions for super admin', async () => {
      const superAdmin = await AdminUser.create({
        ...validAdminData,
        username: 'superadmin',
        is_super_admin: true,
      });
      superAdmin.id = '123e4567-e89b-12d3-a456-426614174001';

      const response = superAdmin.toDetailedResponse();

      expect(response.permissions).toEqual({
        can_perform_admin_actions: true,
        can_perform_super_admin_actions: true,
      });
    });
  });
});
