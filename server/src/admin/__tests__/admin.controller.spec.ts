import { Test, TestingModule } from '@nestjs/testing';
import { AdminController } from '../admin.controller';
import { AdminService } from '../admin.service';
import { AdminJwtAuthGuard } from '../../auth/guards/admin-jwt-auth.guard';
import { UpdateAdminDto } from '../dto/update-admin.dto';
import { AdminResponseDto } from '../dto/admin-response.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { IAdminUser } from '../../common/interfaces/entities';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { HttpStatus } from '@nestjs/common';

describe('AdminController', () => {
  let controller: AdminController;
  let adminService: jest.Mocked<AdminService>;

  const mockAdmin: IAdminUser = {
    id: 'admin-id-1',
    username: 'admin',
    password_hash: '$2b$10$hashedpassword',
    email: '<EMAIL>',
    full_name: 'System Administrator',
    is_active: true,
    is_super_admin: true,
    last_login_at: new Date('2024-01-01'),
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  const mockRegularAdmin: IAdminUser = {
    id: 'admin-id-2',
    username: 'regular_admin',
    password_hash: '$2b$10$hashedpassword',
    email: '<EMAIL>',
    full_name: 'Regular Administrator',
    is_active: true,
    is_super_admin: false,
    last_login_at: new Date('2024-01-01'),
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  const mockAdminResponse: AdminResponseDto = {
    id: 'admin-id-1',
    username: 'admin',
    email: '<EMAIL>',
    full_name: 'System Administrator',
    is_active: true,
    is_super_admin: true,
    last_login_at: new Date('2024-01-01'),
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        {
          provide: AdminService,
          useValue: {
            getProfile: jest.fn(),
            updateProfile: jest.fn(),
            changePassword: jest.fn(),
            getAdminList: jest.fn(),
            deactivateAdmin: jest.fn(),
            activateAdmin: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(AdminJwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AdminController>(AdminController);
    adminService = module.get(AdminService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getProfile', () => {
    it('should return admin profile successfully', async () => {
      // Arrange
      adminService.getProfile.mockResolvedValue(mockAdminResponse);

      // Act
      const result = await controller.getProfile(mockAdmin);

      // Assert
      expect(adminService.getProfile).toHaveBeenCalledWith(mockAdmin.id);
      expect(result).toEqual(mockAdminResponse);
    });

    it('should handle admin not found error', async () => {
      // Arrange
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.getProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getProfile(mockAdmin)).rejects.toThrow(error);
      expect(adminService.getProfile).toHaveBeenCalledWith(mockAdmin.id);
    });

    it('should handle service error', async () => {
      // Arrange
      const error = new Error('Service error');
      adminService.getProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getProfile(mockAdmin)).rejects.toThrow(error);
      expect(adminService.getProfile).toHaveBeenCalledWith(mockAdmin.id);
    });
  });

  describe('updateProfile', () => {
    it('should update admin profile successfully', async () => {
      // Arrange
      const updateAdminDto: UpdateAdminDto = {
        email: '<EMAIL>',
        full_name: 'Updated Administrator',
      };
      const updatedAdminResponse = {
        ...mockAdminResponse,
        email: '<EMAIL>',
        full_name: 'Updated Administrator',
      };
      adminService.updateProfile.mockResolvedValue(updatedAdminResponse);

      // Act
      const result = await controller.updateProfile(mockAdmin, updateAdminDto);

      // Assert
      expect(adminService.updateProfile).toHaveBeenCalledWith(
        mockAdmin.id,
        updateAdminDto,
      );
      expect(result).toEqual(updatedAdminResponse);
    });

    it('should update only email', async () => {
      // Arrange
      const updateAdminDto: UpdateAdminDto = {
        email: '<EMAIL>',
      };
      const updatedAdminResponse = {
        ...mockAdminResponse,
        email: '<EMAIL>',
      };
      adminService.updateProfile.mockResolvedValue(updatedAdminResponse);

      // Act
      const result = await controller.updateProfile(mockAdmin, updateAdminDto);

      // Assert
      expect(adminService.updateProfile).toHaveBeenCalledWith(
        mockAdmin.id,
        updateAdminDto,
      );
      expect(result).toEqual(updatedAdminResponse);
    });

    it('should update only full name', async () => {
      // Arrange
      const updateAdminDto: UpdateAdminDto = {
        full_name: 'Updated Administrator',
      };
      const updatedAdminResponse = {
        ...mockAdminResponse,
        full_name: 'Updated Administrator',
      };
      adminService.updateProfile.mockResolvedValue(updatedAdminResponse);

      // Act
      const result = await controller.updateProfile(mockAdmin, updateAdminDto);

      // Assert
      expect(adminService.updateProfile).toHaveBeenCalledWith(
        mockAdmin.id,
        updateAdminDto,
      );
      expect(result).toEqual(updatedAdminResponse);
    });

    it('should handle admin not found error', async () => {
      // Arrange
      const updateAdminDto: UpdateAdminDto = {
        email: '<EMAIL>',
      };
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.updateProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateProfile(mockAdmin, updateAdminDto),
      ).rejects.toThrow(error);
      expect(adminService.updateProfile).toHaveBeenCalledWith(
        mockAdmin.id,
        updateAdminDto,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const updateAdminDto: UpdateAdminDto = {
        email: '<EMAIL>',
      };
      const error = new Error('Service error');
      adminService.updateProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateProfile(mockAdmin, updateAdminDto),
      ).rejects.toThrow(error);
      expect(adminService.updateProfile).toHaveBeenCalledWith(
        mockAdmin.id,
        updateAdminDto,
      );
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      // Arrange
      const changePasswordDto: ChangePasswordDto = {
        current_password: 'currentpassword',
        new_password: 'newpassword123',
      };
      adminService.changePassword.mockResolvedValue();

      // Act
      const result = await controller.changePassword(
        mockAdmin,
        changePasswordDto,
      );

      // Assert
      expect(adminService.changePassword).toHaveBeenCalledWith(
        mockAdmin.id,
        changePasswordDto,
      );
      expect(result).toBeUndefined();
    });

    it('should handle incorrect current password error', async () => {
      // Arrange
      const changePasswordDto: ChangePasswordDto = {
        current_password: 'wrongpassword',
        new_password: 'newpassword123',
      };
      const error = new BusinessException(
        ErrorCode.INVALID_ADMIN_CREDENTIALS,
        'Current password is incorrect',
        HttpStatus.BAD_REQUEST,
      );
      adminService.changePassword.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.changePassword(mockAdmin, changePasswordDto),
      ).rejects.toThrow(error);
      expect(adminService.changePassword).toHaveBeenCalledWith(
        mockAdmin.id,
        changePasswordDto,
      );
    });

    it('should handle admin not found error', async () => {
      // Arrange
      const changePasswordDto: ChangePasswordDto = {
        current_password: 'currentpassword',
        new_password: 'newpassword123',
      };
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.changePassword.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.changePassword(mockAdmin, changePasswordDto),
      ).rejects.toThrow(error);
      expect(adminService.changePassword).toHaveBeenCalledWith(
        mockAdmin.id,
        changePasswordDto,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const changePasswordDto: ChangePasswordDto = {
        current_password: 'currentpassword',
        new_password: 'newpassword123',
      };
      const error = new Error('Service error');
      adminService.changePassword.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.changePassword(mockAdmin, changePasswordDto),
      ).rejects.toThrow(error);
      expect(adminService.changePassword).toHaveBeenCalledWith(
        mockAdmin.id,
        changePasswordDto,
      );
    });
  });

  describe('getAdminList', () => {
    it('should return admin list successfully for super admin', async () => {
      // Arrange
      const adminList = [mockAdminResponse];
      adminService.getAdminList.mockResolvedValue(adminList);

      // Act
      const result = await controller.getAdminList(mockAdmin);

      // Assert
      expect(adminService.getAdminList).toHaveBeenCalledWith(mockAdmin.id);
      expect(result).toEqual(adminList);
    });

    it('should handle access denied error for regular admin', async () => {
      // Arrange
      const error = new BusinessException(
        ErrorCode.ADMIN_ACCESS_DENIED,
        'Only super admins can view admin list',
        HttpStatus.FORBIDDEN,
      );
      adminService.getAdminList.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getAdminList(mockRegularAdmin)).rejects.toThrow(
        error,
      );
      expect(adminService.getAdminList).toHaveBeenCalledWith(
        mockRegularAdmin.id,
      );
    });

    it('should handle admin not found error', async () => {
      // Arrange
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.getAdminList.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getAdminList(mockAdmin)).rejects.toThrow(error);
      expect(adminService.getAdminList).toHaveBeenCalledWith(mockAdmin.id);
    });

    it('should handle service error', async () => {
      // Arrange
      const error = new Error('Service error');
      adminService.getAdminList.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getAdminList(mockAdmin)).rejects.toThrow(error);
      expect(adminService.getAdminList).toHaveBeenCalledWith(mockAdmin.id);
    });
  });

  describe('deactivateAdmin', () => {
    it('should deactivate admin successfully', async () => {
      // Arrange
      const targetAdminId = 'admin-id-2';
      const deactivatedAdminResponse = {
        ...mockAdminResponse,
        id: targetAdminId,
        is_active: false,
      };
      adminService.deactivateAdmin.mockResolvedValue(deactivatedAdminResponse);

      // Act
      const result = await controller.deactivateAdmin(mockAdmin, targetAdminId);

      // Assert
      expect(adminService.deactivateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
      expect(result).toEqual(deactivatedAdminResponse);
    });

    it('should handle access denied error for regular admin', async () => {
      // Arrange
      const targetAdminId = 'admin-id-3';
      const error = new BusinessException(
        ErrorCode.ADMIN_ACCESS_DENIED,
        'Only super admins can deactivate admin accounts',
        HttpStatus.FORBIDDEN,
      );
      adminService.deactivateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.deactivateAdmin(mockRegularAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.deactivateAdmin).toHaveBeenCalledWith(
        mockRegularAdmin.id,
        targetAdminId,
      );
    });

    it('should handle self-deactivation error', async () => {
      // Arrange
      const error = new BusinessException(
        ErrorCode.ADMIN_ACCESS_DENIED,
        'Cannot deactivate your own account',
        HttpStatus.BAD_REQUEST,
      );
      adminService.deactivateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.deactivateAdmin(mockAdmin, mockAdmin.id),
      ).rejects.toThrow(error);
      expect(adminService.deactivateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        mockAdmin.id,
      );
    });

    it('should handle target admin not found error', async () => {
      // Arrange
      const targetAdminId = 'non-existent-admin-id';
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Target admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.deactivateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.deactivateAdmin(mockAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.deactivateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const targetAdminId = 'admin-id-2';
      const error = new Error('Service error');
      adminService.deactivateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.deactivateAdmin(mockAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.deactivateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
    });
  });

  describe('activateAdmin', () => {
    it('should activate admin successfully', async () => {
      // Arrange
      const targetAdminId = 'admin-id-2';
      const activatedAdminResponse = {
        ...mockAdminResponse,
        id: targetAdminId,
        is_active: true,
      };
      adminService.activateAdmin.mockResolvedValue(activatedAdminResponse);

      // Act
      const result = await controller.activateAdmin(mockAdmin, targetAdminId);

      // Assert
      expect(adminService.activateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
      expect(result).toEqual(activatedAdminResponse);
    });

    it('should handle access denied error for regular admin', async () => {
      // Arrange
      const targetAdminId = 'admin-id-3';
      const error = new BusinessException(
        ErrorCode.ADMIN_ACCESS_DENIED,
        'Only super admins can activate admin accounts',
        HttpStatus.FORBIDDEN,
      );
      adminService.activateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.activateAdmin(mockRegularAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.activateAdmin).toHaveBeenCalledWith(
        mockRegularAdmin.id,
        targetAdminId,
      );
    });

    it('should handle target admin not found error', async () => {
      // Arrange
      const targetAdminId = 'non-existent-admin-id';
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Target admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.activateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.activateAdmin(mockAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.activateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
    });

    it('should handle requesting admin not found error', async () => {
      // Arrange
      const targetAdminId = 'admin-id-2';
      const error = new BusinessException(
        ErrorCode.ADMIN_NOT_FOUND,
        'Requesting admin not found',
        HttpStatus.NOT_FOUND,
      );
      adminService.activateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.activateAdmin(mockAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.activateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const targetAdminId = 'admin-id-2';
      const error = new Error('Service error');
      adminService.activateAdmin.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.activateAdmin(mockAdmin, targetAdminId),
      ).rejects.toThrow(error);
      expect(adminService.activateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        targetAdminId,
      );
    });
  });
});
