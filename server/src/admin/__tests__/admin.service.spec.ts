import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AdminService } from '../admin.service';
import { AdminRepository } from '../admin.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { IAdminUser } from '../../common/interfaces/entities';
import { UpdateAdminDto } from '../dto/update-admin.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import * as bcrypt from 'bcrypt';

// Mock bcrypt
jest.mock('bcrypt', () => ({
  compare: jest.fn(),
  hash: jest.fn(),
}));

describe('AdminService', () => {
  let service: AdminService;
  let adminRepository: jest.Mocked<AdminRepository>;

  const mockAdmin: IAdminUser = {
    id: 'admin-1',
    username: 'testadmin',
    password_hash: 'hashed_password',
    email: '<EMAIL>',
    full_name: 'Test Admin',
    is_active: true,
    is_super_admin: false,
    last_login_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockSuperAdmin: IAdminUser = {
    ...mockAdmin,
    id: 'super-admin-1',
    username: 'superadmin',
    is_super_admin: true,
  };

  beforeEach(async () => {
    const mockAdminRepository = {
      findById: jest.fn(),
      updateAdmin: jest.fn(),
      updatePassword: jest.fn(),
      findActiveAdmins: jest.fn(),
      deactivateAdmin: jest.fn(),
      activateAdmin: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: AdminRepository,
          useValue: mockAdminRepository,
        },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
    adminRepository = module.get(AdminRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getProfile', () => {
    it('should return admin profile successfully', async () => {
      adminRepository.findById.mockResolvedValue(mockAdmin);

      const result = await service.getProfile(mockAdmin.id);

      expect(result).toEqual({
        id: mockAdmin.id,
        username: mockAdmin.username,
        email: mockAdmin.email,
        full_name: mockAdmin.full_name,
        is_active: mockAdmin.is_active,
        is_super_admin: mockAdmin.is_super_admin,
        last_login_at: mockAdmin.last_login_at,
        created_at: mockAdmin.created_at,
        updated_at: mockAdmin.updated_at,
      });
      expect(adminRepository.findById).toHaveBeenCalledWith(mockAdmin.id);
    });

    it('should throw BusinessException when admin not found', async () => {
      adminRepository.findById.mockResolvedValue(null);

      await expect(service.getProfile('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw HttpException on repository error', async () => {
      adminRepository.findById.mockRejectedValue(new Error('Database error'));

      await expect(service.getProfile(mockAdmin.id)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('updateProfile', () => {
    const updateData: UpdateAdminDto = {
      email: '<EMAIL>',
      full_name: 'Updated Name',
    };

    it('should update admin profile successfully', async () => {
      const updatedAdmin = { ...mockAdmin, ...updateData };
      adminRepository.findById.mockResolvedValue(mockAdmin);
      adminRepository.updateAdmin.mockResolvedValue(updatedAdmin);

      const result = await service.updateProfile(mockAdmin.id, updateData);

      expect(result.email).toBe(updateData.email);
      expect(result.full_name).toBe(updateData.full_name);
      expect(adminRepository.findById).toHaveBeenCalledWith(mockAdmin.id);
      expect(adminRepository.updateAdmin).toHaveBeenCalledWith(
        mockAdmin.id,
        updateData,
      );
    });

    it('should throw BusinessException when admin not found', async () => {
      adminRepository.findById.mockResolvedValue(null);

      await expect(
        service.updateProfile('non-existent-id', updateData),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('changePassword', () => {
    const changePasswordDto: ChangePasswordDto = {
      current_password: 'current_password',
      new_password: 'new_password',
    };

    it('should change password successfully', async () => {
      adminRepository.findById.mockResolvedValue(mockAdmin);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      adminRepository.updatePassword.mockResolvedValue(mockAdmin);

      await service.changePassword(mockAdmin.id, changePasswordDto);

      expect(adminRepository.findById).toHaveBeenCalledWith(mockAdmin.id);
      expect(bcrypt.compare).toHaveBeenCalledWith(
        changePasswordDto.current_password,
        mockAdmin.password_hash,
      );
      expect(adminRepository.updatePassword).toHaveBeenCalledWith(
        mockAdmin.id,
        changePasswordDto.new_password,
      );
    });

    it('should throw BusinessException when admin not found', async () => {
      adminRepository.findById.mockResolvedValue(null);

      await expect(
        service.changePassword('non-existent-id', changePasswordDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw BusinessException when current password is incorrect', async () => {
      adminRepository.findById.mockResolvedValue(mockAdmin);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      await expect(
        service.changePassword(mockAdmin.id, changePasswordDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_ADMIN_CREDENTIALS,
          'Current password is incorrect',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('getAdminList', () => {
    it('should return admin list for super admin', async () => {
      const adminList = [mockAdmin, mockSuperAdmin];
      adminRepository.findById.mockResolvedValue(mockSuperAdmin);
      adminRepository.findActiveAdmins.mockResolvedValue(adminList);

      const result = await service.getAdminList(mockSuperAdmin.id);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(mockAdmin.id);
      expect(result[1].id).toBe(mockSuperAdmin.id);
      expect(adminRepository.findById).toHaveBeenCalledWith(mockSuperAdmin.id);
      expect(adminRepository.findActiveAdmins).toHaveBeenCalled();
    });

    it('should throw BusinessException when requesting admin is not super admin', async () => {
      adminRepository.findById.mockResolvedValue(mockAdmin);

      await expect(service.getAdminList(mockAdmin.id)).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Only super admins can view admin list',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should throw BusinessException when requesting admin not found', async () => {
      adminRepository.findById.mockResolvedValue(null);

      await expect(service.getAdminList('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('deactivateAdmin', () => {
    const targetAdminId = 'target-admin-id';

    it('should deactivate admin successfully', async () => {
      const targetAdmin = { ...mockAdmin, id: targetAdminId };
      const deactivatedAdmin = { ...targetAdmin, is_active: false };

      adminRepository.findById
        .mockResolvedValueOnce(mockSuperAdmin) // requesting admin
        .mockResolvedValueOnce(targetAdmin); // target admin
      adminRepository.deactivateAdmin.mockResolvedValue(deactivatedAdmin);

      const result = await service.deactivateAdmin(
        mockSuperAdmin.id,
        targetAdminId,
      );

      expect(result.is_active).toBe(false);
      expect(adminRepository.findById).toHaveBeenCalledWith(mockSuperAdmin.id);
      expect(adminRepository.findById).toHaveBeenCalledWith(targetAdminId);
      expect(adminRepository.deactivateAdmin).toHaveBeenCalledWith(
        targetAdminId,
      );
    });

    it('should throw BusinessException when requesting admin is not super admin', async () => {
      adminRepository.findById.mockResolvedValue(mockAdmin);

      await expect(
        service.deactivateAdmin(mockAdmin.id, targetAdminId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Only super admins can deactivate admin accounts',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should throw BusinessException when trying to deactivate own account', async () => {
      adminRepository.findById.mockResolvedValue(mockSuperAdmin);

      await expect(
        service.deactivateAdmin(mockSuperAdmin.id, mockSuperAdmin.id),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Cannot deactivate your own account',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it('should throw BusinessException when target admin not found', async () => {
      adminRepository.findById
        .mockResolvedValueOnce(mockSuperAdmin)
        .mockResolvedValueOnce(null);

      await expect(
        service.deactivateAdmin(mockSuperAdmin.id, targetAdminId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Target admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('activateAdmin', () => {
    const targetAdminId = 'target-admin-id';

    it('should activate admin successfully', async () => {
      const targetAdmin = { ...mockAdmin, id: targetAdminId, is_active: false };
      const activatedAdmin = { ...targetAdmin, is_active: true };

      adminRepository.findById
        .mockResolvedValueOnce(mockSuperAdmin) // requesting admin
        .mockResolvedValueOnce(targetAdmin); // target admin
      adminRepository.activateAdmin.mockResolvedValue(activatedAdmin);

      const result = await service.activateAdmin(
        mockSuperAdmin.id,
        targetAdminId,
      );

      expect(result.is_active).toBe(true);
      expect(adminRepository.findById).toHaveBeenCalledWith(mockSuperAdmin.id);
      expect(adminRepository.findById).toHaveBeenCalledWith(targetAdminId);
      expect(adminRepository.activateAdmin).toHaveBeenCalledWith(targetAdminId);
    });

    it('should throw BusinessException when requesting admin is not super admin', async () => {
      adminRepository.findById.mockResolvedValue(mockAdmin);

      await expect(
        service.activateAdmin(mockAdmin.id, targetAdminId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Only super admins can activate admin accounts',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should throw BusinessException when target admin not found', async () => {
      adminRepository.findById
        .mockResolvedValueOnce(mockSuperAdmin)
        .mockResolvedValueOnce(null);

      await expect(
        service.activateAdmin(mockSuperAdmin.id, targetAdminId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Target admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });
});
