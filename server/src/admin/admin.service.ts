import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { AdminRepository } from './admin.repository';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';
import { IAdminUser } from '../common/interfaces/entities';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { AdminResponseDto } from './dto/admin-response.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminService {
  constructor(private readonly adminRepository: AdminRepository) {}

  /**
   * 获取管理员资料
   */
  async getProfile(adminId: string): Promise<AdminResponseDto> {
    try {
      const admin = await this.adminRepository.findById(adminId);
      if (!admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      return new AdminResponseDto(admin);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to get admin profile',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 更新管理员资料
   */
  async updateProfile(
    adminId: string,
    updateData: UpdateAdminDto,
  ): Promise<AdminResponseDto> {
    try {
      const admin = await this.adminRepository.findById(adminId);
      if (!admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const updatedAdmin = await this.adminRepository.updateAdmin(
        adminId,
        updateData,
      );
      return new AdminResponseDto(updatedAdmin);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to update admin profile',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 修改管理员密码
   */
  async changePassword(
    adminId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    try {
      const admin = await this.adminRepository.findById(adminId);
      if (!admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(
        changePasswordDto.current_password,
        admin.password_hash,
      );

      if (!isCurrentPasswordValid) {
        throw new BusinessException(
          ErrorCode.INVALID_ADMIN_CREDENTIALS,
          'Current password is incorrect',
          HttpStatus.BAD_REQUEST,
        );
      }

      // 更新密码
      await this.adminRepository.updatePassword(
        adminId,
        changePasswordDto.new_password,
      );
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to change admin password',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取管理员列表（仅超级管理员可用）
   */
  async getAdminList(requestingAdminId: string): Promise<AdminResponseDto[]> {
    try {
      const requestingAdmin =
        await this.adminRepository.findById(requestingAdminId);
      if (!requestingAdmin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      if (!requestingAdmin.is_super_admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Only super admins can view admin list',
          HttpStatus.FORBIDDEN,
        );
      }

      const admins = await this.adminRepository.findActiveAdmins();
      return admins.map((admin) => new AdminResponseDto(admin));
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to get admin list',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 停用管理员账户（仅超级管理员可用）
   */
  async deactivateAdmin(
    requestingAdminId: string,
    targetAdminId: string,
  ): Promise<AdminResponseDto> {
    try {
      const requestingAdmin =
        await this.adminRepository.findById(requestingAdminId);
      if (!requestingAdmin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Requesting admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      if (!requestingAdmin.is_super_admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Only super admins can deactivate admin accounts',
          HttpStatus.FORBIDDEN,
        );
      }

      // 不能停用自己的账户
      if (requestingAdminId === targetAdminId) {
        throw new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Cannot deactivate your own account',
          HttpStatus.BAD_REQUEST,
        );
      }

      const targetAdmin = await this.adminRepository.findById(targetAdminId);
      if (!targetAdmin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Target admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const deactivatedAdmin =
        await this.adminRepository.deactivateAdmin(targetAdminId);
      return new AdminResponseDto(deactivatedAdmin);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to deactivate admin',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 激活管理员账户（仅超级管理员可用）
   */
  async activateAdmin(
    requestingAdminId: string,
    targetAdminId: string,
  ): Promise<AdminResponseDto> {
    try {
      const requestingAdmin =
        await this.adminRepository.findById(requestingAdminId);
      if (!requestingAdmin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Requesting admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      if (!requestingAdmin.is_super_admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Only super admins can activate admin accounts',
          HttpStatus.FORBIDDEN,
        );
      }

      const targetAdmin = await this.adminRepository.findById(targetAdminId);
      if (!targetAdmin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Target admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const activatedAdmin =
        await this.adminRepository.activateAdmin(targetAdminId);
      return new AdminResponseDto(activatedAdmin);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to activate admin',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
