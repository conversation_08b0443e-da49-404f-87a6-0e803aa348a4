import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { UserRepository } from './user.repository';
import { BabyRepository } from '../baby/baby.repository';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';
import {
  IUser,
  IUpdateUserDto,
  IUserResponse,
  IBaby,
} from '../common/interfaces/entities';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly babyRepository: BabyRepository,
  ) {}

  /**
   * 获取用户资料
   */
  async getUserProfile(userId: string): Promise<UserResponseDto> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new BusinessException(
          ErrorCode.USER_NOT_FOUND,
          'User not found',
          HttpStatus.NOT_FOUND,
        );
      }

      // 如果用户有默认宝宝，获取默认宝宝信息
      let defaultBaby: IBaby | undefined = undefined;
      if (user.default_baby_id) {
        const baby = await this.babyRepository.findById(user.default_baby_id);
        if (baby) {
          defaultBaby = baby;
        }
      }

      return {
        id: user.id,
        openid: user.openid,
        username: user.username,
        phone: user.phone,
        avatar_url: user.avatar_url,
        default_baby_id: user.default_baby_id,
        default_baby: defaultBaby,
        created_at: user.created_at,
        updated_at: user.updated_at,
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to get user profile',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 更新用户资料
   */
  async updateUserProfile(
    userId: string,
    updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    try {
      // 验证用户是否存在
      const existingUser = await this.userRepository.findById(userId);
      if (!existingUser) {
        throw new BusinessException(
          ErrorCode.USER_NOT_FOUND,
          'User not found',
          HttpStatus.NOT_FOUND,
        );
      }

      // 如果更新用户名，检查用户名是否已被占用
      if (
        updateUserDto.username &&
        updateUserDto.username !== existingUser.username
      ) {
        const userWithSameUsername = await this.userRepository.findByUsername(
          updateUserDto.username,
        );
        if (userWithSameUsername && userWithSameUsername.id !== userId) {
          throw new BusinessException(
            ErrorCode.USERNAME_TAKEN,
            'Username is already taken',
            HttpStatus.CONFLICT,
          );
        }
      }

      // 如果设置默认宝宝，验证宝宝是否存在且用户有权限访问
      if (updateUserDto.default_baby_id) {
        const baby = await this.babyRepository.findById(
          updateUserDto.default_baby_id,
        );
        if (!baby) {
          throw new BusinessException(
            ErrorCode.BABY_NOT_FOUND,
            'Baby not found',
            HttpStatus.NOT_FOUND,
          );
        }

        // 检查用户是否有权限访问该宝宝（是所有者或家庭成员）
        const hasAccess = await this.babyRepository.checkBabyAccess(
          updateUserDto.default_baby_id,
          userId,
        );
        if (!hasAccess) {
          throw new BusinessException(
            ErrorCode.BABY_ACCESS_DENIED,
            'Access denied to baby profile',
            HttpStatus.FORBIDDEN,
          );
        }
      }

      // 更新用户资料
      const updateData: IUpdateUserDto = {
        username: updateUserDto.username,
        phone: updateUserDto.phone,
        avatar_url: updateUserDto.avatar_url,
        default_baby_id: updateUserDto.default_baby_id,
      };

      const updatedUser = await this.userRepository.updateUser(
        userId,
        updateData,
      );

      // 获取默认宝宝信息
      let defaultBaby: IBaby | undefined = undefined;
      if (updatedUser.default_baby_id) {
        const baby = await this.babyRepository.findById(
          updatedUser.default_baby_id,
        );
        if (baby) {
          defaultBaby = baby;
        }
      }

      return {
        id: updatedUser.id,
        openid: updatedUser.openid,
        username: updatedUser.username,
        phone: updatedUser.phone,
        avatar_url: updatedUser.avatar_url,
        default_baby_id: updatedUser.default_baby_id,
        default_baby: defaultBaby,
        created_at: updatedUser.created_at,
        updated_at: updatedUser.updated_at,
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Failed to update user profile',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 验证用户名格式
   */
  private validateUsername(username: string): boolean {
    // 用户名长度2-20字符，支持中文、英文、数字、下划线
    const usernameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/;
    return usernameRegex.test(username);
  }

  /**
   * 验证手机号格式
   */
  private validatePhoneNumber(phone: string): boolean {
    // 中国大陆手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }
}
