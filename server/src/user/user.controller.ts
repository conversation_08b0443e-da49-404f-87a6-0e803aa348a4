import {
  Controller,
  Get,
  Put,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { UserService } from './user.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { IUser } from '../common/interfaces/entities';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * 获取用户资料
   */
  @Get('profile')
  async getUserProfile(
    @Request() req: { user: IUser },
  ): Promise<UserResponseDto> {
    return this.userService.getUserProfile(req.user.id);
  }

  /**
   * 更新用户资料
   */
  @Put('updateProfile')
  @HttpCode(HttpStatus.OK)
  async updateUserProfile(
    @Request() req: { user: IUser },
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    return this.userService.updateUserProfile(req.user.id, updateUserDto);
  }
}
