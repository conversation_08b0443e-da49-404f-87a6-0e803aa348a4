import { IUser, ICreateUserDto, IUpdateUserDto } from '../../common/interfaces';
import { UserValidator } from '../../common/validators';

export class User implements IUser {
  id: string;
  openid: string;
  username?: string;
  phone?: string;
  avatar_url?: string;
  default_baby_id?: string; // 默认展示的宝宝ID
  created_at: Date;
  updated_at: Date;

  constructor(partial: Partial<User>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新用户实例
   */
  static create(data: ICreateUserDto): User {
    const validation = UserValidator.validateUserData(data);
    if (!validation.isValid) {
      throw new Error(`Invalid user data: ${validation.errors.join(', ')}`);
    }

    const now = new Date();
    return new User({
      id: '', // 将由数据库生成
      openid: data.openid,
      username: data.username,
      phone: data.phone,
      avatar_url: data.avatar_url,
      created_at: now,
      updated_at: now,
    });
  }

  /**
   * 更新用户信息
   */
  update(data: IUpdateUserDto): void {
    // 验证更新数据
    const updateData = {
      openid: this.openid, // 保持原有openid用于验证
      username: data.username ?? this.username,
      phone: data.phone ?? this.phone,
      avatar_url: data.avatar_url ?? this.avatar_url,
    };

    const validation = UserValidator.validateUserData(updateData);
    if (!validation.isValid) {
      throw new Error(`Invalid update data: ${validation.errors.join(', ')}`);
    }

    // 应用更新
    if (data.username !== undefined) this.username = data.username;
    if (data.phone !== undefined) this.phone = data.phone;
    if (data.avatar_url !== undefined) this.avatar_url = data.avatar_url;
    if (data.default_baby_id !== undefined)
      this.default_baby_id = data.default_baby_id;

    this.updated_at = new Date();
  }

  /**
   * 验证用户数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return UserValidator.validateUserData({
      openid: this.openid,
      username: this.username,
      phone: this.phone,
      avatar_url: this.avatar_url,
    });
  }

  /**
   * 检查用户是否已完善个人资料
   */
  isProfileComplete(): boolean {
    return !!(this.username && this.phone && this.avatar_url);
  }

  /**
   * 设置默认宝宝
   */
  setDefaultBaby(babyId: string | null): void {
    this.default_baby_id = babyId || undefined;
    this.updated_at = new Date();
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): Pick<
    User,
    | 'id'
    | 'openid'
    | 'username'
    | 'phone'
    | 'avatar_url'
    | 'default_baby_id'
    | 'created_at'
    | 'updated_at'
  > {
    return {
      id: this.id,
      openid: this.openid,
      username: this.username,
      phone: this.phone,
      avatar_url: this.avatar_url,
      default_baby_id: this.default_baby_id,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }
}
