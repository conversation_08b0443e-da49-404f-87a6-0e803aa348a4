import { User } from '../user.entity';
import { ICreateUserDto, IUpdateUserDto } from '../../../common/interfaces';

describe('User Entity', () => {
  const validUserData: ICreateUserDto = {
    openid: 'valid_openid_123',
    username: 'testuser',
    phone: '13812345678',
    avatar_url: 'https://example.com/avatar.jpg',
  };

  describe('constructor', () => {
    it('should create user instance with partial data', () => {
      const userData = {
        id: 'user-123',
        openid: 'test_openid',
        username: 'testuser',
        created_at: new Date(),
        updated_at: new Date(),
      };

      const user = new User(userData);

      expect(user.id).toBe(userData.id);
      expect(user.openid).toBe(userData.openid);
      expect(user.username).toBe(userData.username);
      expect(user.created_at).toBe(userData.created_at);
      expect(user.updated_at).toBe(userData.updated_at);
    });
  });

  describe('create', () => {
    it('should create user with valid data', () => {
      const user = User.create(validUserData);

      expect(user.openid).toBe(validUserData.openid);
      expect(user.username).toBe(validUserData.username);
      expect(user.phone).toBe(validUserData.phone);
      expect(user.avatar_url).toBe(validUserData.avatar_url);
      expect(user.created_at).toBeInstanceOf(Date);
      expect(user.updated_at).toBeInstanceOf(Date);
    });

    it('should create user with minimal data', () => {
      const minimalData: ICreateUserDto = {
        openid: 'valid_openid_123',
      };

      const user = User.create(minimalData);

      expect(user.openid).toBe(minimalData.openid);
      expect(user.username).toBeUndefined();
      expect(user.phone).toBeUndefined();
      expect(user.avatar_url).toBeUndefined();
    });

    it('should throw error for invalid data', () => {
      const invalidData: ICreateUserDto = {
        openid: '', // Invalid empty openid
        username: 'a'.repeat(51), // Too long username
        phone: '123', // Invalid phone
        avatar_url: 'invalid-url', // Invalid URL
      };

      expect(() => User.create(invalidData)).toThrow('Invalid user data');
    });
  });

  describe('update', () => {
    let user: User;

    beforeEach(() => {
      user = User.create(validUserData);
      user.id = 'user-123';
    });

    it('should update user with valid data', () => {
      const updateData: IUpdateUserDto = {
        username: 'newusername',
        phone: '13987654321',
        avatar_url: 'https://example.com/new-avatar.jpg',
      };

      const originalUpdatedAt = user.updated_at;

      user.update(updateData);

      expect(user.username).toBe(updateData.username);
      expect(user.phone).toBe(updateData.phone);
      expect(user.avatar_url).toBe(updateData.avatar_url);
      expect(user.updated_at).not.toBe(originalUpdatedAt);
    });

    it('should update only provided fields', () => {
      const originalUsername = user.username;
      const originalPhone = user.phone;

      const updateData: IUpdateUserDto = {
        avatar_url: 'https://example.com/new-avatar.jpg',
      };

      user.update(updateData);

      expect(user.username).toBe(originalUsername);
      expect(user.phone).toBe(originalPhone);
      expect(user.avatar_url).toBe(updateData.avatar_url);
    });

    it('should update default_baby_id', () => {
      const babyId = 'baby-123';

      const updateData: IUpdateUserDto = {
        default_baby_id: babyId,
      };

      user.update(updateData);

      expect(user.default_baby_id).toBe(babyId);
    });

    it('should throw error for invalid update data', () => {
      const invalidUpdateData: IUpdateUserDto = {
        username: 'a'.repeat(51), // Too long username
        phone: '123', // Invalid phone
      };

      expect(() => user.update(invalidUpdateData)).toThrow(
        'Invalid update data',
      );
    });
  });

  describe('validate', () => {
    it('should return valid for correct user data', () => {
      const user = User.create(validUserData);
      user.id = 'user-123';

      const result = user.validate();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect user data', () => {
      const user = new User({
        id: 'user-123',
        openid: '', // Invalid empty openid
        username: 'a'.repeat(51), // Too long username
        phone: '123', // Invalid phone
        avatar_url: 'invalid-url', // Invalid URL
        created_at: new Date(),
        updated_at: new Date(),
      });

      const result = user.validate();

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('isProfileComplete', () => {
    it('should return true for complete profile', () => {
      const user = User.create(validUserData);
      user.id = 'user-123';

      expect(user.isProfileComplete()).toBe(true);
    });

    it('should return false for incomplete profile', () => {
      const incompleteData: ICreateUserDto = {
        openid: 'valid_openid_123',
        username: 'testuser',
        // Missing phone and avatar_url
      };

      const user = User.create(incompleteData);
      user.id = 'user-123';

      expect(user.isProfileComplete()).toBe(false);
    });

    it('should return false when any required field is missing', () => {
      const user = User.create(validUserData);
      user.id = 'user-123';

      // Remove username
      user.username = undefined;
      expect(user.isProfileComplete()).toBe(false);

      // Restore username, remove phone
      user.username = 'testuser';
      user.phone = undefined;
      expect(user.isProfileComplete()).toBe(false);

      // Restore phone, remove avatar_url
      user.phone = '13812345678';
      user.avatar_url = undefined;
      expect(user.isProfileComplete()).toBe(false);
    });
  });

  describe('setDefaultBaby', () => {
    let user: User;

    beforeEach(() => {
      user = User.create(validUserData);
      user.id = 'user-123';
    });

    it('should set default baby ID', () => {
      const babyId = 'baby-123';
      const originalUpdatedAt = user.updated_at;

      user.setDefaultBaby(babyId);

      expect(user.default_baby_id).toBe(babyId);
      expect(user.updated_at).not.toBe(originalUpdatedAt);
    });

    it('should clear default baby ID when null is provided', () => {
      user.default_baby_id = 'baby-123';

      user.setDefaultBaby(null);

      expect(user.default_baby_id).toBeUndefined();
    });

    it('should clear default baby ID when empty string is provided', () => {
      user.default_baby_id = 'baby-123';

      user.setDefaultBaby('');

      expect(user.default_baby_id).toBeUndefined();
    });
  });

  describe('toResponse', () => {
    it('should return user data without methods', () => {
      const user = User.create(validUserData);
      user.id = 'user-123';

      const response = user.toResponse();

      expect(response).toEqual({
        id: user.id,
        openid: user.openid,
        username: user.username,
        phone: user.phone,
        avatar_url: user.avatar_url,
        default_baby_id: user.default_baby_id,
        created_at: user.created_at,
        updated_at: user.updated_at,
      });

      // Ensure it's a plain object with expected properties
      expect(Object.keys(response)).toEqual([
        'id',
        'openid',
        'username',
        'phone',
        'avatar_url',
        'default_baby_id',
        'created_at',
        'updated_at',
      ]);
    });
  });
});
