import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { UserService } from '../user.service';
import { UserRepository } from '../user.repository';
import { BabyRepository } from '../../baby/baby.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { IUser, IBaby } from '../../common/interfaces/entities';
import { UpdateUserDto } from '../dto/update-user.dto';

describe('UserService', () => {
  let service: UserService;
  let userRepository: jest.Mocked<UserRepository>;
  let babyRepository: jest.Mocked<BabyRepository>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: '***********',
    avatar_url: 'https://example.com/avatar.jpg',
    default_baby_id: 'baby-id-1',
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  const mockBaby: IBaby = {
    id: 'baby-id-1',
    owner_id: 'user-id-1',
    nickname: '小宝',
    birth_date: new Date('2023-01-01'),
    gender: 1,
    avatar_url: 'https://example.com/baby-avatar.jpg',
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: UserRepository,
          useValue: {
            findById: jest.fn(),
            findByUsername: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: BabyRepository,
          useValue: {
            findById: jest.fn(),
            checkBabyAccess: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    userRepository = module.get(UserRepository);
    babyRepository = module.get(BabyRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserProfile', () => {
    it('should return user profile without default baby', async () => {
      // Arrange
      const userWithoutBaby = { ...mockUser, default_baby_id: undefined };
      userRepository.findById.mockResolvedValue(userWithoutBaby);

      // Act
      const result = await service.getUserProfile(mockUser.id);

      // Assert
      expect(userRepository.findById).toHaveBeenCalledWith(mockUser.id);
      expect(babyRepository.findById).not.toHaveBeenCalled();
      expect(result).toEqual({
        id: userWithoutBaby.id,
        openid: userWithoutBaby.openid,
        username: userWithoutBaby.username,
        phone: userWithoutBaby.phone,
        avatar_url: userWithoutBaby.avatar_url,
        default_baby_id: userWithoutBaby.default_baby_id,
        default_baby: undefined,
        created_at: userWithoutBaby.created_at,
        updated_at: userWithoutBaby.updated_at,
      });
    });

    it('should return user profile with default baby', async () => {
      // Arrange
      userRepository.findById.mockResolvedValue(mockUser);
      babyRepository.findById.mockResolvedValue(mockBaby);

      // Act
      const result = await service.getUserProfile(mockUser.id);

      // Assert
      expect(userRepository.findById).toHaveBeenCalledWith(mockUser.id);
      expect(babyRepository.findById).toHaveBeenCalledWith(
        mockUser.default_baby_id,
      );
      expect(result).toEqual({
        id: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
        phone: mockUser.phone,
        avatar_url: mockUser.avatar_url,
        default_baby_id: mockUser.default_baby_id,
        default_baby: mockBaby,
        created_at: mockUser.created_at,
        updated_at: mockUser.updated_at,
      });
    });

    it('should throw error when user not found', async () => {
      // Arrange
      userRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getUserProfile('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_NOT_FOUND,
          'User not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should handle repository error', async () => {
      // Arrange
      userRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getUserProfile(mockUser.id)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('updateUserProfile', () => {
    const updateUserDto: UpdateUserDto = {
      username: 'newusername',
      phone: '***********',
      avatar_url: 'https://example.com/new-avatar.jpg',
    };

    it('should update user profile successfully', async () => {
      // Arrange
      const updatedUser = { ...mockUser, ...updateUserDto };
      userRepository.findById.mockResolvedValue(mockUser);
      userRepository.findByUsername.mockResolvedValue(null);
      userRepository.updateUser.mockResolvedValue(updatedUser);
      babyRepository.findById.mockResolvedValue(mockBaby);

      // Act
      const result = await service.updateUserProfile(
        mockUser.id,
        updateUserDto,
      );

      // Assert
      expect(userRepository.findById).toHaveBeenCalledWith(mockUser.id);
      expect(userRepository.findByUsername).toHaveBeenCalledWith(
        updateUserDto.username,
      );
      expect(userRepository.updateUser).toHaveBeenCalledWith(mockUser.id, {
        username: updateUserDto.username,
        phone: updateUserDto.phone,
        avatar_url: updateUserDto.avatar_url,
        default_baby_id: updateUserDto.default_baby_id,
      });
      expect(result.username).toBe(updateUserDto.username);
      expect(result.phone).toBe(updateUserDto.phone);
      expect(result.avatar_url).toBe(updateUserDto.avatar_url);
    });

    it('should update user profile with default baby', async () => {
      // Arrange
      const updateWithBaby = { ...updateUserDto, default_baby_id: 'baby-id-2' };
      const updatedUser = { ...mockUser, ...updateWithBaby };
      const newBaby = { ...mockBaby, id: 'baby-id-2' };

      userRepository.findById.mockResolvedValue(mockUser);
      userRepository.findByUsername.mockResolvedValue(null);
      userRepository.updateUser.mockResolvedValue(updatedUser);
      babyRepository.findById.mockResolvedValueOnce(newBaby); // For validation
      babyRepository.findById.mockResolvedValueOnce(newBaby); // For response
      babyRepository.checkBabyAccess.mockResolvedValue(true);

      // Act
      const result = await service.updateUserProfile(
        mockUser.id,
        updateWithBaby,
      );

      // Assert
      expect(babyRepository.findById).toHaveBeenCalledWith('baby-id-2');
      expect(babyRepository.checkBabyAccess).toHaveBeenCalledWith(
        'baby-id-2',
        mockUser.id,
      );
      expect(result.default_baby_id).toBe('baby-id-2');
      expect(result.default_baby).toEqual(newBaby);
    });

    it('should throw error when user not found', async () => {
      // Arrange
      userRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateUserProfile('non-existent-id', updateUserDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_NOT_FOUND,
          'User not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw error when username is already taken', async () => {
      // Arrange
      const existingUser = { ...mockUser, id: 'other-user-id' };
      userRepository.findById.mockResolvedValue(mockUser);
      userRepository.findByUsername.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(
        service.updateUserProfile(mockUser.id, updateUserDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.USERNAME_TAKEN,
          'Username is already taken',
          HttpStatus.CONFLICT,
        ),
      );
    });

    it('should allow updating to same username', async () => {
      // Arrange
      const sameUsernameDto = { username: mockUser.username };
      userRepository.findById.mockResolvedValue(mockUser);
      userRepository.updateUser.mockResolvedValue(mockUser);
      babyRepository.findById.mockResolvedValue(mockBaby);

      // Act
      const result = await service.updateUserProfile(
        mockUser.id,
        sameUsernameDto,
      );

      // Assert
      expect(userRepository.findByUsername).not.toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should throw error when default baby not found', async () => {
      // Arrange
      const updateWithBaby = { default_baby_id: 'non-existent-baby' };
      userRepository.findById.mockResolvedValue(mockUser);
      babyRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.updateUserProfile(mockUser.id, updateWithBaby),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_NOT_FOUND,
          'Baby not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw error when user has no access to baby', async () => {
      // Arrange
      const updateWithBaby = { default_baby_id: 'baby-id-2' };
      userRepository.findById.mockResolvedValue(mockUser);
      babyRepository.findById.mockResolvedValue(mockBaby);
      babyRepository.checkBabyAccess.mockResolvedValue(false);

      // Act & Assert
      await expect(
        service.updateUserProfile(mockUser.id, updateWithBaby),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Access denied to baby profile',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should handle repository error during update', async () => {
      // Arrange
      userRepository.findById.mockResolvedValue(mockUser);
      userRepository.findByUsername.mockResolvedValue(null);
      userRepository.updateUser.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(
        service.updateUserProfile(mockUser.id, updateUserDto),
      ).rejects.toThrow(HttpException);
    });
  });
});
