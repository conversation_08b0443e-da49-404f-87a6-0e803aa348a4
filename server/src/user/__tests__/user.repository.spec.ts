import { Test, TestingModule } from '@nestjs/testing';
import { UserRepository } from '../user.repository';
import { SupabaseService } from '../../database/supabase.service';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';

describe('UserRepository', () => {
  let repository: UserRepository;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockSupabaseQuery = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      query: jest.fn().mockReturnValue(mockSupabaseQuery),
      handleDatabaseError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    repository = module.get<UserRepository>(UserRepository);
    supabaseService = module.get(SupabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findByOpenid', () => {
    it('should find user by openid', async () => {
      const mockUser = {
        id: '123',
        openid: 'test-openid',
        username: 'testuser',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockSupabaseQuery.single.mockResolvedValue({
        data: mockUser,
        error: null,
      });

      const result = await repository.findByOpenid('test-openid');

      expect(result).toEqual(mockUser);
      expect(supabaseService.query).toHaveBeenCalledWith('users');
      expect(mockSupabaseQuery.eq).toHaveBeenCalledWith(
        'openid',
        'test-openid',
      );
    });

    it('should return null when user not found', async () => {
      mockSupabaseQuery.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116', message: 'No rows found' },
      });

      const result = await repository.findByOpenid('nonexistent-openid');

      expect(result).toBeNull();
    });
  });

  describe('findByPhone', () => {
    it('should find user by valid phone number', async () => {
      const mockUser = {
        id: '123',
        openid: 'test-openid',
        phone: '13812345678',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockSupabaseQuery.single.mockResolvedValue({
        data: mockUser,
        error: null,
      });

      const result = await repository.findByPhone('13812345678');

      expect(result).toEqual(mockUser);
      expect(mockSupabaseQuery.eq).toHaveBeenCalledWith('phone', '13812345678');
    });

    it('should return null for invalid phone format', async () => {
      const result = await repository.findByPhone('invalid-phone');

      expect(result).toBeNull();
      expect(supabaseService.query).not.toHaveBeenCalled();
    });
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      const userData = {
        openid: 'new-openid',
        username: 'newuser',
        phone: '13812345678',
      };

      const mockCreatedUser = {
        id: '123',
        ...userData,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock findByOpenid to return null (user doesn't exist)
      jest.spyOn(repository, 'findByOpenid').mockResolvedValue(null);
      jest.spyOn(repository, 'findByPhone').mockResolvedValue(null);

      mockSupabaseQuery.single.mockResolvedValue({
        data: mockCreatedUser,
        error: null,
      });

      const result = await repository.createUser(userData);

      expect(result).toEqual(mockCreatedUser);
      expect(repository.findByOpenid).toHaveBeenCalledWith('new-openid');
      expect(repository.findByPhone).toHaveBeenCalledWith('13812345678');
    });

    it('should throw error if openid already exists', async () => {
      const userData = {
        openid: 'existing-openid',
        username: 'newuser',
      };

      const existingUser = {
        id: '123',
        openid: 'existing-openid',
        username: 'existinguser',
        created_at: new Date(),
        updated_at: new Date(),
      };

      jest.spyOn(repository, 'findByOpenid').mockResolvedValue(existingUser);

      await expect(repository.createUser(userData)).rejects.toThrow(
        BusinessException,
      );
      await expect(repository.createUser(userData)).rejects.toThrow(
        'User with this openid already exists',
      );
    });

    it('should throw error for invalid phone format', async () => {
      const userData = {
        openid: 'new-openid',
        phone: 'invalid-phone',
      };

      jest.spyOn(repository, 'findByOpenid').mockResolvedValue(null);

      await expect(repository.createUser(userData)).rejects.toThrow(
        BusinessException,
      );
      await expect(repository.createUser(userData)).rejects.toThrow(
        'Invalid phone number format',
      );
    });
  });

  describe('validateCreateData', () => {
    it('should validate required fields', () => {
      expect(() => {
        (repository as any).validateCreateData({});
      }).toThrow(BusinessException);
    });

    it('should validate openid is not empty', () => {
      expect(() => {
        (repository as any).validateCreateData({ openid: '' });
      }).toThrow(BusinessException);
    });

    it('should pass validation for valid data', () => {
      expect(() => {
        (repository as any).validateCreateData({ openid: 'valid-openid' });
      }).not.toThrow();
    });
  });
});
