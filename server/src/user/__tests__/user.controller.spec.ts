import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from '../user.controller';
import { UserService } from '../user.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UpdateUserDto } from '../dto/update-user.dto';
import { UserResponseDto } from '../dto/user-response.dto';
import { IUser } from '../../common/interfaces/entities';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { HttpStatus } from '@nestjs/common';

describe('UserController', () => {
  let controller: UserController;
  let userService: jest.Mocked<UserService>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: '***********',
    avatar_url: 'https://example.com/avatar.jpg',
    default_baby_id: 'baby-id-1',
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  const mockUserResponse: UserResponseDto = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: '***********',
    avatar_url: 'https://example.com/avatar.jpg',
    default_baby_id: 'baby-id-1',
    default_baby: {
      id: 'baby-id-1',
      owner_id: 'user-id-1',
      nickname: 'Test Baby',
      birth_date: new Date('2023-01-01'),
      gender: 1,
      avatar_url: 'https://example.com/baby-avatar.jpg',
      is_default: true,
      created_at: new Date('2024-01-01'),
      updated_at: new Date('2024-01-01'),
    },
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: {
            getUserProfile: jest.fn(),
            updateUserProfile: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<UserController>(UserController);
    userService = module.get(UserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserProfile', () => {
    it('should return user profile successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      userService.getUserProfile.mockResolvedValue(mockUserResponse);

      // Act
      const result = await controller.getUserProfile(req);

      // Assert
      expect(userService.getUserProfile).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual(mockUserResponse);
    });

    it('should handle user not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const error = new BusinessException(
        ErrorCode.USER_NOT_FOUND,
        'User not found',
        HttpStatus.NOT_FOUND,
      );
      userService.getUserProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getUserProfile(req)).rejects.toThrow(error);
      expect(userService.getUserProfile).toHaveBeenCalledWith(mockUser.id);
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const error = new Error('Service error');
      userService.getUserProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getUserProfile(req)).rejects.toThrow(error);
      expect(userService.getUserProfile).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('updateUserProfile', () => {
    it('should update user profile successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {
        username: 'newusername',
        phone: '13900139000',
        avatar_url: 'https://example.com/new-avatar.jpg',
      };
      const updatedUserResponse: UserResponseDto = {
        ...mockUserResponse,
        username: 'newusername',
        phone: '13900139000',
        avatar_url: 'https://example.com/new-avatar.jpg',
      };
      userService.updateUserProfile.mockResolvedValue(updatedUserResponse);

      // Act
      const result = await controller.updateUserProfile(req, updateUserDto);

      // Assert
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
      expect(result).toEqual(updatedUserResponse);
    });

    it('should update default baby successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {
        default_baby_id: 'baby-id-2',
      };
      const updatedUserResponse: UserResponseDto = {
        ...mockUserResponse,
        default_baby_id: 'baby-id-2',
        default_baby: {
          id: 'baby-id-2',
          owner_id: 'user-id-1',
          nickname: 'New Default Baby',
          birth_date: new Date('2023-06-01'),
          gender: 2,
          avatar_url: 'https://example.com/baby2-avatar.jpg',
          is_default: true,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-01'),
        },
      };
      userService.updateUserProfile.mockResolvedValue(updatedUserResponse);

      // Act
      const result = await controller.updateUserProfile(req, updateUserDto);

      // Assert
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
      expect(result).toEqual(updatedUserResponse);
    });

    it('should handle username already taken error', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {
        username: 'existinguser',
      };
      const error = new BusinessException(
        ErrorCode.USERNAME_TAKEN,
        'Username is already taken',
        HttpStatus.CONFLICT,
      );
      userService.updateUserProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateUserProfile(req, updateUserDto),
      ).rejects.toThrow(error);
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
    });

    it('should handle baby not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {
        default_baby_id: 'non-existent-baby-id',
      };
      const error = new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby not found',
        HttpStatus.NOT_FOUND,
      );
      userService.updateUserProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateUserProfile(req, updateUserDto),
      ).rejects.toThrow(error);
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
    });

    it('should handle baby access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {
        default_baby_id: 'unauthorized-baby-id',
      };
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Access denied to baby profile',
        HttpStatus.FORBIDDEN,
      );
      userService.updateUserProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateUserProfile(req, updateUserDto),
      ).rejects.toThrow(error);
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {
        username: 'newusername',
      };
      const error = new Error('Service error');
      userService.updateUserProfile.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateUserProfile(req, updateUserDto),
      ).rejects.toThrow(error);
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
    });

    it('should handle empty update data', async () => {
      // Arrange
      const req = { user: mockUser };
      const updateUserDto: UpdateUserDto = {};
      userService.updateUserProfile.mockResolvedValue(mockUserResponse);

      // Act
      const result = await controller.updateUserProfile(req, updateUserDto);

      // Assert
      expect(userService.updateUserProfile).toHaveBeenCalledWith(
        mockUser.id,
        updateUserDto,
      );
      expect(result).toEqual(mockUserResponse);
    });
  });
});
