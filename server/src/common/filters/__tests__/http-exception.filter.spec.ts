import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ArgumentsHost } from '@nestjs/common';
import { HttpExceptionFilter } from '../http-exception.filter';
import { BusinessException } from '../../exceptions/business.exception';
import { ErrorCode } from '../../enums';

describe('HttpExceptionFilter', () => {
  let filter: HttpExceptionFilter;
  let mockResponse: any;
  let mockHost: ArgumentsHost;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [HttpExceptionFilter],
    }).compile();

    filter = module.get<HttpExceptionFilter>(HttpExceptionFilter);

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
      }),
    } as any;
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  describe('catch', () => {
    it('should handle BusinessException correctly', () => {
      const businessException = new BusinessException(
        ErrorCode.USER_NOT_FOUND,
        'User not found',
        HttpStatus.NOT_FOUND,
      );

      filter.catch(businessException, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.NOT_FOUND);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: ErrorCode.USER_NOT_FOUND,
          message: 'User not found',
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle HttpException with string message correctly', () => {
      const httpException = new HttpException(
        'Bad request',
        HttpStatus.BAD_REQUEST,
      );

      filter.catch(httpException, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 400000, // HTTP status code * 1000
          message: 'Bad request',
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle HttpException with object response correctly', () => {
      const httpException = new HttpException(
        { message: 'Validation failed', error: 'Bad Request' },
        HttpStatus.BAD_REQUEST,
      );

      filter.catch(httpException, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 400000,
          message: 'Validation failed',
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle HttpException with object response without message correctly', () => {
      const httpException = new HttpException(
        { error: 'Bad Request' },
        HttpStatus.BAD_REQUEST,
      );

      filter.catch(httpException, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 400000,
          message: 'Http Exception',
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle unknown exceptions correctly', () => {
      const unknownException = new Error('Something went wrong');

      filter.catch(unknownException, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 50000,
          message: 'Internal server error',
          timestamp: expect.any(String),
        },
      });
    });

    it('should generate valid timestamp', () => {
      const businessException = new BusinessException(
        ErrorCode.USER_NOT_FOUND,
        'User not found',
      );

      filter.catch(businessException, mockHost);

      const callArgs = mockResponse.json.mock.calls[0][0];
      const timestamp = callArgs.error.timestamp;

      expect(timestamp).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
      );
      expect(new Date(timestamp)).toBeInstanceOf(Date);
    });
  });
});
