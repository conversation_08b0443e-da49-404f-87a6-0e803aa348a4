import { Gender, FamilyRole } from '../enums';

// 用户数据验证
export class UserValidator {
  static validateOpenid(openid: string): boolean {
    return (
      typeof openid === 'string' && openid.length > 0 && openid.length <= 255
    );
  }

  static validateUsername(username?: string): boolean {
    if (!username) return true; // 可选字段
    return (
      typeof username === 'string' &&
      username.length > 0 &&
      username.length <= 50
    );
  }

  static validatePhone(phone?: string): boolean {
    if (!phone) return true; // 可选字段
    const phoneRegex = /^1[3-9]\d{9}$/; // 中国手机号格式
    return phoneRegex.test(phone);
  }

  static validateAvatarUrl(avatar_url?: string): boolean {
    if (!avatar_url) return true; // 可选字段
    try {
      new URL(avatar_url);
      return true;
    } catch {
      return false;
    }
  }

  static validateUserData(data: {
    openid: string;
    username?: string;
    phone?: string;
    avatar_url?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateOpenid(data.openid)) {
      errors.push('Invalid openid format');
    }

    if (!this.validateUsername(data.username)) {
      errors.push('Username must be between 1 and 50 characters');
    }

    if (!this.validatePhone(data.phone)) {
      errors.push('Invalid phone number format');
    }

    if (!this.validateAvatarUrl(data.avatar_url)) {
      errors.push('Invalid avatar URL format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 宝宝数据验证
export class BabyValidator {
  static validateNickname(nickname: string): boolean {
    return (
      typeof nickname === 'string' &&
      nickname.length > 0 &&
      nickname.length <= 50
    );
  }

  static validateBirthDate(birth_date: Date): boolean {
    const now = new Date();
    const birthDate = new Date(birth_date);

    // 出生日期不能是未来时间
    if (birthDate > now) return false;

    // 出生日期不能超过150年前
    const maxAge = new Date();
    maxAge.setFullYear(maxAge.getFullYear() - 150);
    if (birthDate < maxAge) return false;

    return true;
  }

  static validateGender(gender: number): boolean {
    return gender === Gender.MALE || gender === Gender.FEMALE;
  }

  static validateAvatarUrl(avatar_url?: string): boolean {
    if (!avatar_url) return true; // 可选字段
    try {
      new URL(avatar_url);
      return true;
    } catch {
      return false;
    }
  }

  static validateBabyData(data: {
    nickname: string;
    birth_date: Date;
    gender: number;
    avatar_url?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateNickname(data.nickname)) {
      errors.push('Nickname must be between 1 and 50 characters');
    }

    if (!this.validateBirthDate(data.birth_date)) {
      errors.push('Invalid birth date');
    }

    if (!this.validateGender(data.gender)) {
      errors.push('Invalid gender value');
    }

    if (!this.validateAvatarUrl(data.avatar_url)) {
      errors.push('Invalid avatar URL format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 家庭成员数据验证
export class FamilyMemberValidator {
  static validateRole(role: number): boolean {
    return (
      role === FamilyRole.FATHER ||
      role === FamilyRole.MOTHER ||
      role === FamilyRole.OTHER_CAREGIVER
    );
  }

  static validateUUID(id: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  static validateFamilyMemberData(data: {
    baby_id: string;
    user_id: string;
    role: number;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateUUID(data.baby_id)) {
      errors.push('Invalid baby_id format');
    }

    if (!this.validateUUID(data.user_id)) {
      errors.push('Invalid user_id format');
    }

    if (!this.validateRole(data.role)) {
      errors.push('Invalid family role');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 邀请数据验证
export class InvitationValidator {
  static validateInvitationCode(code: string): boolean {
    // 邀请码应该是10位字符串，包含字母和数字
    const codeRegex = /^[A-Z0-9]{10}$/;
    return codeRegex.test(code);
  }

  static validatePhone(phone?: string): boolean {
    if (!phone) return true; // 可选字段
    const phoneRegex = /^1[3-9]\d{9}$/; // 中国手机号格式
    return phoneRegex.test(phone);
  }

  static validateExpiresAt(expires_at: Date): boolean {
    const now = new Date();
    const expiryDate = new Date(expires_at);

    // 过期时间必须是未来时间
    return expiryDate > now;
  }

  static validateUUID(id: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  static validateInvitationData(data: {
    baby_id: string;
    inviter_id: string;
    invitation_code: string;
    phone?: string;
    expires_at: Date;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateUUID(data.baby_id)) {
      errors.push('Invalid baby_id format');
    }

    if (!this.validateUUID(data.inviter_id)) {
      errors.push('Invalid inviter_id format');
    }

    if (!this.validateInvitationCode(data.invitation_code)) {
      errors.push('Invalid invitation code format');
    }

    if (!this.validatePhone(data.phone)) {
      errors.push('Invalid phone number format');
    }

    if (!this.validateExpiresAt(data.expires_at)) {
      errors.push('Expiry date must be in the future');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  static generateInvitationCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 10; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// 管理员数据验证
export class AdminValidator {
  static validateUsername(username: string): boolean {
    return (
      typeof username === 'string' &&
      username.length >= 3 &&
      username.length <= 50
    );
  }

  static validatePassword(password: string): boolean {
    // 密码至少8位，包含字母和数字
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  }

  static validateEmail(email?: string): boolean {
    if (!email) return true; // 可选字段
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validateFullName(full_name?: string): boolean {
    if (!full_name) return true; // 可选字段
    return (
      typeof full_name === 'string' &&
      full_name.length > 0 &&
      full_name.length <= 100
    );
  }

  static validateAdminData(data: {
    username: string;
    password?: string;
    email?: string;
    full_name?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateUsername(data.username)) {
      errors.push('Username must be between 3 and 50 characters');
    }

    if (data.password && !this.validatePassword(data.password)) {
      errors.push(
        'Password must be at least 8 characters with letters and numbers',
      );
    }

    if (!this.validateEmail(data.email)) {
      errors.push('Invalid email format');
    }

    if (!this.validateFullName(data.full_name)) {
      errors.push('Full name must be between 1 and 100 characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 文件上传验证
export class FileValidator {
  static readonly ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
  ];
  static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

  static validateImageFile(file: { mimetype: string; size: number }): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!this.ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
      errors.push('Invalid file format. Only JPEG, PNG, and WebP are allowed');
    }

    if (file.size > this.MAX_FILE_SIZE) {
      errors.push('File size too large. Maximum size is 5MB');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  static generateFileName(userId: string, originalName: string): string {
    const timestamp = Date.now();
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
    return `${userId}/${timestamp}.${extension}`;
  }
}
