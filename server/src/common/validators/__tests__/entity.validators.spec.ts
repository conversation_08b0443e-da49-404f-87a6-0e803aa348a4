import {
  UserValidator,
  BabyValidator,
  FamilyMemberValidator,
  InvitationValidator,
  AdminValidator,
  FileValidator,
} from '../entity.validators';
import { Gender, FamilyRole } from '../../enums';

describe('UserValidator', () => {
  describe('validateOpenid', () => {
    it('should return true for valid openid', () => {
      expect(UserValidator.validateOpenid('valid_openid_123')).toBe(true);
    });

    it('should return false for empty openid', () => {
      expect(UserValidator.validateOpenid('')).toBe(false);
    });

    it('should return false for too long openid', () => {
      const longOpenid = 'a'.repeat(256);
      expect(UserValidator.validateOpenid(longOpenid)).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('should return true for valid phone number', () => {
      expect(UserValidator.validatePhone('13812345678')).toBe(true);
    });

    it('should return true for undefined phone', () => {
      expect(UserValidator.validatePhone(undefined)).toBe(true);
    });

    it('should return false for invalid phone number', () => {
      expect(UserValidator.validatePhone('12345678901')).toBe(false);
      expect(UserValidator.validatePhone('1381234567')).toBe(false);
    });
  });

  describe('validateUserData', () => {
    it('should validate correct user data', () => {
      const userData = {
        openid: 'valid_openid',
        username: 'testuser',
        phone: '13812345678',
        avatar_url: 'https://example.com/avatar.jpg',
      };

      const result = UserValidator.validateUserData(userData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid user data', () => {
      const userData = {
        openid: '',
        username: 'a'.repeat(51),
        phone: '12345',
        avatar_url: 'invalid-url',
      };

      const result = UserValidator.validateUserData(userData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});

describe('BabyValidator', () => {
  describe('validateNickname', () => {
    it('should return true for valid nickname', () => {
      expect(BabyValidator.validateNickname('小明')).toBe(true);
    });

    it('should return false for empty nickname', () => {
      expect(BabyValidator.validateNickname('')).toBe(false);
    });

    it('should return false for too long nickname', () => {
      const longNickname = 'a'.repeat(51);
      expect(BabyValidator.validateNickname(longNickname)).toBe(false);
    });
  });

  describe('validateBirthDate', () => {
    it('should return true for valid birth date', () => {
      const validDate = new Date('2020-01-01');
      expect(BabyValidator.validateBirthDate(validDate)).toBe(true);
    });

    it('should return false for future date', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);
      expect(BabyValidator.validateBirthDate(futureDate)).toBe(false);
    });

    it('should return false for too old date', () => {
      const oldDate = new Date('1800-01-01');
      expect(BabyValidator.validateBirthDate(oldDate)).toBe(false);
    });
  });

  describe('validateGender', () => {
    it('should return true for valid gender values', () => {
      expect(BabyValidator.validateGender(Gender.MALE)).toBe(true);
      expect(BabyValidator.validateGender(Gender.FEMALE)).toBe(true);
    });

    it('should return false for invalid gender values', () => {
      expect(BabyValidator.validateGender(0)).toBe(false);
      expect(BabyValidator.validateGender(3)).toBe(false);
    });
  });

  describe('validateBabyData', () => {
    it('should validate correct baby data', () => {
      const babyData = {
        nickname: '小明',
        birth_date: new Date('2020-01-01'),
        gender: Gender.MALE,
        avatar_url: 'https://example.com/baby.jpg',
      };

      const result = BabyValidator.validateBabyData(babyData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid baby data', () => {
      const babyData = {
        nickname: '',
        birth_date: new Date('2030-01-01'),
        gender: 0,
        avatar_url: 'invalid-url',
      };

      const result = BabyValidator.validateBabyData(babyData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});

describe('FamilyMemberValidator', () => {
  describe('validateRole', () => {
    it('should return true for valid family roles', () => {
      expect(FamilyMemberValidator.validateRole(FamilyRole.FATHER)).toBe(true);
      expect(FamilyMemberValidator.validateRole(FamilyRole.MOTHER)).toBe(true);
      expect(
        FamilyMemberValidator.validateRole(FamilyRole.OTHER_CAREGIVER),
      ).toBe(true);
    });

    it('should return false for invalid family roles', () => {
      expect(FamilyMemberValidator.validateRole(0)).toBe(false);
      expect(FamilyMemberValidator.validateRole(4)).toBe(false);
    });
  });

  describe('validateUUID', () => {
    it('should return true for valid UUID', () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      expect(FamilyMemberValidator.validateUUID(validUUID)).toBe(true);
    });

    it('should return false for invalid UUID', () => {
      expect(FamilyMemberValidator.validateUUID('invalid-uuid')).toBe(false);
      expect(FamilyMemberValidator.validateUUID('')).toBe(false);
    });
  });
});

describe('InvitationValidator', () => {
  describe('validateInvitationCode', () => {
    it('should return true for valid invitation code', () => {
      expect(InvitationValidator.validateInvitationCode('ABC1234567')).toBe(
        true,
      );
    });

    it('should return false for invalid invitation code', () => {
      expect(InvitationValidator.validateInvitationCode('abc123')).toBe(false);
      expect(InvitationValidator.validateInvitationCode('ABC123456789')).toBe(
        false,
      );
    });
  });

  describe('generateInvitationCode', () => {
    it('should generate valid invitation code', () => {
      const code = InvitationValidator.generateInvitationCode();
      expect(code).toHaveLength(10);
      expect(InvitationValidator.validateInvitationCode(code)).toBe(true);
    });

    it('should generate unique codes', () => {
      const code1 = InvitationValidator.generateInvitationCode();
      const code2 = InvitationValidator.generateInvitationCode();
      expect(code1).not.toBe(code2);
    });
  });

  describe('validateExpiresAt', () => {
    it('should return true for future date', () => {
      const futureDate = new Date();
      futureDate.setHours(futureDate.getHours() + 1);
      expect(InvitationValidator.validateExpiresAt(futureDate)).toBe(true);
    });

    it('should return false for past date', () => {
      const pastDate = new Date();
      pastDate.setHours(pastDate.getHours() - 1);
      expect(InvitationValidator.validateExpiresAt(pastDate)).toBe(false);
    });
  });
});

describe('AdminValidator', () => {
  describe('validateUsername', () => {
    it('should return true for valid username', () => {
      expect(AdminValidator.validateUsername('admin')).toBe(true);
      expect(AdminValidator.validateUsername('admin123')).toBe(true);
    });

    it('should return false for invalid username', () => {
      expect(AdminValidator.validateUsername('ab')).toBe(false);
      expect(AdminValidator.validateUsername('a'.repeat(51))).toBe(false);
    });
  });

  describe('validatePassword', () => {
    it('should return true for valid password', () => {
      expect(AdminValidator.validatePassword('password123')).toBe(true);
      expect(AdminValidator.validatePassword('Admin@123')).toBe(true);
    });

    it('should return false for invalid password', () => {
      expect(AdminValidator.validatePassword('123456')).toBe(false);
      expect(AdminValidator.validatePassword('password')).toBe(false);
      expect(AdminValidator.validatePassword('12345678')).toBe(false);
    });
  });

  describe('validateEmail', () => {
    it('should return true for valid email', () => {
      expect(AdminValidator.validateEmail('<EMAIL>')).toBe(true);
      expect(AdminValidator.validateEmail(undefined)).toBe(true);
    });

    it('should return false for invalid email', () => {
      expect(AdminValidator.validateEmail('invalid-email')).toBe(false);
      expect(AdminValidator.validateEmail('admin@')).toBe(false);
    });
  });
});

describe('FileValidator', () => {
  describe('validateImageFile', () => {
    it('should validate correct image file', () => {
      const mockFile = {
        mimetype: 'image/jpeg',
        size: 1024 * 1024, // 1MB
      } as any;

      const result = FileValidator.validateImageFile(mockFile);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid file', () => {
      const mockFile = {
        mimetype: 'text/plain',
        size: 10 * 1024 * 1024, // 10MB
      } as any;

      const result = FileValidator.validateImageFile(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('generateFileName', () => {
    it('should generate unique file name', async () => {
      const userId = 'user123';
      const originalName = 'avatar.jpg';

      const fileName1 = FileValidator.generateFileName(userId, originalName);
      // Wait a millisecond to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 1));
      const fileName2 = FileValidator.generateFileName(userId, originalName);

      expect(fileName1).toContain(userId);
      expect(fileName1).toContain('.jpg');
      expect(fileName1).not.toBe(fileName2);
    });
  });
});
