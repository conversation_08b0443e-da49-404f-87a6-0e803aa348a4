// 通用响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    timestamp: string;
  };
}

// 分页查询接口
export interface PaginationQuery {
  page?: number;
  limit?: number;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// JWT载荷接口
export interface JwtPayload {
  sub: string;
  openid?: string;
  username?: string;
  iat?: number;
  exp?: number;
}

// 管理员JWT载荷接口
export interface AdminJwtPayload {
  sub: string;
  username: string;
  is_super_admin: boolean;
  iat?: number;
  exp?: number;
}

// 文件上传接口
export interface FileUploadResult {
  image_url: string;
  file_name: string;
  file_size: number;
}

// Supabase配置接口
export interface SupabaseConfig {
  url: string;
  key: string;
  storage_bucket: string;
}

// 导出实体接口
export * from './entities';
