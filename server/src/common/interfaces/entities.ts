// 用户实体接口
export interface IUser {
  id: string;
  openid: string;
  username?: string;
  phone?: string;
  avatar_url?: string;
  default_baby_id?: string;
  created_at: Date;
  updated_at: Date;
}

// 宝宝实体接口
export interface IBaby {
  id: string;
  owner_id: string;
  nickname: string;
  birth_date: Date;
  gender: number; // 1: 男, 2: 女
  avatar_url?: string;
  created_at: Date;
  updated_at: Date;
}

// 家庭成员实体接口
export interface IFamilyMember {
  id: string;
  baby_id: string;
  user_id: string;
  role: number; // 1: 爸爸, 2: 妈妈, 3: 其他照顾者
  is_owner: boolean;
  created_at: Date;
}

// 邀请实体接口
export interface IInvitation {
  id: string;
  baby_id: string;
  inviter_id: string;
  invitation_code: string;
  phone?: string;
  expires_at: Date;
  used_at?: Date;
  used_by?: string;
  created_at: Date;
}

// 管理员用户实体接口
export interface IAdminUser {
  id: string;
  username: string;
  password_hash: string;
  email?: string;
  full_name?: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

// 创建用户DTO接口
export interface ICreateUserDto {
  openid: string;
  username?: string;
  phone?: string;
  avatar_url?: string;
}

// 更新用户DTO接口
export interface IUpdateUserDto {
  username?: string;
  phone?: string;
  avatar_url?: string;
  default_baby_id?: string;
}

// 创建宝宝DTO接口
export interface ICreateBabyDto {
  nickname: string;
  birth_date: Date;
  gender: number;
  avatar_url?: string;
  is_default?: boolean;
}

// 更新宝宝DTO接口
export interface IUpdateBabyDto {
  nickname?: string;
  birth_date?: Date;
  gender?: number;
  avatar_url?: string;
  is_default?: boolean;
}

// 创建邀请DTO接口
export interface ICreateInvitationDto {
  baby_id: string;
  phone?: string;
}

// 接受邀请DTO接口
export interface IAcceptInvitationDto {
  invitation_code: string;
  role: number;
}

// 管理员登录DTO接口
export interface IAdminLoginDto {
  username: string;
  password: string;
}

// 创建管理员DTO接口
export interface ICreateAdminDto {
  username: string;
  password: string;
  email?: string;
  full_name?: string;
  is_super_admin?: boolean;
}

// 更新管理员DTO接口
export interface IUpdateAdminDto {
  email?: string;
  full_name?: string;
  is_active?: boolean;
}

// 响应DTO接口
export interface IUserResponse {
  id: string;
  openid: string;
  username?: string;
  phone?: string;
  avatar_url?: string;
  default_baby_id?: string;
  default_baby?: IBabyResponse;
  created_at: Date;
  updated_at: Date;
}

export interface IBabyResponse {
  id: string;
  owner_id: string;
  nickname: string;
  birth_date: Date;
  gender: number;
  avatar_url?: string;
  is_default?: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface IFamilyMemberResponse {
  id: string;
  baby_id: string;
  user_id: string;
  role: number;
  is_owner: boolean;
  user?: IUserResponse;
  baby?: IBabyResponse;
  created_at: Date;
}

export interface IInvitationResponse {
  id: string;
  baby_id: string;
  inviter_id: string;
  invitation_code: string;
  phone?: string;
  expires_at: Date;
  used_at?: Date;
  used_by?: string;
  baby?: IBabyResponse;
  inviter?: IUserResponse;
  created_at: Date;
}

export interface IAdminResponse {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}
