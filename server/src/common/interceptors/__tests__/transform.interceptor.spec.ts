import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { of } from 'rxjs';
import { TransformInterceptor } from '../transform.interceptor';

describe('TransformInterceptor', () => {
  let interceptor: TransformInterceptor<any>;
  let mockExecutionContext: ExecutionContext;
  let mockCallHandler: CallHandler;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TransformInterceptor],
    }).compile();

    interceptor = module.get<TransformInterceptor<any>>(TransformInterceptor);

    mockExecutionContext = {} as ExecutionContext;
    mockCallHandler = {
      handle: jest.fn(),
    };
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  describe('intercept', () => {
    it('should transform response data with success: true', (done) => {
      const testData = { id: 1, name: 'test' };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: testData,
        });
        done();
      });
    });

    it('should handle null data correctly', (done) => {
      mockCallHandler.handle = jest.fn().mockReturnValue(of(null));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: null,
        });
        done();
      });
    });

    it('should handle undefined data correctly', (done) => {
      mockCallHandler.handle = jest.fn().mockReturnValue(of(undefined));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: undefined,
        });
        done();
      });
    });

    it('should handle empty object correctly', (done) => {
      mockCallHandler.handle = jest.fn().mockReturnValue(of({}));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: {},
        });
        done();
      });
    });

    it('should handle array data correctly', (done) => {
      const testData = [{ id: 1 }, { id: 2 }];
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: testData,
        });
        done();
      });
    });

    it('should handle string data correctly', (done) => {
      const testData = 'test string';
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: testData,
        });
        done();
      });
    });

    it('should handle number data correctly', (done) => {
      const testData = 42;
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: testData,
        });
        done();
      });
    });

    it('should handle boolean data correctly', (done) => {
      const testData = true;
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      const result = interceptor.intercept(
        mockExecutionContext,
        mockCallHandler,
      );

      result.subscribe((response) => {
        expect(response).toEqual({
          success: true,
          data: testData,
        });
        done();
      });
    });

    it('should call next.handle() once', () => {
      mockCallHandler.handle = jest.fn().mockReturnValue(of({}));

      interceptor.intercept(mockExecutionContext, mockCallHandler);

      expect(mockCallHandler.handle).toHaveBeenCalledTimes(1);
    });
  });
});
