import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IUser } from '../interfaces/entities';

export const CurrentUser = createParamDecorator(
  (data: keyof IUser | undefined, ctx: ExecutionContext): IUser | any => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user; // User is stored in request.user by Passport

    if (data) {
      return user?.[data];
    }

    return user;
  },
);
