import { ExecutionContext } from '@nestjs/common';
import { IUser } from '../interfaces/entities';

// Test the decorator logic directly by simulating what the decorator does
const extractUserFromContext = (ctx: ExecutionContext): IUser => {
  const request = ctx.switchToHttp().getRequest();
  return request.user;
};

describe('CurrentUser Decorator Logic', () => {
  let mockContext: ExecutionContext;
  let mockRequest: any;

  const mockUser: IUser = {
    id: 'user-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: undefined,
    avatar_url: undefined,
    default_baby_id: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(() => {
    mockRequest = {
      user: mockUser,
    };

    mockContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;
  });

  it('should extract user from request', () => {
    // Act
    const result = extractUserFromContext(mockContext);

    // Assert
    expect(result).toEqual(mockUser);
  });

  it('should return undefined when no user in request', () => {
    // Arrange
    mockRequest.user = undefined;

    // Act
    const result = extractUserFromContext(mockContext);

    // Assert
    expect(result).toBeUndefined();
  });

  it('should return null when user is null in request', () => {
    // Arrange
    mockRequest.user = null;

    // Act
    const result = extractUserFromContext(mockContext);

    // Assert
    expect(result).toBeNull();
  });
});
