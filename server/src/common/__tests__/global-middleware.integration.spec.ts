import { Test, TestingModule } from '@nestjs/testing';
import {
  INestApplication,
  Controller,
  Get,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import * as request from 'supertest';
import { HttpExceptionFilter } from '../filters/http-exception.filter';
import { TransformInterceptor } from '../interceptors/transform.interceptor';
import { BusinessException } from '../exceptions/business.exception';
import { ErrorCode } from '../enums';

@Controller('test')
class TestController {
  @Get('success')
  getSuccess() {
    return { message: 'success' };
  }

  @Get('business-error')
  getBusinessError() {
    throw new BusinessException(
      ErrorCode.USER_NOT_FOUND,
      'User not found',
      HttpStatus.NOT_FOUND,
    );
  }

  @Get('http-error')
  getHttpError() {
    throw new HttpException('Bad request', HttpStatus.BAD_REQUEST);
  }

  @Get('unknown-error')
  getUnknownError() {
    throw new Error('Unknown error');
  }
}

describe('Global Middleware Integration', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [TestController],
    }).compile();

    app = moduleFixture.createNestApplication();

    // Apply global filters and interceptors
    app.useGlobalFilters(new HttpExceptionFilter());
    app.useGlobalInterceptors(new TransformInterceptor());

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('TransformInterceptor', () => {
    it('should transform successful response', () => {
      return request(app.getHttpServer())
        .get('/test/success')
        .expect(200)
        .expect((res) => {
          expect(res.body).toEqual({
            success: true,
            data: { message: 'success' },
          });
        });
    });
  });

  describe('HttpExceptionFilter', () => {
    it('should handle BusinessException', () => {
      return request(app.getHttpServer())
        .get('/test/business-error')
        .expect(404)
        .expect((res) => {
          expect(res.body).toEqual({
            success: false,
            error: {
              code: ErrorCode.USER_NOT_FOUND,
              message: 'User not found',
              timestamp: expect.any(String),
            },
          });
        });
    });

    it('should handle HttpException', () => {
      return request(app.getHttpServer())
        .get('/test/http-error')
        .expect(400)
        .expect((res) => {
          expect(res.body).toEqual({
            success: false,
            error: {
              code: 400000,
              message: 'Bad request',
              timestamp: expect.any(String),
            },
          });
        });
    });

    it('should handle unknown errors', () => {
      return request(app.getHttpServer())
        .get('/test/unknown-error')
        .expect(500)
        .expect((res) => {
          expect(res.body).toEqual({
            success: false,
            error: {
              code: 50000,
              message: 'Internal server error',
              timestamp: expect.any(String),
            },
          });
        });
    });
  });
});
