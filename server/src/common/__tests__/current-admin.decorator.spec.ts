import { ExecutionContext } from '@nestjs/common';
import { IAdminUser } from '../interfaces/entities';

// Test the decorator logic directly by simulating what the decorator does
const extractAdminFromContext = (ctx: ExecutionContext): IAdminUser => {
  const request = ctx.switchToHttp().getRequest();
  return request.user;
};

describe('CurrentAdmin Decorator Logic', () => {
  let mockContext: ExecutionContext;
  let mockRequest: any;

  const mockAdmin: IAdminUser = {
    id: 'admin-1',
    username: 'testadmin',
    password_hash: '$2b$10$hashedpassword',
    email: '<EMAIL>',
    full_name: 'Test Admin',
    is_active: true,
    is_super_admin: false,
    last_login_at: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(() => {
    mockRequest = {
      user: mockAdmin,
    };

    mockContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;
  });

  it('should extract admin from request', () => {
    // Act
    const result = extractAdminFromContext(mockContext);

    // Assert
    expect(result).toEqual(mockAdmin);
  });

  it('should return undefined when no admin in request', () => {
    // Arrange
    mockRequest.user = undefined;

    // Act
    const result = extractAdminFromContext(mockContext);

    // Assert
    expect(result).toBeUndefined();
  });

  it('should return null when admin is null in request', () => {
    // Arrange
    mockRequest.user = null;

    // Act
    const result = extractAdminFromContext(mockContext);

    // Assert
    expect(result).toBeNull();
  });
});
