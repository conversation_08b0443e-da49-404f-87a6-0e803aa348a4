import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { AppModule } from './app.module';
import { AppService } from './app.service';
import { UserService } from './user/user.service';
import { BabyService } from './baby/baby.service';
import { FamilyService } from './family/family.service';
import { AdminService } from './admin/admin.service';
import { AuthService } from './auth/auth.service';
import { AdminAuthService } from './auth/admin-auth.service';
import { UploadService } from './upload/upload.service';
import { SupabaseService } from './database/supabase.service';
import { HabitStatisticsService } from './habits/services/habit-statistics.service';

describe('AppModule Integration', () => {
  let app: TestingModule;

  beforeEach(async () => {
    app = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.test', '.env'],
        }),
        AppModule,
      ],
    }).compile();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('Module Dependencies', () => {
    it('should create AppService', () => {
      const appService = app.get<AppService>(AppService);
      expect(appService).toBeDefined();
    });

    it('should create SupabaseService', () => {
      const supabaseService = app.get<SupabaseService>(SupabaseService);
      expect(supabaseService).toBeDefined();
    });

    it('should create UserService with dependencies', () => {
      const userService = app.get<UserService>(UserService);
      expect(userService).toBeDefined();
    });

    it('should create BabyService with dependencies', () => {
      const babyService = app.get<BabyService>(BabyService);
      expect(babyService).toBeDefined();
    });

    it('should create FamilyService with dependencies', () => {
      const familyService = app.get<FamilyService>(FamilyService);
      expect(familyService).toBeDefined();
    });

    it('should create AdminService with dependencies', () => {
      const adminService = app.get<AdminService>(AdminService);
      expect(adminService).toBeDefined();
    });

    it('should create AuthService with dependencies', () => {
      const authService = app.get<AuthService>(AuthService);
      expect(authService).toBeDefined();
    });

    it('should create AdminAuthService with dependencies', () => {
      const adminAuthService = app.get<AdminAuthService>(AdminAuthService);
      expect(adminAuthService).toBeDefined();
    });

    it('should create UploadService with dependencies', () => {
      const uploadService = app.get<UploadService>(UploadService);
      expect(uploadService).toBeDefined();
    });

    it('should create HabitStatisticsService with dependencies', () => {
      const habitStatisticsService = app.get<HabitStatisticsService>(
        HabitStatisticsService,
      );
      expect(habitStatisticsService).toBeDefined();
    });
  });

  describe('Cross-Module Dependencies', () => {
    it('should resolve UserService in BabyService', async () => {
      const babyService = app.get<BabyService>(BabyService);
      expect(babyService).toBeDefined();

      // Verify that BabyService can access UserRepository through proper injection
      const userService = app.get<UserService>(UserService);
      expect(userService).toBeDefined();
    });

    it('should resolve BabyService in FamilyService', async () => {
      const familyService = app.get<FamilyService>(FamilyService);
      expect(familyService).toBeDefined();

      const babyService = app.get<BabyService>(BabyService);
      expect(babyService).toBeDefined();
    });

    it('should resolve UserRepository in AuthService', async () => {
      const authService = app.get<AuthService>(AuthService);
      expect(authService).toBeDefined();
    });

    it('should resolve AdminRepository in AdminAuthService', async () => {
      const adminAuthService = app.get<AdminAuthService>(AdminAuthService);
      expect(adminAuthService).toBeDefined();
    });

    it('should resolve HabitStatisticsService in AppService', async () => {
      const appService = app.get<AppService>(AppService);
      expect(appService).toBeDefined();

      const habitStatisticsService = app.get<HabitStatisticsService>(
        HabitStatisticsService,
      );
      expect(habitStatisticsService).toBeDefined();
    });
  });

  describe('Global Providers', () => {
    it('should have global exception filter configured', () => {
      // The APP_FILTER should be configured in AppModule
      expect(app).toBeDefined();
    });

    it('should have global transform interceptor configured', () => {
      // The APP_INTERCEPTOR should be configured in AppModule
      expect(app).toBeDefined();
    });
  });
});
