import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  createClient,
  SupabaseClient,
  PostgrestError,
} from '@supabase/supabase-js';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

export interface DatabaseResult<T = any> {
  data: T | null;
  error: PostgrestError | null;
  count?: number | null;
}

export interface DatabaseConnection {
  isConnected: boolean;
  lastChecked: Date;
  connectionId: string;
}

@Injectable()
export class SupabaseService implements OnModuleInit {
  private readonly logger = new Logger(SupabaseService.name);
  private supabase: SupabaseClient;
  private connectionStatus: DatabaseConnection;

  constructor(private configService: ConfigService) {
    this.initializeConnection();
  }

  async onModuleInit() {
    await this.testConnection();
  }

  private initializeConnection(): void {
    const supabaseUrl = this.configService.get<string>('supabase.url');
    const supabaseKey = this.configService.get<string>('supabase.serviceKey');

    if (!supabaseUrl || !supabaseKey) {
      this.logger.error('Supabase configuration is missing');
      throw new Error('Supabase configuration is missing');
    }

    try {
      this.supabase = createClient(supabaseUrl, supabaseKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
        global: {
          headers: {
            'x-application-name': 'xiaojiabu-backend',
          },
        },
      });

      this.connectionStatus = {
        isConnected: false,
        lastChecked: new Date(),
        connectionId: this.generateConnectionId(),
      };

      this.logger.log('Supabase client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Supabase client', error);
      throw error;
    }
  }

  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async testConnection(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('count', { count: 'exact', head: true });

      if (error) {
        this.logger.warn('Database connection test failed', error);
        this.connectionStatus.isConnected = false;
      } else {
        this.logger.log('Database connection test successful');
        this.connectionStatus.isConnected = true;
      }

      this.connectionStatus.lastChecked = new Date();
      return this.connectionStatus.isConnected;
    } catch (error) {
      this.logger.error('Database connection test error', error);
      this.connectionStatus.isConnected = false;
      this.connectionStatus.lastChecked = new Date();
      return false;
    }
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  getConnectionStatus(): DatabaseConnection {
    return { ...this.connectionStatus };
  }

  // 通用查询方法
  query(table: string) {
    return this.supabase.from(table);
  }

  // 通用存储方法
  get storage() {
    return this.supabase.storage;
  }

  // 数据库操作错误处理工具
  handleDatabaseError(error: PostgrestError | null, operation: string): void {
    if (!error) return;

    this.logger.error(`Database operation failed: ${operation}`, {
      code: error.code,
      message: error.message,
      details: error.details,
      hint: error.hint,
    });

    // 根据错误类型抛出相应的业务异常
    switch (error.code) {
      case 'PGRST116': // 没有找到记录
        throw new BusinessException(
          ErrorCode.USER_NOT_FOUND, // 这里可以根据具体操作调整错误码
          `Record not found: ${error.message}`,
        );
      case '23505': // 唯一约束违反
        if (error.message.includes('openid')) {
          throw new BusinessException(
            ErrorCode.USERNAME_TAKEN,
            'User with this openid already exists',
          );
        }
        throw new BusinessException(
          ErrorCode.USERNAME_TAKEN,
          `Duplicate entry: ${error.message}`,
        );
      case '23503': // 外键约束违反
        throw new BusinessException(
          ErrorCode.INVALID_BABY_DATA,
          `Foreign key constraint violation: ${error.message}`,
        );
      case '23514': // 检查约束违反
        throw new BusinessException(
          ErrorCode.INVALID_BABY_DATA,
          `Data validation failed: ${error.message}`,
        );
      default:
        throw new BusinessException(
          ErrorCode.UPLOAD_FAILED, // 通用数据库错误
          `Database operation failed: ${error.message}`,
        );
    }
  }

  // 包装数据库操作结果
  wrapDatabaseResult<T>(result: {
    data: T | null;
    error: PostgrestError | null;
    count?: number | null;
  }): DatabaseResult<T> {
    return {
      data: result.data,
      error: result.error,
      count: result.count,
    };
  }

  // 执行事务操作
  async executeTransaction<T>(
    operations: (() => Promise<any>)[],
  ): Promise<T[]> {
    const results: T[] = [];

    try {
      for (const operation of operations) {
        const result = await operation();
        results.push(result);
      }

      this.logger.log(
        `Transaction completed successfully with ${operations.length} operations`,
      );
      return results;
    } catch (error) {
      this.logger.error('Transaction failed, rolling back', error);
      throw error;
    }
  }

  // 健康检查方法
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    connection: DatabaseConnection;
    timestamp: Date;
  }> {
    const isConnected = await this.testConnection();

    return {
      status: isConnected ? 'healthy' : 'unhealthy',
      connection: this.getConnectionStatus(),
      timestamp: new Date(),
    };
  }
}
