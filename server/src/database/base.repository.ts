import { Injectable, Logger } from '@nestjs/common';
import { SupabaseService, DatabaseResult } from './supabase.service';
import { DatabaseUtils } from './database.utils';

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface SortingOptions {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface QueryOptions extends PaginationOptions, SortingOptions {
  select?: string;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export abstract class BaseRepository<T extends Record<string, any>> {
  protected readonly logger = new Logger(this.constructor.name);
  protected abstract readonly tableName: string;
  protected abstract readonly entityName: string;

  constructor(protected readonly supabaseService: SupabaseService) {}

  /**
   * 创建记录
   */
  async create(data: Partial<T>): Promise<T> {
    try {
      // 验证必填字段
      this.validateCreateData(data);

      // 清理数据
      const cleanData = DatabaseUtils.cleanRecord(data);

      DatabaseUtils.logDatabaseOperation('CREATE', this.tableName, cleanData);

      const result = await this.supabaseService
        .query(this.tableName)
        .insert(cleanData)
        .select()
        .single();

      this.supabaseService.handleDatabaseError(
        result.error,
        `create ${this.entityName}`,
      );

      this.logger.log(`${this.entityName} created successfully`, {
        id: result.data?.id,
      });
      return result.data as T;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `create ${this.entityName}`,
        this.entityName,
      );
    }
  }

  /**
   * 根据ID查找记录
   */
  async findById(id: string, select?: string): Promise<T | null> {
    try {
      if (!DatabaseUtils.isValidUUID(id)) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_ID', this.tableName, { id });

      let query: any = this.supabaseService.query(this.tableName);

      if (select) {
        query = query.select(select);
      } else {
        query = query.select('*');
      }

      const result = await query.eq('id', id).single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          `find ${this.entityName} by id`,
        );
      }

      return result.data as T | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `find ${this.entityName} by id`,
        this.entityName,
      );
    }
  }

  /**
   * 查找多条记录
   */
  async findMany(
    conditions: Partial<T> = {},
    options: QueryOptions = {},
  ): Promise<T[]> {
    try {
      const { page, limit, sortBy, sortOrder, select } = options;
      const formattedConditions =
        DatabaseUtils.formatQueryConditions(conditions);

      DatabaseUtils.logDatabaseOperation(
        'FIND_MANY',
        this.tableName,
        formattedConditions,
      );

      let query: any = this.supabaseService.query(this.tableName);

      // 选择字段
      if (select) {
        query = query.select(select);
      } else {
        query = query.select('*');
      }

      // 应用条件
      query = this.applyConditions(query, formattedConditions);

      // 应用排序
      if (sortBy) {
        const { column, ascending } = DatabaseUtils.handleSorting(
          sortBy,
          sortOrder,
        );
        query = query.order(column, { ascending });
      }

      // 应用分页
      if (page && limit) {
        const { from, to } = DatabaseUtils.handlePagination(page, limit);
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        `find ${this.entityName} records`,
      );

      return (result.data as T[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `find ${this.entityName} records`,
        this.entityName,
      );
    }
  }

  /**
   * 分页查询
   */
  async findWithPagination(
    conditions: Partial<T> = {},
    options: QueryOptions = {},
  ): Promise<PaginatedResult<T>> {
    try {
      const { page = 1, limit = 10, sortBy, sortOrder, select } = options;
      const formattedConditions =
        DatabaseUtils.formatQueryConditions(conditions);

      DatabaseUtils.logDatabaseOperation(
        'FIND_WITH_PAGINATION',
        this.tableName,
        {
          ...formattedConditions,
          page,
          limit,
        },
      );

      let query: any = this.supabaseService.query(this.tableName);

      // 选择字段
      if (select) {
        query = query.select(select, { count: 'exact' });
      } else {
        query = query.select('*', { count: 'exact' });
      }

      // 应用条件
      query = this.applyConditions(query, formattedConditions);

      // 应用排序
      if (sortBy) {
        const { column, ascending } = DatabaseUtils.handleSorting(
          sortBy,
          sortOrder,
        );
        query = query.order(column, { ascending });
      }

      // 应用分页
      const { from, to } = DatabaseUtils.handlePagination(page, limit);
      query = query.range(from, to);

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        `paginate ${this.entityName} records`,
      );

      const total = result.count || 0;
      const totalPages = Math.ceil(total / limit);

      return {
        data: (result.data as T[]) || [],
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `paginate ${this.entityName} records`,
        this.entityName,
      );
    }
  }

  /**
   * 更新记录
   */
  async update(id: string, data: Partial<T>): Promise<T> {
    try {
      if (!DatabaseUtils.isValidUUID(id)) {
        DatabaseUtils.handleError(
          new Error('Invalid ID format'),
          `update ${this.entityName}`,
          this.entityName,
        );
      }

      // 清理数据
      const cleanData = DatabaseUtils.cleanRecord(data);

      // 添加更新时间
      (cleanData as any).updated_at = new Date().toISOString();

      DatabaseUtils.logDatabaseOperation('UPDATE', this.tableName, {
        id,
        ...cleanData,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .update(cleanData)
        .eq('id', id)
        .select()
        .single();

      this.supabaseService.handleDatabaseError(
        result.error,
        `update ${this.entityName}`,
      );

      this.logger.log(`${this.entityName} updated successfully`, { id });
      return result.data as T;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `update ${this.entityName}`,
        this.entityName,
      );
    }
  }

  /**
   * 删除记录
   */
  async delete(id: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(id)) {
        return false;
      }

      DatabaseUtils.logDatabaseOperation('DELETE', this.tableName, { id });

      const result = await this.supabaseService
        .query(this.tableName)
        .delete()
        .eq('id', id);

      this.supabaseService.handleDatabaseError(
        result.error,
        `delete ${this.entityName}`,
      );

      this.logger.log(`${this.entityName} deleted successfully`, { id });
      return true;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `delete ${this.entityName}`,
        this.entityName,
      );
    }
  }

  /**
   * 检查记录是否存在
   */
  async exists(id: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(id)) {
        return false;
      }

      const result = await this.supabaseService
        .query(this.tableName)
        .select('id', { count: 'exact', head: true })
        .eq('id', id);

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn(`Failed to check ${this.entityName} existence`, error);
      return false;
    }
  }

  /**
   * 计数记录
   */
  async count(conditions: Partial<T> = {}): Promise<number> {
    try {
      const formattedConditions =
        DatabaseUtils.formatQueryConditions(conditions);

      let query: any = this.supabaseService
        .query(this.tableName)
        .select('*', { count: 'exact', head: true });

      query = this.applyConditions(query, formattedConditions);

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        `count ${this.entityName} records`,
      );

      return result.count || 0;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        `count ${this.entityName} records`,
        this.entityName,
      );
    }
  }

  /**
   * 应用查询条件
   */
  protected applyConditions(query: any, conditions: Record<string, any>): any {
    for (const [key, value] of Object.entries(conditions)) {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else {
          query = query.eq(key, value);
        }
      }
    }
    return query;
  }

  /**
   * 验证创建数据 - 子类可以重写
   */
  protected validateCreateData(data: Partial<T>): void {
    // 基础验证，子类可以重写添加特定验证
    if (!data || Object.keys(data).length === 0) {
      DatabaseUtils.handleError(
        new Error('Create data cannot be empty'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }
  }
}
