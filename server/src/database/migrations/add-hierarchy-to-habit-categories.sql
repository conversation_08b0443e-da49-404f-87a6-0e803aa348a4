-- 添加层级结构字段到 habit_categories 表
-- 执行时间：2024-01-15

-- 1. 添加新字段
ALTER TABLE habit_categories 
ADD COLUMN parent_id UUID REFERENCES habit_categories(id) ON DELETE CASCADE,
ADD COLUMN level INTEGER NOT NULL DEFAULT 1;

-- 2. 添加约束确保层级结构的一致性
ALTER TABLE habit_categories 
ADD CONSTRAINT check_parent_level CHECK (
  (level = 1 AND parent_id IS NULL) OR 
  (level = 2 AND parent_id IS NOT NULL)
);

-- 3. 添加层级检查约束
ALTER TABLE habit_categories 
ADD CONSTRAINT check_level_range CHECK (level IN (1, 2));

-- 4. 更新现有数据为一级分类
UPDATE habit_categories 
SET level = 1, parent_id = NULL 
WHERE level IS NULL OR level = 0;

-- 5. 创建新的索引
CREATE INDEX idx_habit_categories_parent ON habit_categories(parent_id, level, sort_order);
CREATE INDEX idx_habit_categories_level ON habit_categories(level, is_active, sort_order);

-- 6. 更新现有索引（如果需要）
-- DROP INDEX IF EXISTS idx_habit_categories_active;
-- CREATE INDEX idx_habit_categories_active ON habit_categories(is_active, level, sort_order);

COMMENT ON COLUMN habit_categories.parent_id IS '父分类ID，NULL表示一级分类';
COMMENT ON COLUMN habit_categories.level IS '分类层级：1表示一级分类，2表示二级分类';