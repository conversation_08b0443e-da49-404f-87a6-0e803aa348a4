import { DatabaseUtils } from '../database.utils';
import { ErrorCode } from '../../common/enums';
import { BusinessException } from '../../common/exceptions/business.exception';

describe('DatabaseUtils', () => {
  describe('isValidUUID', () => {
    it('should return true for valid UUID', () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      expect(DatabaseUtils.isValidUUID(validUUID)).toBe(true);
    });

    it('should return false for invalid UUID', () => {
      expect(DatabaseUtils.isValidUUID('invalid-uuid')).toBe(false);
      expect(DatabaseUtils.isValidUUID('')).toBe(false);
      expect(DatabaseUtils.isValidUUID('123')).toBe(false);
    });
  });

  describe('isValidPhone', () => {
    it('should return true for valid Chinese phone numbers', () => {
      expect(DatabaseUtils.isValidPhone('***********')).toBe(true);
      expect(DatabaseUtils.isValidPhone('***********')).toBe(true);
      expect(DatabaseUtils.isValidPhone('***********')).toBe(true);
    });

    it('should return false for invalid phone numbers', () => {
      expect(DatabaseUtils.isValidPhone('***********')).toBe(false); // 不以1开头的有效数字
      expect(DatabaseUtils.isValidPhone('1381234567')).toBe(false); // 少一位
      expect(DatabaseUtils.isValidPhone('***********9')).toBe(false); // 多一位
      expect(DatabaseUtils.isValidPhone('abc12345678')).toBe(false); // 包含字母
    });
  });

  describe('generateInvitationCode', () => {
    it('should generate 8-character invitation code', () => {
      const code = DatabaseUtils.generateInvitationCode();
      expect(code).toHaveLength(8);
      expect(/^[A-Z0-9]+$/.test(code)).toBe(true);
    });

    it('should generate unique codes', () => {
      const codes = new Set();
      for (let i = 0; i < 100; i++) {
        codes.add(DatabaseUtils.generateInvitationCode());
      }
      expect(codes.size).toBeGreaterThan(90); // 应该大部分都是唯一的
    });
  });

  describe('calculateInvitationExpiry', () => {
    it('should calculate expiry date correctly', () => {
      const now = new Date();
      const expiry = DatabaseUtils.calculateInvitationExpiry(7);
      const diffInDays = Math.ceil(
        (expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
      );
      expect(diffInDays).toBe(7);
    });

    it('should use default 7 days if no parameter provided', () => {
      const now = new Date();
      const expiry = DatabaseUtils.calculateInvitationExpiry();
      const diffInDays = Math.ceil(
        (expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
      );
      expect(diffInDays).toBe(7);
    });
  });

  describe('isInvitationExpired', () => {
    it('should return true for expired invitation', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      expect(DatabaseUtils.isInvitationExpired(pastDate)).toBe(true);
    });

    it('should return false for valid invitation', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      expect(DatabaseUtils.isInvitationExpired(futureDate)).toBe(false);
    });
  });

  describe('formatQueryConditions', () => {
    it('should remove null and undefined values', () => {
      const conditions = {
        name: 'test',
        age: null,
        email: undefined,
        active: true,
        count: 0,
      };

      const formatted = DatabaseUtils.formatQueryConditions(conditions);
      expect(formatted).toEqual({
        name: 'test',
        active: true,
        count: 0,
      });
    });
  });

  describe('handlePagination', () => {
    it('should calculate pagination correctly', () => {
      const result = DatabaseUtils.handlePagination(2, 10);
      expect(result).toEqual({ from: 10, to: 19 });
    });

    it('should use defaults for invalid parameters', () => {
      const result = DatabaseUtils.handlePagination(0, -5);
      expect(result).toEqual({ from: 0, to: 0 }); // page 1, limit 1
    });

    it('should limit maximum page size', () => {
      const result = DatabaseUtils.handlePagination(1, 200);
      expect(result).toEqual({ from: 0, to: 99 }); // 限制为100
    });
  });

  describe('handleSorting', () => {
    it('should return correct sorting parameters', () => {
      const result = DatabaseUtils.handleSorting('name', 'asc');
      expect(result).toEqual({ column: 'name', ascending: true });
    });

    it('should use defaults', () => {
      const result = DatabaseUtils.handleSorting();
      expect(result).toEqual({ column: 'created_at', ascending: false });
    });
  });

  describe('cleanRecord', () => {
    it('should remove system fields and null values', () => {
      const record = {
        id: '123',
        name: 'test',
        email: null,
        age: 25,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const cleaned = DatabaseUtils.cleanRecord(record);
      expect(cleaned).toEqual({
        name: 'test',
        age: 25,
      });
    });
  });

  describe('validateRequiredFields', () => {
    it('should not throw for valid data', () => {
      const data = { name: 'test', email: '<EMAIL>' };
      expect(() => {
        DatabaseUtils.validateRequiredFields(
          data,
          ['name', 'email'] as (keyof typeof data)[],
          'User',
        );
      }).not.toThrow();
    });

    it('should throw BusinessException for missing fields', () => {
      const data = { name: 'test' };
      expect(() => {
        DatabaseUtils.validateRequiredFields(
          data,
          ['name', 'email'] as (keyof typeof data)[],
          'User',
        );
      }).toThrow(BusinessException);
    });
  });
});
