import { Logger } from '@nestjs/common';
import { PostgrestError } from '@supabase/supabase-js';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

export class DatabaseUtils {
  private static readonly logger = new Logger(DatabaseUtils.name);

  /**
   * 验证UUID格式
   */
  static isValidUUID(uuid: string): boolean {
    // 更宽松的UUID格式验证，支持自定义UUID格式
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * 验证手机号格式
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 生成邀请码
   */
  static generateInvitationCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 计算邀请过期时间（默认7天）
   */
  static calculateInvitationExpiry(days: number = 7): Date {
    const expiry = new Date();
    expiry.setDate(expiry.getDate() + days);
    return expiry;
  }

  /**
   * 检查邀请是否过期
   */
  static isInvitationExpired(expiresAt: Date): boolean {
    return new Date() > new Date(expiresAt);
  }

  /**
   * 格式化数据库查询条件
   */
  static formatQueryConditions(
    conditions: Record<string, any>,
  ): Record<string, any> {
    const formatted: Record<string, any> = {};

    for (const [key, value] of Object.entries(conditions)) {
      if (value !== undefined && value !== null) {
        formatted[key] = value;
      }
    }

    return formatted;
  }

  /**
   * 处理分页参数
   */
  static handlePagination(
    page?: number,
    limit?: number,
  ): { from: number; to: number } {
    const pageNum = Math.max(1, page || 1);
    const limitNum = Math.min(100, Math.max(1, limit || 10)); // 限制最大100条

    const from = (pageNum - 1) * limitNum;
    const to = from + limitNum - 1;

    return { from, to };
  }

  /**
   * 处理排序参数
   */
  static handleSorting(
    sortBy?: string,
    sortOrder?: 'asc' | 'desc',
  ): { column: string; ascending: boolean } {
    const column = sortBy || 'created_at';
    const ascending = sortOrder === 'asc';

    return { column, ascending };
  }

  /**
   * 清理数据库记录（移除null值和系统字段）
   */
  static cleanRecord<T extends Record<string, any>>(record: T): Partial<T> {
    const cleaned: Partial<T> = {};

    for (const [key, value] of Object.entries(record)) {
      // 跳过系统字段和null值
      if (
        value !== null &&
        value !== undefined &&
        !['id', 'created_at', 'updated_at'].includes(key)
      ) {
        cleaned[key as keyof T] = value;
      }
    }

    return cleaned;
  }

  /**
   * 验证必填字段
   */
  static validateRequiredFields<T extends Record<string, any>>(
    data: T,
    requiredFields: (keyof T)[],
    entityName: string = 'Entity',
  ): void {
    const missingFields = requiredFields.filter(
      (field) =>
        data[field] === undefined || data[field] === null || data[field] === '',
    );

    if (missingFields.length > 0) {
      this.logger.warn(
        `${entityName} validation failed: missing required fields`,
        {
          missingFields,
          providedData: Object.keys(data),
        },
      );

      throw new BusinessException(
        ErrorCode.INVALID_BABY_DATA, // 可以根据实体类型调整
        `Missing required fields: ${missingFields.join(', ')}`,
      );
    }
  }

  /**
   * 记录数据库操作日志
   */
  static logDatabaseOperation(
    operation: string,
    table: string,
    conditions?: Record<string, any>,
    result?: { count?: number; success: boolean },
  ): void {
    this.logger.log(`Database operation: ${operation}`, {
      table,
      conditions,
      result,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 处理数据库错误的通用方法
   */
  static handleError(
    error: PostgrestError | Error,
    operation: string,
    entityType?: string,
  ): never {
    this.logger.error(`Database operation failed: ${operation}`, {
      error: error.message,
      entityType,
      timestamp: new Date().toISOString(),
    });

    if ('code' in error) {
      // PostgrestError
      const pgError = error;

      switch (pgError.code) {
        case 'PGRST116': // 没有找到记录
          throw new BusinessException(
            this.getNotFoundErrorCode(entityType),
            `${entityType || 'Record'} not found`,
          );
        case '23505': // 唯一约束违反
          throw new BusinessException(
            this.getDuplicateErrorCode(entityType),
            `${entityType || 'Record'} already exists`,
          );
        case '23503': // 外键约束违反
          throw new BusinessException(
            ErrorCode.INVALID_BABY_DATA,
            `Invalid reference: ${pgError.message}`,
          );
        case '23514': // 检查约束违反
          throw new BusinessException(
            ErrorCode.INVALID_BABY_DATA,
            `Data validation failed: ${pgError.message}`,
          );
        default:
          throw new BusinessException(
            ErrorCode.UPLOAD_FAILED,
            `Database operation failed: ${pgError.message}`,
          );
      }
    } else {
      // 通用错误
      throw new BusinessException(
        ErrorCode.UPLOAD_FAILED,
        `Operation failed: ${error.message}`,
      );
    }
  }

  private static getNotFoundErrorCode(entityType?: string): ErrorCode {
    switch (entityType?.toLowerCase()) {
      case 'user':
        return ErrorCode.USER_NOT_FOUND;
      case 'baby':
        return ErrorCode.BABY_NOT_FOUND;
      case 'invitation':
        return ErrorCode.INVITATION_NOT_FOUND;
      case 'admin':
        return ErrorCode.ADMIN_NOT_FOUND;
      default:
        return ErrorCode.USER_NOT_FOUND;
    }
  }

  private static getDuplicateErrorCode(entityType?: string): ErrorCode {
    switch (entityType?.toLowerCase()) {
      case 'user':
        return ErrorCode.USERNAME_TAKEN;
      case 'admin':
        return ErrorCode.ADMIN_USERNAME_TAKEN;
      default:
        return ErrorCode.USERNAME_TAKEN;
    }
  }
}
