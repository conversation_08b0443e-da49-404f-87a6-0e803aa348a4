import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UserRepository } from '../user/user.repository';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';
import { JwtPayload } from '../common/interfaces';
import { IUser, ICreateUserDto } from '../common/interfaces/entities';
import { WechatLoginDto } from './dto/wechat-login.dto';
import {
  AuthResponseDto,
  RefreshTokenResponseDto,
} from './dto/auth-response.dto';

interface WechatSessionResponse {
  openid: string;
  session_key: string;
  unionid?: string;
  errcode?: number;
  errmsg?: string;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * 微信小程序登录
   */
  async wechatLogin(wechatLoginDto: WechatLoginDto): Promise<AuthResponseDto> {
    try {
      // 1. 通过微信API获取openid
      const openid = await this.getWechatOpenid(wechatLoginDto.code);

      // 2. 查找或创建用户
      let user = await this.userRepository.findByOpenid(openid);
      if (!user) {
        const createUserDto: ICreateUserDto = {
          openid,
        };
        user = await this.userRepository.createUser(createUserDto);
      }

      // 3. 生成JWT token
      const payload: JwtPayload = {
        sub: user.id,
        openid: user.openid,
        username: user.username,
      };
      const access_token = this.jwtService.sign(payload);

      return {
        access_token,
        user,
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'WeChat login failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(userId: string): Promise<RefreshTokenResponseDto> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new BusinessException(
          ErrorCode.USER_NOT_FOUND,
          'User not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const payload: JwtPayload = {
        sub: user.id,
        openid: user.openid,
        username: user.username,
      };
      const access_token = this.jwtService.sign(payload);

      return {
        access_token,
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Token refresh failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 验证用户
   */
  async validateUser(payload: JwtPayload): Promise<IUser | null> {
    try {
      return await this.userRepository.findById(payload.sub);
    } catch (error) {
      return null;
    }
  }

  /**
   * 通过微信API获取openid
   */
  private async getWechatOpenid(code: string): Promise<string> {
    try {
      const appId = this.configService.get<string>('WECHAT_APP_ID');
      const appSecret = this.configService.get<string>('WECHAT_APP_SECRET');

      if (!appId || !appSecret) {
        throw new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          'WeChat configuration not found',
        );
      }

      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;

      const response = await fetch(url);
      const data: WechatSessionResponse = await response.json();

      if (data.errcode) {
        throw new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          `WeChat API error: ${data.errmsg}`,
        );
      }

      if (!data.openid) {
        throw new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          'Invalid WeChat code',
        );
      }

      return data.openid;
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new BusinessException(
        ErrorCode.INVALID_WECHAT_CODE,
        'Failed to get WeChat openid',
      );
    }
  }
}
