import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AdminRepository } from '../admin/admin.repository';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';
import { AdminJwtPayload } from '../common/interfaces';
import { IAdminUser } from '../common/interfaces/entities';
import { AdminLoginDto } from './dto/admin-login.dto';
import {
  AdminAuthResponseDto,
  AdminRefreshTokenResponseDto,
} from './dto/admin-auth-response.dto';

@Injectable()
export class AdminAuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly adminRepository: AdminRepository,
  ) {}

  /**
   * 管理员登录
   */
  async adminLogin(
    adminLoginDto: AdminLoginDto,
  ): Promise<AdminAuthResponseDto> {
    try {
      // 1. 验证管理员凭据
      const admin = await this.adminRepository.validatePassword(
        adminLoginDto.username,
        adminLoginDto.password,
      );

      if (!admin) {
        throw new BusinessException(
          ErrorCode.INVALID_ADMIN_CREDENTIALS,
          'Invalid username or password',
          HttpStatus.UNAUTHORIZED,
        );
      }

      // 2. 检查管理员账户是否激活
      if (!admin.is_active) {
        throw new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Admin account is deactivated',
          HttpStatus.FORBIDDEN,
        );
      }

      // 3. 更新最后登录时间
      await this.adminRepository.updateLastLogin(admin.id);

      // 4. 生成JWT token
      const payload: AdminJwtPayload = {
        sub: admin.id,
        username: admin.username,
        is_super_admin: admin.is_super_admin,
      };

      const access_token = this.jwtService.sign(payload, {
        secret: this.configService.get<string>('ADMIN_JWT_SECRET'),
        expiresIn: '24h', // 管理员token有效期24小时
      });

      // 5. 返回响应（不包含密码哈希）
      const { password_hash, ...adminResponse } = admin;

      return {
        access_token,
        admin: adminResponse,
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Admin login failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 刷新管理员token
   */
  async refreshAdminToken(
    adminId: string,
  ): Promise<AdminRefreshTokenResponseDto> {
    try {
      const admin = await this.adminRepository.findById(adminId);
      if (!admin) {
        throw new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        );
      }

      if (!admin.is_active) {
        throw new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Admin account is deactivated',
          HttpStatus.FORBIDDEN,
        );
      }

      const payload: AdminJwtPayload = {
        sub: admin.id,
        username: admin.username,
        is_super_admin: admin.is_super_admin,
      };

      const access_token = this.jwtService.sign(payload, {
        secret: this.configService.get<string>('ADMIN_JWT_SECRET'),
        expiresIn: '24h',
      });

      return {
        access_token,
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new HttpException(
        'Admin token refresh failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 验证管理员
   */
  async validateAdmin(payload: AdminJwtPayload): Promise<IAdminUser | null> {
    try {
      const admin = await this.adminRepository.findById(payload.sub);
      if (!admin || !admin.is_active) {
        return null;
      }
      return admin;
    } catch (error) {
      return null;
    }
  }
}
