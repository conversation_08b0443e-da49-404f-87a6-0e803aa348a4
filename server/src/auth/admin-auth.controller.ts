import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { AdminAuthService } from './admin-auth.service';
import { AdminJwtAuthGuard } from './guards/admin-jwt-auth.guard';
import { AdminLoginDto } from './dto/admin-login.dto';
import {
  AdminAuthResponseDto,
  AdminRefreshTokenResponseDto,
} from './dto/admin-auth-response.dto';
import { IAdminUser } from '../common/interfaces/entities';

@Controller('admin/auth')
export class AdminAuthController {
  constructor(private readonly adminAuthService: AdminAuthService) {}

  /**
   * 管理员登录
   */
  @Post('login')
  async adminLogin(
    @Body() adminLoginDto: AdminLoginDto,
  ): Promise<AdminAuthResponseDto> {
    return this.adminAuthService.adminLogin(adminLoginDto);
  }

  /**
   * 刷新管理员token
   */
  @Post('refresh')
  @UseGuards(AdminJwtAuthGuard)
  async refreshAdminToken(
    @Request() req: { user: IAdminUser },
  ): Promise<AdminRefreshTokenResponseDto> {
    return this.adminAuthService.refreshAdminToken(req.user.id);
  }
}
