import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { AdminAuthController } from './admin-auth.controller';
import { AdminAuthService } from './admin-auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { AdminJwtStrategy } from './strategies/admin-jwt.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AdminJwtAuthGuard } from './guards/admin-jwt-auth.guard';
import { SuperAdminGuard } from './guards/super-admin.guard';
import { OptionalJwtAuthGuard } from './guards/optional-jwt-auth.guard';
import { UserRepository } from '../user/user.repository';
import { AdminRepository } from '../admin/admin.repository';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    PassportModule,
    DatabaseModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        signOptions: { expiresIn: configService.get<string>('jwt.expiresIn') },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController, AdminAuthController],
  providers: [
    AuthService,
    AdminAuthService,
    JwtStrategy,
    AdminJwtStrategy,
    JwtAuthGuard,
    AdminJwtAuthGuard,
    SuperAdminGuard,
    OptionalJwtAuthGuard,
    UserRepository,
    AdminRepository,
  ],
  exports: [
    AuthService,
    AdminAuthService,
    JwtAuthGuard,
    AdminJwtAuthGuard,
    SuperAdminGuard,
    OptionalJwtAuthGuard,
  ],
})
export class AuthModule {}
