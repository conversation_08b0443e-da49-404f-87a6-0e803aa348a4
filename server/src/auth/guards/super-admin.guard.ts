import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { IAdminUser } from '../../common/interfaces/entities';

@Injectable()
export class SuperAdminGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const admin: IAdminUser = request.user;

    if (!admin) {
      throw new ForbiddenException('Admin authentication required');
    }

    if (!admin.is_super_admin) {
      throw new ForbiddenException('Super admin privileges required');
    }

    return true;
  }
}
