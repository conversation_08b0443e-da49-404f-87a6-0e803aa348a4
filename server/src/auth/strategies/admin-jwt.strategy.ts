import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AdminAuthService } from '../admin-auth.service';
import { AdminJwtPayload } from '../../common/interfaces';
import { IAdminUser } from '../../common/interfaces/entities';

@Injectable()
export class AdminJwtStrategy extends PassportStrategy(Strategy, 'admin-jwt') {
  constructor(
    private configService: ConfigService,
    private adminAuthService: AdminAuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey:
        configService.get<string>('jwt.adminSecret') ||
        'default-admin-jwt-secret',
    });
  }

  async validate(payload: AdminJwtPayload): Promise<IAdminUser> {
    try {
      const admin = await this.adminAuthService.validateAdmin(payload);
      if (!admin) {
        throw new UnauthorizedException('Invalid admin token');
      }
      return admin;
    } catch (error) {
      throw new UnauthorizedException('Invalid admin token');
    }
  }
}
