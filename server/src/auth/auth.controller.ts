import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { WechatLoginDto } from './dto/wechat-login.dto';
import {
  AuthResponseDto,
  RefreshTokenResponseDto,
} from './dto/auth-response.dto';
import { IUser } from '../common/interfaces/entities';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * 微信小程序登录
   */
  @Post('wechat-login')
  async wechatLogin(
    @Body() wechatLoginDto: WechatLoginDto,
  ): Promise<AuthResponseDto> {
    return this.authService.wechatLogin(wechatLoginDto);
  }

  /**
   * 刷新token
   */
  @Post('refresh')
  @UseGuards(JwtAuthGuard)
  async refreshToken(
    @Request() req: { user: IUser },
  ): Promise<RefreshTokenResponseDto> {
    return this.authService.refreshToken(req.user.id);
  }
}
