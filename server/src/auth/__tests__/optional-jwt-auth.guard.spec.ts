import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { OptionalJwtAuthGuard } from '../guards/optional-jwt-auth.guard';

describe('OptionalJwtAuthGuard', () => {
  let guard: OptionalJwtAuthGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OptionalJwtAuthGuard],
    }).compile();

    guard = module.get<OptionalJwtAuthGuard>(OptionalJwtAuthGuard);
  });

  describe('handleRequest', () => {
    let mockContext: ExecutionContext;

    beforeEach(() => {
      mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({}),
        }),
      } as ExecutionContext;
    });

    it('should return user when authentication is successful', () => {
      // Arrange
      const mockUser = { id: 'user-1', username: 'testuser' };

      // Act
      const result = guard.handleRequest(null, mockUser, null, mockContext);

      // Assert
      expect(result).toEqual(mockUser);
    });

    it('should return null when no user is found (no token)', () => {
      // Act
      const result = guard.handleRequest(null, null, null, mockContext);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when there is an error', () => {
      // Arrange
      const error = new Error('Some error');

      // Act
      const result = guard.handleRequest(error, null, null, mockContext);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when token is expired', () => {
      // Arrange
      const tokenExpiredInfo = { name: 'TokenExpiredError' };

      // Act
      const result = guard.handleRequest(
        null,
        null,
        tokenExpiredInfo,
        mockContext,
      );

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when token is invalid', () => {
      // Arrange
      const invalidTokenInfo = { name: 'JsonWebTokenError' };

      // Act
      const result = guard.handleRequest(
        null,
        null,
        invalidTokenInfo,
        mockContext,
      );

      // Assert
      expect(result).toBeNull();
    });
  });
});
