import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AuthService } from '../auth.service';
import { UserRepository } from '../../user/user.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { IUser, ICreateUserDto } from '../../common/interfaces/entities';
import { WechatLoginDto } from '../dto/wechat-login.dto';

// Mock fetch globally
global.fetch = jest.fn();

describe('AuthService', () => {
  let service: AuthService;
  let userRepository: jest.Mocked<UserRepository>;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: undefined,
    avatar_url: undefined,
    default_baby_id: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UserRepository,
          useValue: {
            findByOpenid: jest.fn(),
            createUser: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get(UserRepository);
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('wechatLogin', () => {
    const wechatLoginDto: WechatLoginDto = {
      code: 'test-code',
    };

    beforeEach(() => {
      configService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'WECHAT_APP_ID':
            return 'test-app-id';
          case 'WECHAT_APP_SECRET':
            return 'test-app-secret';
          default:
            return null;
        }
      });
    });

    it('should login existing user successfully', async () => {
      // Arrange
      const mockWechatResponse = {
        openid: 'test-openid',
        session_key: 'test-session-key',
      };
      (fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockWechatResponse),
      });
      userRepository.findByOpenid.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('test-jwt-token');

      // Act
      const result = await service.wechatLogin(wechatLoginDto);

      // Assert
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('api.weixin.qq.com/sns/jscode2session'),
      );
      expect(userRepository.findByOpenid).toHaveBeenCalledWith('test-openid');
      expect(userRepository.createUser).not.toHaveBeenCalled();
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
      });
      expect(result).toEqual({
        access_token: 'test-jwt-token',
        user: mockUser,
      });
    });

    it('should create new user and login successfully', async () => {
      // Arrange
      const mockWechatResponse = {
        openid: 'new-openid',
        session_key: 'test-session-key',
      };
      const newUser: IUser = {
        ...mockUser,
        id: 'new-user-id',
        openid: 'new-openid',
        username: undefined,
      };
      (fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockWechatResponse),
      });
      userRepository.findByOpenid.mockResolvedValue(null);
      userRepository.createUser.mockResolvedValue(newUser);
      jwtService.sign.mockReturnValue('test-jwt-token');

      // Act
      const result = await service.wechatLogin(wechatLoginDto);

      // Assert
      expect(userRepository.findByOpenid).toHaveBeenCalledWith('new-openid');
      expect(userRepository.createUser).toHaveBeenCalledWith({
        openid: 'new-openid',
      } as ICreateUserDto);
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: newUser.id,
        openid: newUser.openid,
        username: newUser.username,
      });
      expect(result).toEqual({
        access_token: 'test-jwt-token',
        user: newUser,
      });
    });

    it('should throw error when WeChat configuration is missing', async () => {
      // Arrange
      configService.get.mockReturnValue(null);

      // Act & Assert
      await expect(service.wechatLogin(wechatLoginDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          'WeChat configuration not found',
        ),
      );
    });

    it('should throw error when WeChat API returns error', async () => {
      // Arrange
      const mockWechatResponse = {
        errcode: 40013,
        errmsg: 'invalid appid',
      };
      (fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockWechatResponse),
      });

      // Act & Assert
      await expect(service.wechatLogin(wechatLoginDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          'WeChat API error: invalid appid',
        ),
      );
    });

    it('should throw error when WeChat API returns no openid', async () => {
      // Arrange
      const mockWechatResponse = {
        session_key: 'test-session-key',
      };
      (fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockWechatResponse),
      });

      // Act & Assert
      await expect(service.wechatLogin(wechatLoginDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          'Invalid WeChat code',
        ),
      );
    });

    it('should handle fetch error', async () => {
      // Arrange
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      // Act & Assert
      await expect(service.wechatLogin(wechatLoginDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_WECHAT_CODE,
          'Failed to get WeChat openid',
        ),
      );
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      // Arrange
      userRepository.findById.mockResolvedValue(mockUser);
      jwtService.sign.mockReturnValue('new-jwt-token');

      // Act
      const result = await service.refreshToken(mockUser.id);

      // Assert
      expect(userRepository.findById).toHaveBeenCalledWith(mockUser.id);
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
      });
      expect(result).toEqual({
        access_token: 'new-jwt-token',
      });
    });

    it('should throw error when user not found', async () => {
      // Arrange
      userRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.refreshToken('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_NOT_FOUND,
          'User not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should handle repository error', async () => {
      // Arrange
      userRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.refreshToken(mockUser.id)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('validateUser', () => {
    it('should validate user successfully', async () => {
      // Arrange
      const payload = {
        sub: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
      };
      userRepository.findById.mockResolvedValue(mockUser);

      // Act
      const result = await service.validateUser(payload);

      // Assert
      expect(userRepository.findById).toHaveBeenCalledWith(payload.sub);
      expect(result).toEqual(mockUser);
    });

    it('should return null when user not found', async () => {
      // Arrange
      const payload = {
        sub: 'non-existent-id',
        openid: 'test-openid',
        username: 'testuser',
      };
      userRepository.findById.mockResolvedValue(null);

      // Act
      const result = await service.validateUser(payload);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when repository throws error', async () => {
      // Arrange
      const payload = {
        sub: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
      };
      userRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await service.validateUser(payload);

      // Assert
      expect(result).toBeNull();
    });
  });
});
