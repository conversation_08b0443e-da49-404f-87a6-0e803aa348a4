import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AdminAuthService } from '../admin-auth.service';
import { AdminRepository } from '../../admin/admin.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { IAdminUser } from '../../common/interfaces/entities';
import { AdminLoginDto } from '../dto/admin-login.dto';

describe('AdminAuthService', () => {
  let service: AdminAuthService;
  let adminRepository: jest.Mocked<AdminRepository>;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;

  const mockAdmin: IAdminUser = {
    id: 'admin-id-1',
    username: 'testadmin',
    password_hash: '$2b$10$hashedpassword',
    email: '<EMAIL>',
    full_name: 'Test Admin',
    is_active: true,
    is_super_admin: false,
    last_login_at: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminAuthService,
        {
          provide: AdminRepository,
          useValue: {
            validatePassword: jest.fn(),
            updateLastLogin: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminAuthService>(AdminAuthService);
    adminRepository = module.get(AdminRepository);
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('adminLogin', () => {
    const adminLoginDto: AdminLoginDto = {
      username: 'testadmin',
      password: 'password123',
    };

    beforeEach(() => {
      configService.get.mockReturnValue('test-admin-jwt-secret');
    });

    it('should login admin successfully', async () => {
      // Arrange
      adminRepository.validatePassword.mockResolvedValue(mockAdmin);
      adminRepository.updateLastLogin.mockResolvedValue({
        ...mockAdmin,
        last_login_at: new Date(),
      });
      jwtService.sign.mockReturnValue('test-admin-jwt-token');

      // Act
      const result = await service.adminLogin(adminLoginDto);

      // Assert
      expect(adminRepository.validatePassword).toHaveBeenCalledWith(
        adminLoginDto.username,
        adminLoginDto.password,
      );
      expect(adminRepository.updateLastLogin).toHaveBeenCalledWith(
        mockAdmin.id,
      );
      expect(jwtService.sign).toHaveBeenCalledWith(
        {
          sub: mockAdmin.id,
          username: mockAdmin.username,
          is_super_admin: mockAdmin.is_super_admin,
        },
        {
          secret: 'test-admin-jwt-secret',
          expiresIn: '24h',
        },
      );
      expect(result).toEqual({
        access_token: 'test-admin-jwt-token',
        admin: {
          id: mockAdmin.id,
          username: mockAdmin.username,
          email: mockAdmin.email,
          full_name: mockAdmin.full_name,
          is_active: mockAdmin.is_active,
          is_super_admin: mockAdmin.is_super_admin,
          last_login_at: mockAdmin.last_login_at,
          created_at: mockAdmin.created_at,
          updated_at: mockAdmin.updated_at,
        },
      });
    });

    it('should throw error for invalid credentials', async () => {
      // Arrange
      adminRepository.validatePassword.mockResolvedValue(null);

      // Act & Assert
      await expect(service.adminLogin(adminLoginDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_ADMIN_CREDENTIALS,
          'Invalid username or password',
          HttpStatus.UNAUTHORIZED,
        ),
      );
      expect(adminRepository.updateLastLogin).not.toHaveBeenCalled();
      expect(jwtService.sign).not.toHaveBeenCalled();
    });

    it('should throw error for deactivated admin', async () => {
      // Arrange
      const deactivatedAdmin = { ...mockAdmin, is_active: false };
      adminRepository.validatePassword.mockResolvedValue(deactivatedAdmin);

      // Act & Assert
      await expect(service.adminLogin(adminLoginDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Admin account is deactivated',
          HttpStatus.FORBIDDEN,
        ),
      );
      expect(adminRepository.updateLastLogin).not.toHaveBeenCalled();
      expect(jwtService.sign).not.toHaveBeenCalled();
    });

    it('should handle repository error', async () => {
      // Arrange
      adminRepository.validatePassword.mockRejectedValue(
        new Error('Database error'),
      );

      // Act & Assert
      await expect(service.adminLogin(adminLoginDto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('refreshAdminToken', () => {
    beforeEach(() => {
      configService.get.mockReturnValue('test-admin-jwt-secret');
    });

    it('should refresh admin token successfully', async () => {
      // Arrange
      adminRepository.findById.mockResolvedValue(mockAdmin);
      jwtService.sign.mockReturnValue('new-admin-jwt-token');

      // Act
      const result = await service.refreshAdminToken(mockAdmin.id);

      // Assert
      expect(adminRepository.findById).toHaveBeenCalledWith(mockAdmin.id);
      expect(jwtService.sign).toHaveBeenCalledWith(
        {
          sub: mockAdmin.id,
          username: mockAdmin.username,
          is_super_admin: mockAdmin.is_super_admin,
        },
        {
          secret: 'test-admin-jwt-secret',
          expiresIn: '24h',
        },
      );
      expect(result).toEqual({
        access_token: 'new-admin-jwt-token',
      });
    });

    it('should throw error when admin not found', async () => {
      // Arrange
      adminRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.refreshAdminToken('non-existent-id'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_NOT_FOUND,
          'Admin not found',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw error for deactivated admin', async () => {
      // Arrange
      const deactivatedAdmin = { ...mockAdmin, is_active: false };
      adminRepository.findById.mockResolvedValue(deactivatedAdmin);

      // Act & Assert
      await expect(service.refreshAdminToken(mockAdmin.id)).rejects.toThrow(
        new BusinessException(
          ErrorCode.ADMIN_ACCESS_DENIED,
          'Admin account is deactivated',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should handle repository error', async () => {
      // Arrange
      adminRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.refreshAdminToken(mockAdmin.id)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('validateAdmin', () => {
    it('should validate admin successfully', async () => {
      // Arrange
      const payload = {
        sub: mockAdmin.id,
        username: mockAdmin.username,
        is_super_admin: mockAdmin.is_super_admin,
      };
      adminRepository.findById.mockResolvedValue(mockAdmin);

      // Act
      const result = await service.validateAdmin(payload);

      // Assert
      expect(adminRepository.findById).toHaveBeenCalledWith(payload.sub);
      expect(result).toEqual(mockAdmin);
    });

    it('should return null when admin not found', async () => {
      // Arrange
      const payload = {
        sub: 'non-existent-id',
        username: 'testadmin',
        is_super_admin: false,
      };
      adminRepository.findById.mockResolvedValue(null);

      // Act
      const result = await service.validateAdmin(payload);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when admin is deactivated', async () => {
      // Arrange
      const payload = {
        sub: mockAdmin.id,
        username: mockAdmin.username,
        is_super_admin: mockAdmin.is_super_admin,
      };
      const deactivatedAdmin = { ...mockAdmin, is_active: false };
      adminRepository.findById.mockResolvedValue(deactivatedAdmin);

      // Act
      const result = await service.validateAdmin(payload);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when repository throws error', async () => {
      // Arrange
      const payload = {
        sub: mockAdmin.id,
        username: mockAdmin.username,
        is_super_admin: mockAdmin.is_super_admin,
      };
      adminRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await service.validateAdmin(payload);

      // Assert
      expect(result).toBeNull();
    });
  });
});
