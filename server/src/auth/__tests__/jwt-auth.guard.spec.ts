import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

describe('JwtAuthGuard', () => {
  let guard: JwtAuthGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [JwtAuthGuard],
    }).compile();

    guard = module.get<JwtAuthGuard>(JwtAuthGuard);
  });

  describe('handleRequest', () => {
    let mockContext: ExecutionContext;
    let mockRequest: any;

    beforeEach(() => {
      mockRequest = {
        headers: {},
      };

      mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;
    });

    it('should return user when authentication is successful', () => {
      // Arrange
      const mockUser = { id: 'user-1', username: 'testuser' };

      // Act
      const result = guard.handleRequest(null, mockUser, null, mockContext);

      // Assert
      expect(result).toEqual(mockUser);
    });

    it('should throw UnauthorizedException when no token is provided', () => {
      // Arrange
      mockRequest.headers = {};

      // Act & Assert
      expect(() => guard.handleRequest(null, null, null, mockContext)).toThrow(
        new UnauthorizedException('Access token is required'),
      );
    });

    it('should throw UnauthorizedException when token is expired', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer expired-token',
      };
      const tokenExpiredInfo = { name: 'TokenExpiredError' };

      // Act & Assert
      expect(() =>
        guard.handleRequest(null, null, tokenExpiredInfo, mockContext),
      ).toThrow(new UnauthorizedException('Access token has expired'));
    });

    it('should throw UnauthorizedException when token is invalid', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer invalid-token',
      };
      const invalidTokenInfo = { name: 'JsonWebTokenError' };

      // Act & Assert
      expect(() =>
        guard.handleRequest(null, null, invalidTokenInfo, mockContext),
      ).toThrow(new UnauthorizedException('Invalid access token'));
    });

    it('should throw UnauthorizedException when there is an error', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer some-token',
      };
      const error = new Error('Some error');

      // Act & Assert
      expect(() => guard.handleRequest(error, null, null, mockContext)).toThrow(
        new UnauthorizedException('Authentication failed'),
      );
    });

    it('should throw UnauthorizedException when user is null with token present', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer some-token',
      };

      // Act & Assert
      expect(() => guard.handleRequest(null, null, null, mockContext)).toThrow(
        new UnauthorizedException('Authentication failed'),
      );
    });
  });
});
