import { Test, TestingModule } from '@nestjs/testing';
import { AdminAuthController } from '../admin-auth.controller';
import { AdminAuthService } from '../admin-auth.service';
import { AdminJwtAuthGuard } from '../guards/admin-jwt-auth.guard';
import { AdminLoginDto } from '../dto/admin-login.dto';
import {
  AdminAuthResponseDto,
  AdminRefreshTokenResponseDto,
} from '../dto/admin-auth-response.dto';
import { IAdminUser } from '../../common/interfaces/entities';

describe('AdminAuthController', () => {
  let controller: AdminAuthController;
  let adminAuthService: jest.Mocked<AdminAuthService>;

  const mockAdmin: IAdminUser = {
    id: 'admin-id-1',
    username: 'testadmin',
    password_hash: '$2b$10$hashedpassword',
    email: '<EMAIL>',
    full_name: 'Test Admin',
    is_active: true,
    is_super_admin: false,
    last_login_at: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminAuthController],
      providers: [
        {
          provide: AdminAuthService,
          useValue: {
            adminLogin: jest.fn(),
            refreshAdminToken: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(AdminJwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AdminAuthController>(AdminAuthController);
    adminAuthService = module.get(AdminAuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('adminLogin', () => {
    it('should login admin successfully', async () => {
      // Arrange
      const adminLoginDto: AdminLoginDto = {
        username: 'testadmin',
        password: 'password123',
      };
      const expectedResponse: AdminAuthResponseDto = {
        access_token: 'test-admin-jwt-token',
        admin: {
          id: mockAdmin.id,
          username: mockAdmin.username,
          email: mockAdmin.email,
          full_name: mockAdmin.full_name,
          is_active: mockAdmin.is_active,
          is_super_admin: mockAdmin.is_super_admin,
          last_login_at: mockAdmin.last_login_at,
          created_at: mockAdmin.created_at,
          updated_at: mockAdmin.updated_at,
        },
      };
      adminAuthService.adminLogin.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.adminLogin(adminLoginDto);

      // Assert
      expect(adminAuthService.adminLogin).toHaveBeenCalledWith(adminLoginDto);
      expect(result).toEqual(expectedResponse);
    });

    it('should handle service error', async () => {
      // Arrange
      const adminLoginDto: AdminLoginDto = {
        username: 'testadmin',
        password: 'wrongpassword',
      };
      const error = new Error('Admin login failed');
      adminAuthService.adminLogin.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.adminLogin(adminLoginDto)).rejects.toThrow(error);
      expect(adminAuthService.adminLogin).toHaveBeenCalledWith(adminLoginDto);
    });
  });

  describe('refreshAdminToken', () => {
    it('should refresh admin token successfully', async () => {
      // Arrange
      const req = { user: mockAdmin };
      const expectedResponse: AdminRefreshTokenResponseDto = {
        access_token: 'new-admin-jwt-token',
      };
      adminAuthService.refreshAdminToken.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.refreshAdminToken(req);

      // Assert
      expect(adminAuthService.refreshAdminToken).toHaveBeenCalledWith(
        mockAdmin.id,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockAdmin };
      const error = new Error('Admin token refresh failed');
      adminAuthService.refreshAdminToken.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.refreshAdminToken(req)).rejects.toThrow(error);
      expect(adminAuthService.refreshAdminToken).toHaveBeenCalledWith(
        mockAdmin.id,
      );
    });
  });
});
