import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AdminJwtAuthGuard } from '../guards/admin-jwt-auth.guard';

describe('AdminJwtAuthGuard', () => {
  let guard: AdminJwtAuthGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AdminJwtAuthGuard],
    }).compile();

    guard = module.get<AdminJwtAuthGuard>(AdminJwtAuthGuard);
  });

  describe('handleRequest', () => {
    let mockContext: ExecutionContext;
    let mockRequest: any;

    beforeEach(() => {
      mockRequest = {
        headers: {},
      };

      mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;
    });

    it('should return admin when authentication is successful', () => {
      // Arrange
      const mockAdmin = {
        id: 'admin-1',
        username: 'testadmin',
        is_super_admin: false,
      };

      // Act
      const result = guard.handleRequest(null, mockAdmin, null, mockContext);

      // Assert
      expect(result).toEqual(mockAdmin);
    });

    it('should throw UnauthorizedException when no token is provided', () => {
      // Arrange
      mockRequest.headers = {};

      // Act & Assert
      expect(() => guard.handleRequest(null, null, null, mockContext)).toThrow(
        new UnauthorizedException('Admin access token is required'),
      );
    });

    it('should throw UnauthorizedException when token is expired', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer expired-token',
      };
      const tokenExpiredInfo = { name: 'TokenExpiredError' };

      // Act & Assert
      expect(() =>
        guard.handleRequest(null, null, tokenExpiredInfo, mockContext),
      ).toThrow(new UnauthorizedException('Admin access token has expired'));
    });

    it('should throw UnauthorizedException when token is invalid', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer invalid-token',
      };
      const invalidTokenInfo = { name: 'JsonWebTokenError' };

      // Act & Assert
      expect(() =>
        guard.handleRequest(null, null, invalidTokenInfo, mockContext),
      ).toThrow(new UnauthorizedException('Invalid admin access token'));
    });

    it('should throw UnauthorizedException when there is an error', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer some-token',
      };
      const error = new Error('Some error');

      // Act & Assert
      expect(() => guard.handleRequest(error, null, null, mockContext)).toThrow(
        new UnauthorizedException('Admin authentication failed'),
      );
    });

    it('should throw UnauthorizedException when admin is null with token present', () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'Bearer some-token',
      };

      // Act & Assert
      expect(() => guard.handleRequest(null, null, null, mockContext)).toThrow(
        new UnauthorizedException('Admin authentication failed'),
      );
    });
  });
});
