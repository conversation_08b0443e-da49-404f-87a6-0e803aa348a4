import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { SuperAdminGuard } from '../guards/super-admin.guard';
import { IAdminUser } from '../../common/interfaces/entities';

describe('SuperAdminGuard', () => {
  let guard: SuperAdminGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SuperAdminGuard],
    }).compile();

    guard = module.get<SuperAdminGuard>(SuperAdminGuard);
  });

  describe('canActivate', () => {
    let mockContext: ExecutionContext;
    let mockRequest: any;

    beforeEach(() => {
      mockRequest = {
        user: null,
      };

      mockContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
        }),
      } as ExecutionContext;
    });

    it('should allow access for super admin', () => {
      // Arrange
      const superAdmin: IAdminUser = {
        id: 'admin-1',
        username: 'superadmin',
        password_hash: 'hash',
        email: '<EMAIL>',
        full_name: 'Super Admin',
        is_active: true,
        is_super_admin: true,
        last_login_at: undefined,
        created_at: new Date(),
        updated_at: new Date(),
      };
      mockRequest.user = superAdmin;

      // Act
      const result = guard.canActivate(mockContext);

      // Assert
      expect(result).toBe(true);
    });

    it('should deny access for regular admin', () => {
      // Arrange
      const regularAdmin: IAdminUser = {
        id: 'admin-2',
        username: 'regularadmin',
        password_hash: 'hash',
        email: '<EMAIL>',
        full_name: 'Regular Admin',
        is_active: true,
        is_super_admin: false,
        last_login_at: undefined,
        created_at: new Date(),
        updated_at: new Date(),
      };
      mockRequest.user = regularAdmin;

      // Act & Assert
      expect(() => guard.canActivate(mockContext)).toThrow(
        new ForbiddenException('Super admin privileges required'),
      );
    });

    it('should deny access when no admin is authenticated', () => {
      // Arrange
      mockRequest.user = null;

      // Act & Assert
      expect(() => guard.canActivate(mockContext)).toThrow(
        new ForbiddenException('Admin authentication required'),
      );
    });

    it('should deny access when admin is undefined', () => {
      // Arrange
      mockRequest.user = undefined;

      // Act & Assert
      expect(() => guard.canActivate(mockContext)).toThrow(
        new ForbiddenException('Admin authentication required'),
      );
    });
  });
});
