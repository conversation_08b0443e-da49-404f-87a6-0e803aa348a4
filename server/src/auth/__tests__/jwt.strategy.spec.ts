import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { JwtStrategy } from '../strategies/jwt.strategy';
import { AuthService } from '../auth.service';
import { JwtPayload } from '../../common/interfaces';
import { IUser } from '../../common/interfaces/entities';

describe('JwtStrategy', () => {
  let strategy: JwtStrategy;
  let authService: jest.Mocked<AuthService>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: undefined,
    avatar_url: undefined,
    default_baby_id: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-jwt-secret'),
          },
        },
        {
          provide: AuthService,
          useValue: {
            validateUser: jest.fn(),
          },
        },
      ],
    }).compile();

    strategy = module.get<JwtStrategy>(JwtStrategy);
    authService = module.get(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validate', () => {
    it('should validate user successfully', async () => {
      // Arrange
      const payload: JwtPayload = {
        sub: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
      };
      authService.validateUser.mockResolvedValue(mockUser);

      // Act
      const result = await strategy.validate(payload);

      // Assert
      expect(authService.validateUser).toHaveBeenCalledWith(payload);
      expect(result).toEqual(mockUser);
    });

    it('should throw UnauthorizedException when user not found', async () => {
      // Arrange
      const payload: JwtPayload = {
        sub: 'non-existent-id',
        openid: 'test-openid',
        username: 'testuser',
      };
      authService.validateUser.mockResolvedValue(null);

      // Act & Assert
      await expect(strategy.validate(payload)).rejects.toThrow(
        new UnauthorizedException('Invalid token'),
      );
      expect(authService.validateUser).toHaveBeenCalledWith(payload);
    });

    it('should throw UnauthorizedException when validateUser throws error', async () => {
      // Arrange
      const payload: JwtPayload = {
        sub: mockUser.id,
        openid: mockUser.openid,
        username: mockUser.username,
      };
      authService.validateUser.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(strategy.validate(payload)).rejects.toThrow(
        new UnauthorizedException('Invalid token'),
      );
    });
  });
});
