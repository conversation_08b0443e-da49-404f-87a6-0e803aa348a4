import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from '../auth.controller';
import { AuthService } from '../auth.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { WechatLoginDto } from '../dto/wechat-login.dto';
import {
  AuthResponseDto,
  RefreshTokenResponseDto,
} from '../dto/auth-response.dto';
import { IUser } from '../../common/interfaces/entities';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: jest.Mocked<AuthService>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: undefined,
    avatar_url: undefined,
    default_baby_id: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            wechatLogin: jest.fn(),
            refreshToken: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('wechatLogin', () => {
    it('should login with WeChat successfully', async () => {
      // Arrange
      const wechatLoginDto: WechatLoginDto = {
        code: 'test-code',
      };
      const expectedResponse: AuthResponseDto = {
        access_token: 'test-jwt-token',
        user: mockUser,
      };
      authService.wechatLogin.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.wechatLogin(wechatLoginDto);

      // Assert
      expect(authService.wechatLogin).toHaveBeenCalledWith(wechatLoginDto);
      expect(result).toEqual(expectedResponse);
    });

    it('should handle service error', async () => {
      // Arrange
      const wechatLoginDto: WechatLoginDto = {
        code: 'invalid-code',
      };
      const error = new Error('WeChat login failed');
      authService.wechatLogin.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.wechatLogin(wechatLoginDto)).rejects.toThrow(
        error,
      );
      expect(authService.wechatLogin).toHaveBeenCalledWith(wechatLoginDto);
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const expectedResponse: RefreshTokenResponseDto = {
        access_token: 'new-jwt-token',
      };
      authService.refreshToken.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.refreshToken(req);

      // Assert
      expect(authService.refreshToken).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual(expectedResponse);
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const error = new Error('Token refresh failed');
      authService.refreshToken.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.refreshToken(req)).rejects.toThrow(error);
      expect(authService.refreshToken).toHaveBeenCalledWith(mockUser.id);
    });
  });
});
