import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { AdminJwtStrategy } from '../strategies/admin-jwt.strategy';
import { AdminAuthService } from '../admin-auth.service';
import { AdminJwtPayload } from '../../common/interfaces';
import { IAdminUser } from '../../common/interfaces/entities';

describe('AdminJwtStrategy', () => {
  let strategy: AdminJwtStrategy;
  let adminAuthService: jest.Mocked<AdminAuthService>;

  const mockAdmin: IAdminUser = {
    id: 'admin-id-1',
    username: 'testadmin',
    password_hash: '$2b$10$hashedpassword',
    email: '<EMAIL>',
    full_name: 'Test Admin',
    is_active: true,
    is_super_admin: false,
    last_login_at: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminJwtStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-admin-jwt-secret'),
          },
        },
        {
          provide: AdminAuthService,
          useValue: {
            validateAdmin: jest.fn(),
          },
        },
      ],
    }).compile();

    strategy = module.get<AdminJwtStrategy>(AdminJwtStrategy);
    adminAuthService = module.get(AdminAuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validate', () => {
    it('should validate admin successfully', async () => {
      // Arrange
      const payload: AdminJwtPayload = {
        sub: mockAdmin.id,
        username: mockAdmin.username,
        is_super_admin: mockAdmin.is_super_admin,
      };
      adminAuthService.validateAdmin.mockResolvedValue(mockAdmin);

      // Act
      const result = await strategy.validate(payload);

      // Assert
      expect(adminAuthService.validateAdmin).toHaveBeenCalledWith(payload);
      expect(result).toEqual(mockAdmin);
    });

    it('should throw UnauthorizedException when admin not found', async () => {
      // Arrange
      const payload: AdminJwtPayload = {
        sub: 'non-existent-id',
        username: 'testadmin',
        is_super_admin: false,
      };
      adminAuthService.validateAdmin.mockResolvedValue(null);

      // Act & Assert
      await expect(strategy.validate(payload)).rejects.toThrow(
        new UnauthorizedException('Invalid admin token'),
      );
      expect(adminAuthService.validateAdmin).toHaveBeenCalledWith(payload);
    });

    it('should throw UnauthorizedException when validateAdmin throws error', async () => {
      // Arrange
      const payload: AdminJwtPayload = {
        sub: mockAdmin.id,
        username: mockAdmin.username,
        is_super_admin: mockAdmin.is_super_admin,
      };
      adminAuthService.validateAdmin.mockRejectedValue(
        new Error('Database error'),
      );

      // Act & Assert
      await expect(strategy.validate(payload)).rejects.toThrow(
        new UnauthorizedException('Invalid admin token'),
      );
    });
  });
});
