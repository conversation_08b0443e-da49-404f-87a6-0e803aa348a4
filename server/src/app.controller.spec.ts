import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HabitStatisticsService } from './habits/services/habit-statistics.service';
import { appConfig } from './config';

describe('AppController', () => {
  let appController: AppController;

  beforeEach(async () => {
    const mockHabitStatisticsService = {
      getOverviewStatistics: jest.fn().mockResolvedValue({
        today_completion_rate: 0.8,
        today_completed_count: 4,
        today_total_count: 5,
        today_timeline: [
          {
            habit_id: '1',
            habit_name: '刷牙',
            habit_icon: 'tooth',
            theme_color: '#FF6B6B',
            is_completed: true,
            check_in_time: new Date(),
            preferred_time: '08:00',
          },
        ],
      }),
    };

    const app: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [appConfig],
        }),
      ],
      controllers: [AppController],
      providers: [
        AppService,
        {
          provide: HabitStatisticsService,
          useValue: mockHabitStatisticsService,
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(appController.getHello()).toBe('Hello World!');
    });
  });

  describe('health', () => {
    it('should return health status', () => {
      const health = appController.getHealth();
      expect(health).toHaveProperty('status', 'ok');
      expect(health).toHaveProperty('timestamp');
      expect(health).toHaveProperty('environment');
      expect(health).toHaveProperty('version', '1.0.0');
      expect(typeof health.timestamp).toBe('string');
    });
  });

  describe('homepage habits overview', () => {
    it('should return homepage habits overview statistics', async () => {
      const mockUser = { id: 'user-1' };
      const babyId = 'baby-1';

      const result = await appController.getHomepageHabitsOverview(
        mockUser,
        babyId,
      );

      expect(result).toHaveProperty('today_completion_rate', 0.8);
      expect(result).toHaveProperty('today_completed_count', 4);
      expect(result).toHaveProperty('today_total_count', 5);
      expect(result).toHaveProperty('today_timeline');
      expect(Array.isArray(result.today_timeline)).toBe(true);
      expect(result.today_timeline[0]).toHaveProperty('habit_id');
      expect(result.today_timeline[0]).toHaveProperty('habit_name');
      expect(result.today_timeline[0]).toHaveProperty('is_completed');
    });
  });
});
