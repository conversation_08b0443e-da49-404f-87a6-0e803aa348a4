/**
 * 习惯模块类型定义
 */

// 分页查询参数
export interface IPaginationQuery {
  limit?: number;
  offset?: number;
  page?: number;
  pageSize?: number;
}

// 排序参数
export interface ISortQuery {
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// 基础查询参数
export interface IBaseQuery extends IPaginationQuery, ISortQuery {}

// 日期范围查询
export interface IDateRangeQuery {
  startDate?: Date;
  endDate?: Date;
}

// 习惯库查询参数
export interface IHabitLibraryQuery extends IBaseQuery {
  categoryId?: string;
  isActive?: boolean;
}

// 用户习惯查询参数
export interface IUserHabitQuery extends IBaseQuery {
  babyId?: string;
  status?: string;
  categoryId?: string;
}

// 打卡记录查询参数
export interface ICheckInQuery extends IBaseQuery, IDateRangeQuery {
  habitId?: string;
  babyId?: string;
}

// API响应包装类型
export interface IApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    timestamp: string;
  };
}

// 分页响应类型
export interface IPaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
