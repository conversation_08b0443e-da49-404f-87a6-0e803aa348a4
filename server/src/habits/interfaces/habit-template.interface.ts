/**
 * 习惯模板接口
 * 用于管理预配置的习惯模板
 */
export interface IHabitTemplate {
  id: string;
  category_id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * 创建习惯模板的数据接口
 */
export interface ICreateHabitTemplate {
  category_id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  is_active?: boolean;
}

/**
 * 更新习惯模板的数据接口
 */
export interface IUpdateHabitTemplate {
  category_id?: string;
  name?: string;
  icon?: string;
  theme_color?: string;
  description?: string;
  is_active?: boolean;
}
