/**
 * 用户习惯状态枚举
 */
export enum UserHabitStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

/**
 * 用户习惯接口
 * 用于管理用户创建的习惯
 */
export interface IUserHabit {
  id: string;
  user_id: string;
  baby_id: string;
  template_id?: string; // 可选，如果基于模板创建
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[]; // 1-7 表示周一到周日
  preferred_time?: string; // HH:mm 格式
  reward_stars: number;
  status: UserHabitStatus;
  created_at: Date;
  updated_at: Date;
}

/**
 * 创建用户习惯的数据接口
 */
export interface ICreateUserHabit {
  baby_id: string;
  template_id?: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  reward_stars?: number;
  status?: UserHabitStatus;
}

/**
 * 更新用户习惯的数据接口
 */
export interface IUpdateUserHabit {
  name?: string;
  icon?: string;
  theme_color?: string;
  description?: string;
  frequency?: number[];
  preferred_time?: string;
  reward_stars?: number;
  status?: UserHabitStatus;
}
