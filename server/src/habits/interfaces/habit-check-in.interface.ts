/**
 * 习惯打卡接口
 * 用于记录用户的习惯打卡信息
 */
export interface IHabitCheckIn {
  id: string;
  habit_id: string;
  user_id: string;
  baby_id: string;
  check_in_date: Date; // 打卡日期（不含时间）
  check_in_time: Date; // 实际打卡时间
  notes?: string;
  created_at: Date;
}

/**
 * 创建习惯打卡的数据接口
 */
export interface ICreateHabitCheckIn {
  habit_id: string;
  baby_id: string;
  check_in_date?: Date; // 默认为今天
  notes?: string;
}

/**
 * 打卡查询条件接口
 */
export interface IHabitCheckInQuery {
  habit_id?: string;
  baby_id?: string;
  start_date?: Date;
  end_date?: Date;
  limit?: number;
  offset?: number;
}
