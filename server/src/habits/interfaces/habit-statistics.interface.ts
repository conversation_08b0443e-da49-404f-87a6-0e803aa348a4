/**
 * 习惯统计视图类型
 */
export enum StatisticsViewType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

/**
 * 日统计数据接口
 */
export interface IDailyStatistics {
  date: Date;
  habits: Array<{
    habit_id: string;
    habit_name: string;
    habit_icon: string;
    theme_color: string;
    is_completed: boolean;
    check_in_time?: Date;
  }>;
  completion_rate: number; // 完成率 0-1
}

/**
 * 周统计数据接口
 */
export interface IWeeklyStatistics {
  week_start: Date;
  week_end: Date;
  daily_completion: Array<{
    date: Date;
    completion_rate: number;
    completed_count: number;
    total_count: number;
  }>;
  overall_completion_rate: number;
}

/**
 * 月统计数据接口
 */
export interface IMonthlyStatistics {
  year: number;
  month: number;
  calendar_data: Array<{
    date: Date;
    completion_rate: number;
    completed_count: number;
    total_count: number;
  }>;
  overall_completion_rate: number;
}

/**
 * 首页概览统计接口
 */
export interface IOverviewStatistics {
  today_completion_rate: number;
  today_completed_count: number;
  today_total_count: number;
  today_timeline: Array<{
    habit_id: string;
    habit_name: string;
    habit_icon: string;
    theme_color: string;
    is_completed: boolean;
    check_in_time?: Date;
    preferred_time?: string;
  }>;
}

/**
 * 习惯详情统计接口
 */
export interface IHabitDetailStatistics {
  habit_id: string;
  current_month_data: Array<{
    date: Date;
    is_completed: boolean;
    check_in_time?: Date;
    notes?: string;
  }>;
  completion_rate: number;
  streak_days: number; // 连续天数
  total_check_ins: number;
}
