/**
 * 习惯分类接口
 * 用于管理习惯的分类信息，支持层级结构
 */
export interface IHabitCategory {
  id: string;
  name: string;
  icon: string;
  parent_id?: string; // 父分类ID，null表示一级分类
  level: number; // 分类层级：1表示一级分类，2表示二级分类
  sort_order: number;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;

  // 关联关系（可选，用于查询结果）
  parent?: IHabitCategory; // 父分类
  children?: IHabitCategory[]; // 子分类列表
}

/**
 * 创建习惯分类的数据接口
 */
export interface ICreateHabitCategory {
  name: string;
  icon: string;
  parent_id?: string; // 父分类ID，创建二级分类时必须提供
  level: number; // 分类层级：1或2
  sort_order?: number;
  is_active?: boolean;
}

/**
 * 更新习惯分类的数据接口
 */
export interface IUpdateHabitCategory {
  name?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
  // 注意：不允许更新 parent_id 和 level，层级关系一旦创建不可修改
}
/**
 * 习惯
分类树形结构接口
 */
export interface IHabitCategoryTree extends IHabitCategory {
  children: IHabitCategoryTree[];
}

/**
 * 习惯分类查询选项接口
 */
export interface IHabitCategoryQueryOptions {
  level?: number; // 查询指定层级的分类
  parent_id?: string; // 查询指定父分类下的子分类
  is_active?: boolean; // 查询激活状态的分类
  include_children?: boolean; // 是否包含子分类
  include_parent?: boolean; // 是否包含父分类信息
}

/**
 * 习惯分类层级验证结果接口
 */
export interface IHabitCategoryHierarchyValidation {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}
