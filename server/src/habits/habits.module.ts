import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database';
import { BabyModule } from '../baby/baby.module';

// Repositories
import { HabitTemplateRepository } from './repositories/habit-template.repository';
import { HabitCategoryRepository } from './repositories/habit-category.repository';
import { UserHabitRepository } from './repositories/user-habit.repository';
import { HabitCheckInRepository } from './repositories/habit-checkin.repository';

// Services
import { HabitTemplateService } from './services/habit-template.service';
import { HabitCategoryService } from './services/habit-category.service';
import { UserHabitService } from './services/user-habit.service';
import { HabitCheckInService } from './services/habit-checkin.service';
import { HabitStatisticsService } from './services/habit-statistics.service';

// Admin Controllers
import { AdminHabitTemplateController } from './controllers/admin/admin-habit-template.controller';
import { AdminHabitCategoryController } from './controllers/admin/admin-habit-category.controller';

// Client Controllers
import { HabitLibraryController } from './controllers/client/habit-library.controller';
import { UserHabitController } from './controllers/client/user-habit.controller';
import { HabitCheckInController } from './controllers/client/habit-checkin.controller';
import { HabitStatisticsController } from './controllers/client/habit-statistics.controller';

/**
 * 习惯模块
 * 整合所有习惯相关功能的主模块
 */
@Module({
  imports: [DatabaseModule, BabyModule],
  controllers: [
    // Admin Controllers
    AdminHabitTemplateController,
    AdminHabitCategoryController,
    // Client Controllers
    HabitLibraryController,
    UserHabitController,
    HabitCheckInController,
    HabitStatisticsController,
  ],
  providers: [
    // Repositories
    HabitTemplateRepository,
    HabitCategoryRepository,
    UserHabitRepository,
    HabitCheckInRepository,
    // Services
    HabitTemplateService,
    HabitCategoryService,
    UserHabitService,
    HabitCheckInService,
    HabitStatisticsService,
  ],
  exports: [
    // Repositories
    HabitTemplateRepository,
    HabitCategoryRepository,
    UserHabitRepository,
    HabitCheckInRepository,
    // Services
    HabitTemplateService,
    HabitCategoryService,
    UserHabitService,
    HabitCheckInService,
    HabitStatisticsService,
  ],
})
export class HabitsModule {}
