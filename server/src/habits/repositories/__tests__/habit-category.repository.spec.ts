import { Test, TestingModule } from '@nestjs/testing';
import { HabitCategoryRepository } from '../habit-category.repository';
import { SupabaseService } from '../../../database/supabase.service';
import {
  IHabitCategory,
  ICreateHabitCategory,
} from '../../interfaces/habit-category.interface';

describe('HabitCategoryRepository', () => {
  let repository: HabitCategoryRepository;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockCategory: IHabitCategory = {
    id: '123e4567-e89b-12d3-a456-************',
    name: '生活自理',
    icon: 'life-icon',
    sort_order: 1,
    is_active: true,
    created_at: new Date('2024-01-01T00:00:00Z'),
    updated_at: new Date('2024-01-01T00:00:00Z'),
  };

  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn(),
    range: jest.fn().mockReturnThis(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      handleDatabaseError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitCategoryRepository,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    repository = module.get<HabitCategoryRepository>(HabitCategoryRepository);
    supabaseService = module.get(SupabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createCategory', () => {
    it('should create a habit category with default values', async () => {
      const createData: ICreateHabitCategory = {
        name: '生活自理',
        icon: 'life-icon',
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: mockCategory,
        error: null,
      });

      const result = await repository.createCategory(createData);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_categories');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith({
        name: '生活自理',
        icon: 'life-icon',
        sort_order: 0,
        is_active: true,
      });
      expect(result).toEqual(mockCategory);
    });

    it('should create a habit category with custom values', async () => {
      const createData: ICreateHabitCategory = {
        name: '健康习惯',
        icon: 'health-icon',
        sort_order: 2,
        is_active: false,
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: { ...mockCategory, ...createData },
        error: null,
      });

      const result = await repository.createCategory(createData);

      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(createData);
      expect(result.name).toBe('健康习惯');
      expect(result.sort_order).toBe(2);
      expect(result.is_active).toBe(false);
    });

    it('should throw error when name is missing', async () => {
      const createData = {
        icon: 'life-icon',
      } as ICreateHabitCategory;

      await expect(repository.createCategory(createData)).rejects.toThrow();
    });

    it('should throw error when icon is missing', async () => {
      const createData = {
        name: '生活自理',
      } as ICreateHabitCategory;

      await expect(repository.createCategory(createData)).rejects.toThrow();
    });
  });

  describe('findActiveCategories', () => {
    it('should return active categories ordered by sort_order', async () => {
      const mockCategories = [mockCategory];
      mockQueryBuilder.eq.mockReturnThis();
      mockQueryBuilder.order.mockReturnThis();

      supabaseService.query.mockReturnValue({
        ...mockQueryBuilder,
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
      } as any);

      // Mock the final result
      const finalQuery = {
        data: mockCategories,
        error: null,
      };

      supabaseService.query().select().eq().order().order = jest
        .fn()
        .mockResolvedValue(finalQuery);

      const result = await repository.findActiveCategories();

      expect(supabaseService.query).toHaveBeenCalledWith('habit_categories');
      expect(result).toEqual(mockCategories);
    });

    it('should return empty array when no active categories found', async () => {
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
      } as any);

      // Mock the final result
      supabaseService.query().select().eq().order().order = jest
        .fn()
        .mockResolvedValue({
          data: null,
          error: null,
        });

      const result = await repository.findActiveCategories();

      expect(result).toEqual([]);
    });
  });

  describe('findAllCategories', () => {
    it('should return all categories ordered by sort_order', async () => {
      const mockCategories = [mockCategory];

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
      } as any);

      // Mock the final result
      supabaseService.query().select().order().order = jest
        .fn()
        .mockResolvedValue({
          data: mockCategories,
          error: null,
        });

      const result = await repository.findAllCategories();

      expect(supabaseService.query).toHaveBeenCalledWith('habit_categories');
      expect(result).toEqual(mockCategories);
    });
  });

  describe('findByName', () => {
    it('should find category by name', async () => {
      mockQueryBuilder.single.mockResolvedValue({
        data: mockCategory,
        error: null,
      });

      const result = await repository.findByName('生活自理');

      expect(supabaseService.query).toHaveBeenCalledWith('habit_categories');
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('name', '生活自理');
      expect(result).toEqual(mockCategory);
    });

    it('should return null when category not found', async () => {
      mockQueryBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await repository.findByName('不存在的分类');

      expect(result).toBeNull();
    });

    it('should return null when name is empty', async () => {
      const result = await repository.findByName('');
      expect(result).toBeNull();
    });
  });

  describe('hasTemplates', () => {
    it('should return true when category has templates', async () => {
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 5,
          error: null,
        }),
      } as any);

      const result = await repository.hasTemplates(mockCategory.id);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(result).toBe(true);
    });

    it('should return false when category has no templates', async () => {
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 0,
          error: null,
        }),
      } as any);

      const result = await repository.hasTemplates(mockCategory.id);

      expect(result).toBe(false);
    });

    it('should return false for invalid UUID', async () => {
      const result = await repository.hasTemplates('invalid-uuid');
      expect(result).toBe(false);
    });
  });

  describe('getNextSortOrder', () => {
    it('should return next sort order', async () => {
      mockQueryBuilder.single.mockResolvedValue({
        data: { sort_order: 5 },
        error: null,
      });

      const result = await repository.getNextSortOrder();

      expect(supabaseService.query).toHaveBeenCalledWith('habit_categories');
      expect(mockQueryBuilder.order).toHaveBeenCalledWith('sort_order', {
        ascending: false,
      });
      expect(mockQueryBuilder.limit).toHaveBeenCalledWith(1);
      expect(result).toBe(6);
    });

    it('should return 1 when no categories exist', async () => {
      mockQueryBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await repository.getNextSortOrder();

      expect(result).toBe(1);
    });
  });

  describe('reorderCategories', () => {
    it('should reorder categories successfully', async () => {
      const categoryOrders = [
        { id: '123e4567-e89b-12d3-a456-************', sort_order: 1 },
        { id: '123e4567-e89b-12d3-a456-************', sort_order: 2 },
      ];

      mockQueryBuilder.single.mockResolvedValue({
        data: mockCategory,
        error: null,
      });

      await repository.reorderCategories(categoryOrders);

      expect(supabaseService.query).toHaveBeenCalledTimes(2);
      expect(mockQueryBuilder.update).toHaveBeenCalledTimes(2);
    });

    it('should skip invalid UUIDs during reordering', async () => {
      const categoryOrders = [
        { id: 'invalid-uuid', sort_order: 1 },
        { id: '123e4567-e89b-12d3-a456-************', sort_order: 2 },
      ];

      mockQueryBuilder.single.mockResolvedValue({
        data: mockCategory,
        error: null,
      });

      await repository.reorderCategories(categoryOrders);

      // Should only call update once for the valid UUID
      expect(mockQueryBuilder.update).toHaveBeenCalledTimes(1);
    });
  });
});
