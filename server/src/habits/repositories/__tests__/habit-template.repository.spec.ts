import { Test, TestingModule } from '@nestjs/testing';
import { HabitTemplateRepository } from '../habit-template.repository';
import { SupabaseService } from '../../../database/supabase.service';
import {
  IHabitTemplate,
  ICreateHabitTemplate,
} from '../../interfaces/habit-template.interface';

describe('HabitTemplateRepository', () => {
  let repository: HabitTemplateRepository;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockTemplate: IHabitTemplate = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    category_id: '123e4567-e89b-12d3-a456-************',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
    is_active: true,
    created_at: new Date('2024-01-01T00:00:00Z'),
    updated_at: new Date('2024-01-01T00:00:00Z'),
  };

  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      handleDatabaseError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitTemplateRepository,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    repository = module.get<HabitTemplateRepository>(HabitTemplateRepository);
    supabaseService = module.get(SupabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTemplate', () => {
    it('should create a habit template with default values', async () => {
      const createData: ICreateHabitTemplate = {
        category_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: mockTemplate,
        error: null,
      });

      const result = await repository.createTemplate(createData);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith({
        ...createData,
        is_active: true,
      });
      expect(result).toEqual(mockTemplate);
    });

    it('should create a habit template with custom is_active value', async () => {
      const createData: ICreateHabitTemplate = {
        category_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        is_active: false,
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: { ...mockTemplate, is_active: false },
        error: null,
      });

      const result = await repository.createTemplate(createData);

      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(createData);
      expect(result.is_active).toBe(false);
    });

    it('should throw error when required fields are missing', async () => {
      const createData = {
        name: '睡前刷牙',
      } as ICreateHabitTemplate;

      await expect(repository.createTemplate(createData)).rejects.toThrow();
    });

    it('should throw error when theme_color is invalid', async () => {
      const createData: ICreateHabitTemplate = {
        category_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: 'invalid-color',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
      };

      await expect(repository.createTemplate(createData)).rejects.toThrow();
    });
  });

  describe('findActiveTemplates', () => {
    it('should return active templates ordered by created_at', async () => {
      const mockTemplates = [mockTemplate];

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.findActiveTemplates();

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(result).toEqual(mockTemplates);
    });

    it('should return empty array when no active templates found', async () => {
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      } as any);

      const result = await repository.findActiveTemplates();

      expect(result).toEqual([]);
    });
  });

  describe('findByCategory', () => {
    it('should return templates for a specific category', async () => {
      const mockTemplates = [mockTemplate];
      const categoryId = '123e4567-e89b-12d3-a456-************';

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.findByCategory(categoryId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(result).toEqual(mockTemplates);
    });

    it('should include inactive templates when includeInactive is true', async () => {
      const categoryId = '123e4567-e89b-12d3-a456-************';
      const mockTemplates = [
        mockTemplate,
        { ...mockTemplate, is_active: false },
      ];

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.findByCategory(categoryId, true);

      expect(result).toEqual(mockTemplates);
    });

    it('should return empty array for invalid UUID', async () => {
      const result = await repository.findByCategory('invalid-uuid');
      expect(result).toEqual([]);
    });
  });

  describe('findByName', () => {
    it('should find template by name', async () => {
      mockQueryBuilder.single.mockResolvedValue({
        data: mockTemplate,
        error: null,
      });

      const result = await repository.findByName('睡前刷牙');

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('name', '睡前刷牙');
      expect(result).toEqual(mockTemplate);
    });

    it('should find template by name within specific category', async () => {
      const categoryId = '123e4567-e89b-12d3-a456-************';
      mockQueryBuilder.single.mockResolvedValue({
        data: mockTemplate,
        error: null,
      });

      const result = await repository.findByName('睡前刷牙', categoryId);

      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('name', '睡前刷牙');
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith(
        'category_id',
        categoryId,
      );
      expect(result).toEqual(mockTemplate);
    });

    it('should return null when template not found', async () => {
      mockQueryBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await repository.findByName('不存在的模板');

      expect(result).toBeNull();
    });

    it('should return null when name is empty', async () => {
      const result = await repository.findByName('');
      expect(result).toBeNull();
    });
  });

  describe('findByIdWithCategory', () => {
    it('should find template with category information', async () => {
      const mockTemplateWithCategory = {
        ...mockTemplate,
        category: {
          id: '123e4567-e89b-12d3-a456-************',
          name: '生活自理',
          icon: 'life-icon',
        },
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: mockTemplateWithCategory,
        error: null,
      });

      const result = await repository.findByIdWithCategory(mockTemplate.id);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(
        expect.stringContaining('category:habit_categories'),
      );
      expect(result).toEqual(mockTemplateWithCategory);
    });

    it('should return null for invalid UUID', async () => {
      const result = await repository.findByIdWithCategory('invalid-uuid');
      expect(result).toBeNull();
    });
  });

  describe('countByCategory', () => {
    it('should return count of templates in category', async () => {
      const categoryId = '123e4567-e89b-12d3-a456-************';

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 5,
          error: null,
        }),
      } as any);

      const result = await repository.countByCategory(categoryId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(result).toBe(5);
    });

    it('should return 0 for invalid UUID', async () => {
      const result = await repository.countByCategory('invalid-uuid');
      expect(result).toBe(0);
    });
  });

  describe('createBatch', () => {
    it('should create multiple templates at once', async () => {
      const templates: ICreateHabitTemplate[] = [
        {
          category_id: '123e4567-e89b-12d3-a456-************',
          name: '睡前刷牙',
          icon: 'brush-teeth-icon',
          theme_color: '#FF6B6B',
          description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        },
        {
          category_id: '123e4567-e89b-12d3-a456-************',
          name: '饭前洗手',
          icon: 'wash-hands-icon',
          theme_color: '#4ECDC4',
          description: '小手洗干净，病菌不来烦',
        },
      ];

      const mockCreatedTemplates = templates.map((template, index) => ({
        ...mockTemplate,
        id: `123e4567-e89b-12d3-a456-42661417400${index}`,
        ...template,
        is_active: true,
      }));

      supabaseService.query.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockResolvedValue({
          data: mockCreatedTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.createBatch(templates);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('睡前刷牙');
      expect(result[1].name).toBe('饭前洗手');
    });

    it('should return empty array when no templates provided', async () => {
      const result = await repository.createBatch([]);
      expect(result).toEqual([]);
    });

    it('should throw error when template validation fails', async () => {
      const invalidTemplates = [
        {
          name: '睡前刷牙',
          // missing required fields
        } as ICreateHabitTemplate,
      ];

      await expect(repository.createBatch(invalidTemplates)).rejects.toThrow();
    });
  });

  describe('deleteByCategory', () => {
    it('should delete all templates in a category', async () => {
      const categoryId = '123e4567-e89b-12d3-a456-************';

      supabaseService.query.mockReturnValue({
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          error: null,
        }),
      } as any);

      const result = await repository.deleteByCategory(categoryId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(result).toBe(true);
    });

    it('should return false for invalid UUID', async () => {
      const result = await repository.deleteByCategory('invalid-uuid');
      expect(result).toBe(false);
    });
  });

  describe('searchTemplates', () => {
    it('should search templates by name and description', async () => {
      const searchTerm = '刷牙';
      const mockTemplates = [mockTemplate];

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.searchTemplates(searchTerm);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_templates');
      expect(mockQueryBuilder.or).toHaveBeenCalledWith(
        expect.stringContaining('name.ilike.%刷牙%'),
      );
      expect(result).toEqual(mockTemplates);
    });

    it('should search within specific category', async () => {
      const searchTerm = '刷牙';
      const categoryId = '123e4567-e89b-12d3-a456-************';
      const mockTemplates = [mockTemplate];

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.searchTemplates(searchTerm, categoryId);

      expect(mockQueryBuilder.eq).toHaveBeenCalledWith(
        'category_id',
        categoryId,
      );
      expect(result).toEqual(mockTemplates);
    });

    it('should return all active templates when search term is empty', async () => {
      const mockTemplates = [mockTemplate];

      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
        }),
      } as any);

      const result = await repository.searchTemplates('');

      expect(result).toEqual(mockTemplates);
    });
  });
});
