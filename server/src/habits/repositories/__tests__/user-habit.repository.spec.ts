import { Test, TestingModule } from '@nestjs/testing';
import { UserHabitRepository } from '../user-habit.repository';
import { SupabaseService } from '../../../database/supabase.service';
import { IUserHabit, ICreateUserHabit, UserHabitStatus } from '../../interfaces/user-habit.interface';

describe('UserHabitRepository', () => {
  let repository: UserHabitRepository;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockUserHabit: IUserHabit = {
    id: '123e4567-e89b-12d3-a456-************',
    user_id: '123e4567-e89b-12d3-a456-************',
    baby_id: '123e4567-e89b-12d3-a456-************',
    template_id: '123e4567-e89b-12d3-a456-426614174003',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
    frequency: [1, 2, 3, 4, 5, 6, 7], // 每天
    preferred_time: '20:00',
    reward_stars: 3,
    status: UserHabitStatus.ACTIVE,
    created_at: new Date('2024-01-01T00:00:00Z'),
    updated_at: new Date('2024-01-01T00:00:00Z'),
  };

  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      handleDatabaseError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserHabitRepository,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    repository = module.get<UserHabitRepository>(UserHabitRepository);
    supabaseService = module.get(SupabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createUserHabit', () => {
    it('should create a user habit with default values', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData: ICreateUserHabit = {
        baby_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        frequency: [1, 2, 3, 4, 5, 6, 7],
        preferred_time: '20:00',
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: mockUserHabit,
        error: null,
      });

      const result = await repository.createUserHabit(userId, createData);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith({
        user_id: userId,
        ...createData,
        reward_stars: 1,
        status: UserHabitStatus.ACTIVE,
      });
      expect(result).toEqual(mockUserHabit);
    });
  });
}); 
 describe('findByUser', () => {
    it('should return user habits ordered by created_at desc', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const mockHabits = [mockUserHabit];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockHabits,
          error: null,
        }),
      } as any);

      const result = await repository.findByUser(userId);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(result).toEqual(mockHabits);
    });

    it('should filter by status when provided', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const status = UserHabitStatus.ACTIVE;
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: [mockUserHabit],
          error: null,
        }),
      } as any);

      const result = await repository.findByUser(userId, status);

      expect(result).toEqual([mockUserHabit]);
    });

    it('should return empty array for invalid UUID', async () => {
      const result = await repository.findByUser('invalid-uuid');
      expect(result).toEqual([]);
    });
  });

  describe('findByUserAndBaby', () => {
    it('should return habits for specific user and baby', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const mockHabits = [mockUserHabit];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockHabits,
          error: null,
        }),
      } as any);

      const result = await repository.findByUserAndBaby(userId, babyId);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(result).toEqual(mockHabits);
    });

    it('should return empty array for invalid UUIDs', async () => {
      const result = await repository.findByUserAndBaby('invalid-user', 'invalid-baby');
      expect(result).toEqual([]);
    });
  });

  describe('findTodayHabits', () => {
    it('should return habits scheduled for today', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const mockHabits = [mockUserHabit];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        contains: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockHabits,
          error: null,
        }),
      } as any);

      const result = await repository.findTodayHabits(userId);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(result).toEqual(mockHabits);
    });

    it('should return empty array for invalid UUID', async () => {
      const result = await repository.findTodayHabits('invalid-uuid');
      expect(result).toEqual([]);
    });
  });

  describe('checkUserAccess', () => {
    it('should return true when user has access to habit', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 1,
          error: null,
        }),
      } as any);

      const result = await repository.checkUserAccess(habitId, userId);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(result).toBe(true);
    });

    it('should return false when user has no access', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 0,
          error: null,
        }),
      } as any);

      const result = await repository.checkUserAccess(habitId, userId);

      expect(result).toBe(false);
    });

    it('should return false for invalid UUIDs', async () => {
      const result = await repository.checkUserAccess('invalid-habit', 'invalid-user');
      expect(result).toBe(false);
    });
  });

  describe('findByName', () => {
    it('should find habit by name for specific user and baby', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const name = '睡前刷牙';

      mockQueryBuilder.single.mockResolvedValue({
        data: mockUserHabit,
        error: null,
      });

      const result = await repository.findByName(userId, babyId, name);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', userId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('baby_id', babyId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('name', name);
      expect(result).toEqual(mockUserHabit);
    });

    it('should return null when habit not found', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';

      mockQueryBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await repository.findByName(userId, babyId, '不存在的习惯');

      expect(result).toBeNull();
    });

    it('should return null for invalid parameters', async () => {
      const result = await repository.findByName('invalid-user', 'invalid-baby', '');
      expect(result).toBeNull();
    });
  });

  describe('countUserHabits', () => {
    it('should return count of user habits', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 5,
          error: null,
        }),
      } as any);

      const result = await repository.countUserHabits(userId);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(result).toBe(5);
    });

    it('should return 0 for invalid UUID', async () => {
      const result = await repository.countUserHabits('invalid-uuid');
      expect(result).toBe(0);
    });
  });

  describe('archiveHabit', () => {
    it('should archive habit when user has access', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';

      // Mock checkUserAccess to return true
      supabaseService.query.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 1,
          error: null,
        }),
      } as any);

      // Mock update operation
      mockQueryBuilder.single.mockResolvedValue({
        data: { ...mockUserHabit, status: UserHabitStatus.ARCHIVED },
        error: null,
      });

      const result = await repository.archiveHabit(habitId, userId);

      expect(result.status).toBe(UserHabitStatus.ARCHIVED);
    });
  });

  describe('searchUserHabits', () => {
    it('should search habits by name and description', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const searchTerm = '刷牙';
      const mockHabits = [mockUserHabit];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockHabits,
          error: null,
        }),
      } as any);

      const result = await repository.searchUserHabits(userId, searchTerm);

      expect(supabaseService.query).toHaveBeenCalledWith('user_habits');
      expect(result).toEqual(mockHabits);
    });

    it('should return all habits when search term is empty', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const mockHabits = [mockUserHabit];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockHabits,
          error: null,
        }),
      } as any);

      const result = await repository.searchUserHabits(userId, '');

      expect(result).toEqual(mockHabits);
    });

    it('should return empty array for invalid UUID', async () => {
      const result = await repository.searchUserHabits('invalid-uuid', 'search');
      expect(result).toEqual([]);
    });
  });

  describe('validation', () => {
    it('should throw error when required fields are missing', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData = {
        name: '睡前刷牙',
      } as ICreateUserHabit;

      await expect(repository.createUserHabit(userId, createData)).rejects.toThrow();
    });

    it('should throw error when frequency array is empty', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData: ICreateUserHabit = {
        baby_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        frequency: [],
      };

      await expect(repository.createUserHabit(userId, createData)).rejects.toThrow();
    });

    it('should throw error when theme_color is invalid', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData: ICreateUserHabit = {
        baby_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: 'invalid-color',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        frequency: [1, 2, 3, 4, 5, 6, 7],
      };

      await expect(repository.createUserHabit(userId, createData)).rejects.toThrow();
    });

    it('should throw error when frequency values are invalid', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData: ICreateUserHabit = {
        baby_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        frequency: [0, 8, 9], // Invalid values
      };

      await expect(repository.createUserHabit(userId, createData)).rejects.toThrow();
    });

    it('should throw error when preferred_time format is invalid', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData: ICreateUserHabit = {
        baby_id: '123e4567-e89b-12d3-a456-************',
        name: '睡前刷牙',
        icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        frequency: [1, 2, 3, 4, 5, 6, 7],
        preferred_time: '25:70', // Invalid time format
      };

      await expect(repository.createUserHabit(userId, createData)).rejects.toThrow();
    });
  });
});