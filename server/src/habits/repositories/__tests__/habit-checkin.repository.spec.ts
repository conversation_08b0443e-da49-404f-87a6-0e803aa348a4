import { Test, TestingModule } from '@nestjs/testing';
import { HabitCheckInRepository } from '../habit-checkin.repository';
import { SupabaseService } from '../../../database/supabase.service';
import { IHabitCheckIn, ICreateHabitCheckIn } from '../../interfaces/habit-check-in.interface';

describe('HabitCheckInRepository', () => {
  let repository: HabitCheckInRepository;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockCheckIn: IHabitCheckIn = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    habit_id: '123e4567-e89b-12d3-a456-************',
    user_id: '123e4567-e89b-12d3-a456-************',
    baby_id: '123e4567-e89b-12d3-a456-************',
    check_in_date: new Date('2024-01-01T00:00:00Z'),
    check_in_time: new Date('2024-01-01T20:00:00Z'),
    notes: '今天刷牙很认真',
    created_at: new Date('2024-01-01T20:00:00Z'),
  };

  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      handleDatabaseError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitCheckInRepository,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    repository = module.get<HabitCheckInRepository>(HabitCheckInRepository);
    supabaseService = module.get(SupabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createCheckIn', () => {
    it('should create a check-in record with current time', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData: ICreateHabitCheckIn = {
        habit_id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-************',
        notes: '今天刷牙很认真',
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: mockCheckIn,
        error: null,
      });

      const result = await repository.createCheckIn(userId, createData);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: userId,
          habit_id: createData.habit_id,
          baby_id: createData.baby_id,
          notes: createData.notes,
        })
      );
      expect(result).toEqual(mockCheckIn);
    });

    it('should create a check-in record with specified date', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const checkInDate = new Date('2024-01-02T00:00:00Z');
      const createData: ICreateHabitCheckIn = {
        habit_id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-************',
        check_in_date: checkInDate,
      };

      mockQueryBuilder.single.mockResolvedValue({
        data: { ...mockCheckIn, check_in_date: checkInDate },
        error: null,
      });

      const result = await repository.createCheckIn(userId, createData);

      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          check_in_date: checkInDate,
        })
      );
      expect(result.check_in_date).toEqual(checkInDate);
    });
  });

  describe('hasCheckedInToday', () => {
    it('should return true when user has checked in today', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lt: jest.fn().mockResolvedValue({
          count: 1,
          error: null,
        }),
      } as any);

      const result = await repository.hasCheckedInToday(habitId, userId, babyId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toBe(true);
    });

    it('should return false when user has not checked in today', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lt: jest.fn().mockResolvedValue({
          count: 0,
          error: null,
        }),
      } as any);

      const result = await repository.hasCheckedInToday(habitId, userId, babyId);

      expect(result).toBe(false);
    });

    it('should return false for invalid UUIDs', async () => {
      const result = await repository.hasCheckedInToday('invalid', 'invalid', 'invalid');
      expect(result).toBe(false);
    });
  });
});  descri
be('findByDate', () => {
    it('should find check-in record for specific date', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const date = new Date('2024-01-01');

      mockQueryBuilder.single.mockResolvedValue({
        data: mockCheckIn,
        error: null,
      });

      const result = await repository.findByDate(habitId, userId, babyId, date);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('habit_id', habitId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', userId);
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('baby_id', babyId);
      expect(result).toEqual(mockCheckIn);
    });

    it('should return null when no check-in found for date', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const date = new Date('2024-01-02');

      mockQueryBuilder.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await repository.findByDate(habitId, userId, babyId, date);

      expect(result).toBeNull();
    });

    it('should return null for invalid UUIDs', async () => {
      const date = new Date('2024-01-01');
      const result = await repository.findByDate('invalid', 'invalid', 'invalid', date);
      expect(result).toBeNull();
    });
  });

  describe('findByHabit', () => {
    it('should return check-ins for a specific habit', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const mockCheckIns = [mockCheckIn];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockCheckIns,
          error: null,
        }),
      } as any);

      const result = await repository.findByHabit(habitId, userId, babyId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toEqual(mockCheckIns);
    });

    it('should return empty array for invalid UUIDs', async () => {
      const result = await repository.findByHabit('invalid', 'invalid', 'invalid');
      expect(result).toEqual([]);
    });
  });

  describe('findByDateRange', () => {
    it('should return check-ins within date range', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const mockCheckIns = [mockCheckIn];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockCheckIns,
          error: null,
        }),
      } as any);

      const result = await repository.findByDateRange(userId, babyId, startDate, endDate);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toEqual(mockCheckIns);
    });

    it('should filter by habit when habitId provided', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const habitId = '123e4567-e89b-12d3-a456-************';
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const mockCheckIns = [mockCheckIn];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockCheckIns,
          error: null,
        }),
      } as any);

      const result = await repository.findByDateRange(userId, babyId, startDate, endDate, habitId);

      expect(result).toEqual(mockCheckIns);
    });

    it('should return empty array for invalid UUIDs', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const result = await repository.findByDateRange('invalid', 'invalid', startDate, endDate);
      expect(result).toEqual([]);
    });
  });

  describe('countCheckIns', () => {
    it('should return count of check-ins for habit', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          count: 10,
          error: null,
        }),
      } as any);

      const result = await repository.countCheckIns(habitId, userId, babyId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toBe(10);
    });

    it('should filter by date range when provided', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockResolvedValue({
          count: 5,
          error: null,
        }),
      } as any);

      const result = await repository.countCheckIns(habitId, userId, babyId, startDate, endDate);

      expect(result).toBe(5);
    });

    it('should return 0 for invalid UUIDs', async () => {
      const result = await repository.countCheckIns('invalid', 'invalid', 'invalid');
      expect(result).toBe(0);
    });
  });

  describe('getStreakDays', () => {
    it('should calculate streak days correctly', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      const mockCheckInDates = [
        { check_in_date: today.toISOString() },
        { check_in_date: yesterday.toISOString() },
      ];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockCheckInDates,
          error: null,
        }),
      } as any);

      const result = await repository.getStreakDays(habitId, userId, babyId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toBeGreaterThanOrEqual(0);
    });

    it('should return 0 when no check-ins found', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: [],
          error: null,
        }),
      } as any);

      const result = await repository.getStreakDays(habitId, userId, babyId);

      expect(result).toBe(0);
    });

    it('should return 0 for invalid UUIDs', async () => {
      const result = await repository.getStreakDays('invalid', 'invalid', 'invalid');
      expect(result).toBe(0);
    });
  });

  describe('getMonthlyStats', () => {
    it('should return monthly statistics', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const babyId = '123e4567-e89b-12d3-a456-************';
      const year = 2024;
      const month = 1;
      
      const mockCheckInDates = [
        { check_in_date: '2024-01-01T00:00:00Z' },
        { check_in_date: '2024-01-01T00:00:00Z' },
        { check_in_date: '2024-01-02T00:00:00Z' },
      ];
      
      supabaseService.query.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockResolvedValue({
          data: mockCheckInDates,
          error: null,
        }),
      } as any);

      const result = await repository.getMonthlyStats(userId, babyId, year, month);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toEqual([
        { date: '2024-01-01', count: 2 },
        { date: '2024-01-02', count: 1 },
      ]);
    });

    it('should return empty array for invalid UUIDs', async () => {
      const result = await repository.getMonthlyStats('invalid', 'invalid', 2024, 1);
      expect(result).toEqual([]);
    });
  });

  describe('deleteByHabit', () => {
    it('should delete all check-ins for a habit', async () => {
      const habitId = '123e4567-e89b-12d3-a456-************';
      
      supabaseService.query.mockReturnValue({
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          error: null,
        }),
      } as any);

      const result = await repository.deleteByHabit(habitId);

      expect(supabaseService.query).toHaveBeenCalledWith('habit_check_ins');
      expect(result).toBe(true);
    });

    it('should return false for invalid UUID', async () => {
      const result = await repository.deleteByHabit('invalid-uuid');
      expect(result).toBe(false);
    });
  });

  describe('validation', () => {
    it('should throw error when required fields are missing', async () => {
      const userId = '123e4567-e89b-12d3-a456-************';
      const createData = {
        baby_id: '123e4567-e89b-12d3-a456-************',
      } as ICreateHabitCheckIn;

      await expect(repository.createCheckIn(userId, createData)).rejects.toThrow();
    });
  });
});