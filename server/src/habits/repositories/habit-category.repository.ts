import { Injectable } from '@nestjs/common';
import { BaseRepository, QueryOptions } from '../../database/base.repository';
import { SupabaseService } from '../../database/supabase.service';
import { DatabaseUtils } from '../../database/database.utils';
import {
  IHabitCategory,
  ICreateHabitCategory,
  IUpdateHabitCategory,
  IHabitCategoryQueryOptions,
  IHabitCategoryTree,
} from '../interfaces/habit-category.interface';

@Injectable()
export class HabitCategoryRepository extends BaseRepository<IHabitCategory> {
  protected readonly tableName = 'habit_categories';
  protected readonly entityName = 'habit category';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 创建习惯分类
   */
  async createCategory(data: ICreateHabitCategory): Promise<IHabitCategory> {
    return this.create({
      ...data,
      sort_order: data.sort_order ?? 0,
      is_active: data.is_active ?? true,
    });
  }

  /**
   * 更新习惯分类
   */
  async updateCategory(
    id: string,
    data: IUpdateHabitCategory,
  ): Promise<IHabitCategory> {
    return this.update(id, data);
  }

  /**
   * 获取所有活跃的分类，按排序顺序
   */
  async findActiveCategories(): Promise<IHabitCategory[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_ACTIVE_CATEGORIES',
        this.tableName,
        {},
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('is_active', true)
        .order('level', { ascending: true })
        .order('sort_order', { ascending: true })
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find active habit categories',
      );

      return (result.data as IHabitCategory[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find active habit categories',
        this.entityName,
      );
    }
  }

  /**
   * 获取所有分类，按排序顺序（包括非活跃的）
   */
  async findAllCategories(): Promise<IHabitCategory[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_ALL_CATEGORIES',
        this.tableName,
        {},
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .order('sort_order', { ascending: true })
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find all habit categories',
      );

      return (result.data as IHabitCategory[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find all habit categories',
        this.entityName,
      );
    }
  }

  /**
   * 根据查询选项查找分类
   */
  async findCategories(
    options: IHabitCategoryQueryOptions = {},
  ): Promise<IHabitCategory[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_CATEGORIES',
        this.tableName,
        options,
      );

      let query = this.supabaseService.query(this.tableName).select('*');

      // 应用过滤条件
      if (options.level !== undefined) {
        query = query.eq('level', options.level);
      }
      if (options.parent_id !== undefined) {
        if (options.parent_id === null) {
          query = query.is('parent_id', null);
        } else {
          query = query.eq('parent_id', options.parent_id);
        }
      }
      if (options.is_active !== undefined) {
        query = query.eq('is_active', options.is_active);
      }

      // 排序
      query = query
        .order('level', { ascending: true })
        .order('sort_order', { ascending: true })
        .order('created_at', { ascending: true });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find habit categories',
      );

      return (result.data as IHabitCategory[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit categories',
        this.entityName,
      );
    }
  }

  /**
   * 获取分类树形结构
   */
  async findCategoryTree(
    options: IHabitCategoryQueryOptions = {},
  ): Promise<IHabitCategoryTree[]> {
    try {
      // 获取所有分类
      const categories = await this.findCategories(options);

      // 构建树形结构
      const categoryMap = new Map<string, IHabitCategoryTree>();
      const rootCategories: IHabitCategoryTree[] = [];

      // 初始化所有分类
      categories.forEach((category) => {
        categoryMap.set(category.id, {
          ...category,
          children: [],
        });
      });

      // 构建父子关系
      categories.forEach((category) => {
        const treeNode = categoryMap.get(category.id)!;

        if (category.parent_id && categoryMap.has(category.parent_id)) {
          // 添加到父分类的children中
          const parent = categoryMap.get(category.parent_id)!;
          parent.children.push(treeNode);
        } else {
          // 一级分类
          rootCategories.push(treeNode);
        }
      });

      return rootCategories;
    } catch (error) {
      DatabaseUtils.handleError(error, 'find category tree', this.entityName);
      return []; // 这行代码永远不会执行，因为 handleError 总是抛出异常
    }
  }

  /**
   * 获取子分类列表
   */
  async findChildren(parentId: string): Promise<IHabitCategory[]> {
    return this.findCategories({ parent_id: parentId });
  }

  /**
   * 获取一级分类列表
   */
  async findTopLevelCategories(isActive?: boolean): Promise<IHabitCategory[]> {
    return this.findCategories({
      level: 1,
      is_active: isActive,
    });
  }

  /**
   * 根据名称查找分类（用于检查重复）
   */
  async findByName(
    name: string,
    parentId?: string,
  ): Promise<IHabitCategory | null> {
    try {
      if (!name?.trim()) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_NAME', this.tableName, {
        name,
        parentId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('name', name.trim());

      // 在同一层级下检查重复名称
      if (parentId !== undefined) {
        if (parentId === null) {
          query = query.is('parent_id', null);
        } else {
          query = query.eq('parent_id', parentId);
        }
      }

      const result = await query.single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find habit category by name',
        );
      }

      return result.data as IHabitCategory | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit category by name',
        this.entityName,
      );
    }
  }

  /**
   * 检查分类下是否有习惯模板
   */
  async hasTemplates(categoryId: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(categoryId)) {
        return false;
      }

      DatabaseUtils.logDatabaseOperation(
        'CHECK_HAS_TEMPLATES',
        this.tableName,
        { categoryId },
      );

      const result = await this.supabaseService
        .query('habit_templates')
        .select('id', { count: 'exact', head: true })
        .eq('category_id', categoryId);

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn('Failed to check if category has templates', error);
      return false;
    }
  }

  /**
   * 检查分类下是否有子分类
   */
  async hasChildren(categoryId: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(categoryId)) {
        return false;
      }

      DatabaseUtils.logDatabaseOperation('CHECK_HAS_CHILDREN', this.tableName, {
        categoryId,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('id', { count: 'exact', head: true })
        .eq('parent_id', categoryId);

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn('Failed to check if category has children', error);
      return false;
    }
  }

  /**
   * 验证父分类是否存在且为一级分类
   */
  async validateParentCategory(parentId: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(parentId)) {
        return false;
      }

      const parent = await this.findById(parentId);
      return parent !== null && parent.level === 1;
    } catch (error) {
      this.logger.warn('Failed to validate parent category', error);
      return false;
    }
  }

  /**
   * 获取下一个排序顺序（在指定层级和父分类下）
   */
  async getNextSortOrder(level: number, parentId?: string): Promise<number> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'GET_NEXT_SORT_ORDER',
        this.tableName,
        { level, parentId },
      );

      let query = this.supabaseService
        .query(this.tableName)
        .select('sort_order')
        .eq('level', level);

      if (parentId) {
        query = query.eq('parent_id', parentId);
      } else {
        query = query.is('parent_id', null);
      }

      const result = await query
        .order('sort_order', { ascending: false })
        .limit(1)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'get next sort order',
        );
      }

      const maxSortOrder = result.data?.sort_order || 0;
      return maxSortOrder + 1;
    } catch (error) {
      this.logger.warn('Failed to get next sort order, using default', error);
      return 1;
    }
  }

  /**
   * 重新排序分类
   */
  async reorderCategories(
    categoryOrders: { id: string; sort_order: number }[],
  ): Promise<void> {
    try {
      DatabaseUtils.logDatabaseOperation('REORDER_CATEGORIES', this.tableName, {
        categoryOrders,
      });

      // 使用事务更新所有分类的排序
      for (const { id, sort_order } of categoryOrders) {
        if (DatabaseUtils.isValidUUID(id)) {
          await this.update(id, { sort_order } as Partial<IHabitCategory>);
        }
      }

      this.logger.log('Categories reordered successfully');
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'reorder habit categories',
        this.entityName,
      );
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IHabitCategory>): void {
    super.validateCreateData(data);

    if (!data.name?.trim()) {
      DatabaseUtils.handleError(
        new Error('Category name is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.icon?.trim()) {
      DatabaseUtils.handleError(
        new Error('Category icon is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (data.level === undefined || (data.level !== 1 && data.level !== 2)) {
      DatabaseUtils.handleError(
        new Error('Category level must be 1 or 2'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (data.level === 1 && data.parent_id) {
      DatabaseUtils.handleError(
        new Error('Top level category cannot have parent'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (data.level === 2 && !data.parent_id) {
      DatabaseUtils.handleError(
        new Error('Sub category must have parent'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }
  }
}
