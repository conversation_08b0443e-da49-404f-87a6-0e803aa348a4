import { Injectable } from '@nestjs/common';
import { BaseRepository, QueryOptions } from '../../database/base.repository';
import { SupabaseService } from '../../database/supabase.service';
import { DatabaseUtils } from '../../database/database.utils';
import {
  IUserHabit,
  ICreateUserHabit,
  IUpdateUserHabit,
  UserHabitStatus,
} from '../interfaces/user-habit.interface';

@Injectable()
export class UserHabitRepository extends BaseRepository<IUserHabit> {
  protected readonly tableName = 'user_habits';
  protected readonly entityName = 'user habit';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 创建用户习惯
   */
  async createUserHabit(
    userId: string,
    data: ICreateUserHabit,
  ): Promise<IUserHabit> {
    return this.create({
      user_id: userId,
      ...data,
      reward_stars: data.reward_stars ?? 1,
      status: data.status ?? UserHabitStatus.ACTIVE,
    });
  }

  /**
   * 更新用户习惯
   */
  async updateUserHabit(
    id: string,
    data: IUpdateUserHabit,
  ): Promise<IUserHabit> {
    return this.update(id, data);
  }

  /**
   * 根据用户ID获取习惯列表
   */
  async findByUser(
    userId: string,
    status?: UserHabitStatus,
  ): Promise<IUserHabit[]> {
    try {
      if (!DatabaseUtils.isValidUUID(userId)) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_USER', this.tableName, {
        userId,
        status,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId);

      if (status) {
        query = query.eq('status', status);
      }

      query = query.order('created_at', { ascending: false });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find user habits by user',
      );

      return (result.data as IUserHabit[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find user habits by user',
        this.entityName,
      );
    }
  }

  /**
   * 根据用户和宝宝ID获取习惯列表
   */
  async findByUserAndBaby(
    userId: string,
    babyId: string,
    status?: UserHabitStatus,
  ): Promise<IUserHabit[]> {
    try {
      if (
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation(
        'FIND_BY_USER_AND_BABY',
        this.tableName,
        {
          userId,
          babyId,
          status,
        },
      );

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId)
        .eq('baby_id', babyId);

      if (status) {
        query = query.eq('status', status);
      }

      query = query.order('created_at', { ascending: false });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find user habits by user and baby',
      );

      return (result.data as IUserHabit[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find user habits by user and baby',
        this.entityName,
      );
    }
  }

  /**
   * 获取用户的活跃习惯
   */
  async findActiveHabits(
    userId: string,
    babyId?: string,
  ): Promise<IUserHabit[]> {
    const status = UserHabitStatus.ACTIVE;

    if (babyId) {
      return this.findByUserAndBaby(userId, babyId, status);
    }

    return this.findByUser(userId, status);
  }

  /**
   * 根据模板ID查找用户习惯
   */
  async findByTemplate(templateId: string): Promise<IUserHabit[]> {
    try {
      if (!DatabaseUtils.isValidUUID(templateId)) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_TEMPLATE', this.tableName, {
        templateId,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('template_id', templateId)
        .order('created_at', { ascending: false });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find user habits by template',
      );

      return (result.data as IUserHabit[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find user habits by template',
        this.entityName,
      );
    }
  }

  /**
   * 检查用户是否有权限访问习惯
   */
  async checkUserAccess(habitId: string, userId: string): Promise<boolean> {
    try {
      if (
        !DatabaseUtils.isValidUUID(habitId) ||
        !DatabaseUtils.isValidUUID(userId)
      ) {
        return false;
      }

      DatabaseUtils.logDatabaseOperation('CHECK_USER_ACCESS', this.tableName, {
        habitId,
        userId,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('id', { count: 'exact', head: true })
        .eq('id', habitId)
        .eq('user_id', userId);

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn('Failed to check user access to habit', error);
      return false;
    }
  }

  /**
   * 根据名称查找用户习惯（用于检查重复）
   */
  async findByName(
    userId: string,
    babyId: string,
    name: string,
  ): Promise<IUserHabit | null> {
    try {
      if (
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId) ||
        !name?.trim()
      ) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_NAME', this.tableName, {
        userId,
        babyId,
        name,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId)
        .eq('baby_id', babyId)
        .eq('name', name.trim())
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find user habit by name',
        );
      }

      return result.data as IUserHabit | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find user habit by name',
        this.entityName,
      );
    }
  }

  /**
   * 获取今日应该执行的习惯
   */
  async findTodayHabits(
    userId: string,
    babyId?: string,
  ): Promise<IUserHabit[]> {
    try {
      if (!DatabaseUtils.isValidUUID(userId)) {
        return [];
      }

      // 获取今天是周几 (1-7, 周一到周日)
      const today = new Date();
      const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay(); // 转换为1-7格式

      DatabaseUtils.logDatabaseOperation('FIND_TODAY_HABITS', this.tableName, {
        userId,
        babyId,
        dayOfWeek,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId)
        .eq('status', UserHabitStatus.ACTIVE)
        .contains('frequency', [dayOfWeek]); // 检查frequency数组是否包含今天

      if (babyId && DatabaseUtils.isValidUUID(babyId)) {
        query = query.eq('baby_id', babyId);
      }

      query = query
        .order('preferred_time', { ascending: true, nullsFirst: false })
        .order('created_at', { ascending: true });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find today habits',
      );

      return (result.data as IUserHabit[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(error, 'find today habits', this.entityName);
    }
  }

  /**
   * 统计用户习惯数量
   */
  async countUserHabits(
    userId: string,
    babyId?: string,
    status?: UserHabitStatus,
  ): Promise<number> {
    try {
      if (!DatabaseUtils.isValidUUID(userId)) {
        return 0;
      }

      DatabaseUtils.logDatabaseOperation('COUNT_USER_HABITS', this.tableName, {
        userId,
        babyId,
        status,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (babyId && DatabaseUtils.isValidUUID(babyId)) {
        query = query.eq('baby_id', babyId);
      }

      if (status) {
        query = query.eq('status', status);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'count user habits',
      );

      return result.count || 0;
    } catch (error) {
      this.logger.warn('Failed to count user habits', error);
      return 0;
    }
  }

  /**
   * 归档习惯
   */
  async archiveHabit(habitId: string, userId: string): Promise<IUserHabit> {
    try {
      // 先检查用户权限
      const hasAccess = await this.checkUserAccess(habitId, userId);
      if (!hasAccess) {
        DatabaseUtils.handleError(
          new Error('User does not have access to this habit'),
          'archive user habit',
          this.entityName,
        );
      }

      return this.update(habitId, {
        status: UserHabitStatus.ARCHIVED,
      } as Partial<IUserHabit>);
    } catch (error) {
      DatabaseUtils.handleError(error, 'archive user habit', this.entityName);
    }
  }

  /**
   * 激活习惯
   */
  async activateHabit(habitId: string, userId: string): Promise<IUserHabit> {
    try {
      // 先检查用户权限
      const hasAccess = await this.checkUserAccess(habitId, userId);
      if (!hasAccess) {
        DatabaseUtils.handleError(
          new Error('User does not have access to this habit'),
          'activate user habit',
          this.entityName,
        );
      }

      return this.update(habitId, {
        status: UserHabitStatus.ACTIVE,
      } as Partial<IUserHabit>);
    } catch (error) {
      DatabaseUtils.handleError(error, 'activate user habit', this.entityName);
    }
  }

  /**
   * 搜索用户习惯
   */
  async searchUserHabits(
    userId: string,
    searchTerm: string,
    babyId?: string,
    options: QueryOptions = {},
  ): Promise<IUserHabit[]> {
    try {
      if (!DatabaseUtils.isValidUUID(userId)) {
        return [];
      }

      if (!searchTerm?.trim()) {
        return babyId
          ? await this.findByUserAndBaby(userId, babyId)
          : await this.findByUser(userId);
      }

      DatabaseUtils.logDatabaseOperation('SEARCH_USER_HABITS', this.tableName, {
        userId,
        babyId,
        searchTerm,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId);

      if (babyId && DatabaseUtils.isValidUUID(babyId)) {
        query = query.eq('baby_id', babyId);
      }

      // 使用 ilike 进行模糊搜索（名称和描述）
      query = query.or(
        `name.ilike.%${searchTerm.trim()}%,description.ilike.%${searchTerm.trim()}%`,
      );

      // 应用排序
      const { sortBy = 'created_at', sortOrder = 'desc' } = options;
      const { column, ascending } = DatabaseUtils.handleSorting(
        sortBy,
        sortOrder,
      );
      query = query.order(column, { ascending });

      // 应用分页
      if (options.page && options.limit) {
        const { from, to } = DatabaseUtils.handlePagination(
          options.page,
          options.limit,
        );
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'search user habits',
      );

      return (result.data as IUserHabit[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(error, 'search user habits', this.entityName);
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IUserHabit>): void {
    super.validateCreateData(data);

    if (!data.user_id?.trim() || !DatabaseUtils.isValidUUID(data.user_id)) {
      DatabaseUtils.handleError(
        new Error('Valid user ID is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.baby_id?.trim() || !DatabaseUtils.isValidUUID(data.baby_id)) {
      DatabaseUtils.handleError(
        new Error('Valid baby ID is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.name?.trim()) {
      DatabaseUtils.handleError(
        new Error('Habit name is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.icon?.trim()) {
      DatabaseUtils.handleError(
        new Error('Habit icon is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.theme_color?.trim()) {
      DatabaseUtils.handleError(
        new Error('Habit theme color is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证主题色格式（HEX颜色）
    if (data.theme_color && !/^#[0-9A-Fa-f]{6}$/.test(data.theme_color)) {
      DatabaseUtils.handleError(
        new Error('Theme color must be a valid HEX color (e.g., #FF0000)'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证频率数组
    if (!Array.isArray(data.frequency) || data.frequency.length === 0) {
      DatabaseUtils.handleError(
        new Error('Frequency array is required and cannot be empty'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证频率值（1-7）
    if (
      data.frequency &&
      !data.frequency.every(
        (day) => Number.isInteger(day) && day >= 1 && day <= 7,
      )
    ) {
      DatabaseUtils.handleError(
        new Error('Frequency values must be integers between 1 and 7'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证奖励星星数量
    if (
      data.reward_stars !== undefined &&
      (!Number.isInteger(data.reward_stars) ||
        data.reward_stars < 1 ||
        data.reward_stars > 5)
    ) {
      DatabaseUtils.handleError(
        new Error('Reward stars must be an integer between 1 and 5'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证首选时间格式
    if (
      data.preferred_time &&
      !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(data.preferred_time)
    ) {
      DatabaseUtils.handleError(
        new Error('Preferred time must be in HH:mm format'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证模板ID（如果提供）
    if (data.template_id && !DatabaseUtils.isValidUUID(data.template_id)) {
      DatabaseUtils.handleError(
        new Error('Template ID must be a valid UUID'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }
  }
}
