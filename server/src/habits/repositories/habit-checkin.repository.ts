import { Injectable } from '@nestjs/common';
import { BaseRepository, QueryOptions } from '../../database/base.repository';
import { SupabaseService } from '../../database/supabase.service';
import { DatabaseUtils } from '../../database/database.utils';
import {
  IHabitCheckIn,
  ICreateHabitCheckIn,
  IHabitCheckInQuery,
} from '../interfaces/habit-check-in.interface';

@Injectable()
export class HabitCheckInRepository extends BaseRepository<IHabitCheckIn> {
  protected readonly tableName = 'habit_check_ins';
  protected readonly entityName = 'habit check-in';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 创建打卡记录
   */
  async createCheckIn(
    userId: string,
    data: ICreateHabitCheckIn,
  ): Promise<IHabitCheckIn> {
    const now = new Date();
    const checkInDate =
      data.check_in_date ||
      new Date(now.getFullYear(), now.getMonth(), now.getDate());

    return this.create({
      user_id: userId,
      habit_id: data.habit_id,
      baby_id: data.baby_id,
      check_in_date: checkInDate,
      check_in_time: now,
      notes: data.notes,
    });
  }

  /**
   * 检查今日是否已打卡
   */
  async hasCheckedInToday(
    habitId: string,
    userId: string,
    babyId: string,
  ): Promise<boolean> {
    try {
      if (
        !DatabaseUtils.isValidUUID(habitId) ||
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return false;
      }

      const today = new Date();
      const todayStart = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const todayEnd = new Date(todayStart);
      todayEnd.setDate(todayEnd.getDate() + 1);

      DatabaseUtils.logDatabaseOperation(
        'CHECK_TODAY_CHECKIN',
        this.tableName,
        {
          habitId,
          userId,
          babyId,
          todayStart,
          todayEnd,
        },
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select('id', { count: 'exact', head: true })
        .eq('habit_id', habitId)
        .eq('user_id', userId)
        .eq('baby_id', babyId)
        .gte('check_in_date', todayStart.toISOString())
        .lt('check_in_date', todayEnd.toISOString());

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn('Failed to check today check-in status', error);
      return false;
    }
  }

  /**
   * 获取指定日期的打卡记录
   */
  async findByDate(
    habitId: string,
    userId: string,
    babyId: string,
    date: Date,
  ): Promise<IHabitCheckIn | null> {
    try {
      if (
        !DatabaseUtils.isValidUUID(habitId) ||
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return null;
      }

      const dateStart = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const dateEnd = new Date(dateStart);
      dateEnd.setDate(dateEnd.getDate() + 1);

      DatabaseUtils.logDatabaseOperation('FIND_BY_DATE', this.tableName, {
        habitId,
        userId,
        babyId,
        dateStart,
        dateEnd,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('habit_id', habitId)
        .eq('user_id', userId)
        .eq('baby_id', babyId)
        .gte('check_in_date', dateStart.toISOString())
        .lt('check_in_date', dateEnd.toISOString())
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find check-in by date',
        );
      }

      return result.data as IHabitCheckIn | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find check-in by date',
        this.entityName,
      );
    }
  }

  /**
   * 获取习惯的打卡记录列表
   */
  async findByHabit(
    habitId: string,
    userId: string,
    babyId: string,
    options: QueryOptions = {},
  ): Promise<IHabitCheckIn[]> {
    try {
      if (
        !DatabaseUtils.isValidUUID(habitId) ||
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_HABIT', this.tableName, {
        habitId,
        userId,
        babyId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('habit_id', habitId)
        .eq('user_id', userId)
        .eq('baby_id', babyId);

      // 应用排序
      const { sortBy = 'check_in_date', sortOrder = 'desc' } = options;
      const { column, ascending } = DatabaseUtils.handleSorting(
        sortBy,
        sortOrder,
      );
      query = query.order(column, { ascending });

      // 应用分页
      if (options.page && options.limit) {
        const { from, to } = DatabaseUtils.handlePagination(
          options.page,
          options.limit,
        );
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find check-ins by habit',
      );

      return (result.data as IHabitCheckIn[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find check-ins by habit',
        this.entityName,
      );
    }
  }

  /**
   * 获取日期范围内的打卡记录
   */
  async findByDateRange(
    userId: string,
    babyId: string,
    startDate: Date,
    endDate: Date,
    habitId?: string,
  ): Promise<IHabitCheckIn[]> {
    try {
      if (
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_DATE_RANGE', this.tableName, {
        userId,
        babyId,
        startDate,
        endDate,
        habitId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId)
        .eq('baby_id', babyId)
        .gte('check_in_date', startDate.toISOString())
        .lte('check_in_date', endDate.toISOString());

      if (habitId && DatabaseUtils.isValidUUID(habitId)) {
        query = query.eq('habit_id', habitId);
      }

      query = query
        .order('check_in_date', { ascending: false })
        .order('check_in_time', { ascending: false });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find check-ins by date range',
      );

      return (result.data as IHabitCheckIn[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find check-ins by date range',
        this.entityName,
      );
    }
  }

  /**
   * 获取用户的所有打卡记录
   */
  async findByUser(
    userId: string,
    babyId?: string,
    options: QueryOptions = {},
  ): Promise<IHabitCheckIn[]> {
    try {
      if (!DatabaseUtils.isValidUUID(userId)) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_USER', this.tableName, {
        userId,
        babyId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('user_id', userId);

      if (babyId && DatabaseUtils.isValidUUID(babyId)) {
        query = query.eq('baby_id', babyId);
      }

      // 应用排序
      const { sortBy = 'check_in_date', sortOrder = 'desc' } = options;
      const { column, ascending } = DatabaseUtils.handleSorting(
        sortBy,
        sortOrder,
      );
      query = query.order(column, { ascending });

      // 应用分页
      if (options.page && options.limit) {
        const { from, to } = DatabaseUtils.handlePagination(
          options.page,
          options.limit,
        );
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find check-ins by user',
      );

      return (result.data as IHabitCheckIn[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find check-ins by user',
        this.entityName,
      );
    }
  }

  /**
   * 统计习惯的打卡次数
   */
  async countCheckIns(
    habitId: string,
    userId: string,
    babyId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    try {
      if (
        !DatabaseUtils.isValidUUID(habitId) ||
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return 0;
      }

      DatabaseUtils.logDatabaseOperation('COUNT_CHECKINS', this.tableName, {
        habitId,
        userId,
        babyId,
        startDate,
        endDate,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('habit_id', habitId)
        .eq('user_id', userId)
        .eq('baby_id', babyId);

      if (startDate) {
        query = query.gte('check_in_date', startDate.toISOString());
      }

      if (endDate) {
        query = query.lte('check_in_date', endDate.toISOString());
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(result.error, 'count check-ins');

      return result.count || 0;
    } catch (error) {
      this.logger.warn('Failed to count check-ins', error);
      return 0;
    }
  }

  /**
   * 获取习惯的连续打卡天数
   */
  async getStreakDays(
    habitId: string,
    userId: string,
    babyId: string,
  ): Promise<number> {
    try {
      if (
        !DatabaseUtils.isValidUUID(habitId) ||
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return 0;
      }

      DatabaseUtils.logDatabaseOperation('GET_STREAK_DAYS', this.tableName, {
        habitId,
        userId,
        babyId,
      });

      // 获取最近30天的打卡记录
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await this.supabaseService
        .query(this.tableName)
        .select('check_in_date')
        .eq('habit_id', habitId)
        .eq('user_id', userId)
        .eq('baby_id', babyId)
        .gte('check_in_date', thirtyDaysAgo.toISOString())
        .order('check_in_date', { ascending: false });

      this.supabaseService.handleDatabaseError(result.error, 'get streak days');

      const checkIns = (result.data as { check_in_date: string }[]) || [];

      if (checkIns.length === 0) {
        return 0;
      }

      // 计算连续天数
      let streakDays = 0;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (let i = 0; i < checkIns.length; i++) {
        const checkInDate = new Date(checkIns[i].check_in_date);
        checkInDate.setHours(0, 0, 0, 0);

        const expectedDate = new Date(today);
        expectedDate.setDate(expectedDate.getDate() - streakDays);

        if (checkInDate.getTime() === expectedDate.getTime()) {
          streakDays++;
        } else {
          break;
        }
      }

      return streakDays;
    } catch (error) {
      this.logger.warn('Failed to calculate streak days', error);
      return 0;
    }
  }

  /**
   * 获取月度打卡统计
   */
  async getMonthlyStats(
    userId: string,
    babyId: string,
    year: number,
    month: number,
    habitId?: string,
  ): Promise<{ date: string; count: number }[]> {
    try {
      if (
        !DatabaseUtils.isValidUUID(userId) ||
        !DatabaseUtils.isValidUUID(babyId)
      ) {
        return [];
      }

      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);

      DatabaseUtils.logDatabaseOperation('GET_MONTHLY_STATS', this.tableName, {
        userId,
        babyId,
        year,
        month,
        habitId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('check_in_date')
        .eq('user_id', userId)
        .eq('baby_id', babyId)
        .gte('check_in_date', startDate.toISOString())
        .lte('check_in_date', endDate.toISOString());

      if (habitId && DatabaseUtils.isValidUUID(habitId)) {
        query = query.eq('habit_id', habitId);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'get monthly stats',
      );

      const checkIns = (result.data as { check_in_date: string }[]) || [];

      // 统计每日打卡次数
      const dailyStats: { [key: string]: number } = {};

      checkIns.forEach((checkIn) => {
        const date = new Date(checkIn.check_in_date)
          .toISOString()
          .split('T')[0];
        dailyStats[date] = (dailyStats[date] || 0) + 1;
      });

      // 转换为数组格式
      return Object.entries(dailyStats).map(([date, count]) => ({
        date,
        count,
      }));
    } catch (error) {
      DatabaseUtils.handleError(error, 'get monthly stats', this.entityName);
    }
  }

  /**
   * 删除习惯的所有打卡记录
   */
  async deleteByHabit(habitId: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(habitId)) {
        return false;
      }

      DatabaseUtils.logDatabaseOperation('DELETE_BY_HABIT', this.tableName, {
        habitId,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .delete()
        .eq('habit_id', habitId);

      this.supabaseService.handleDatabaseError(
        result.error,
        'delete check-ins by habit',
      );

      this.logger.log(`Check-ins deleted for habit ${habitId}`);
      return true;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'delete check-ins by habit',
        this.entityName,
      );
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IHabitCheckIn>): void {
    super.validateCreateData(data);

    if (!data.user_id?.trim() || !DatabaseUtils.isValidUUID(data.user_id)) {
      DatabaseUtils.handleError(
        new Error('Valid user ID is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.habit_id?.trim() || !DatabaseUtils.isValidUUID(data.habit_id)) {
      DatabaseUtils.handleError(
        new Error('Valid habit ID is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.baby_id?.trim() || !DatabaseUtils.isValidUUID(data.baby_id)) {
      DatabaseUtils.handleError(
        new Error('Valid baby ID is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.check_in_date) {
      DatabaseUtils.handleError(
        new Error('Check-in date is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.check_in_time) {
      DatabaseUtils.handleError(
        new Error('Check-in time is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }
  }
}
