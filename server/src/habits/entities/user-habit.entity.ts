import {
  <PERSON><PERSON>ser<PERSON><PERSON><PERSON>,
  ICreate<PERSON>ser<PERSON>abi<PERSON>,
  IUpdateUserHabit,
  UserHabitStatus,
} from '../interfaces';
import { HabitValidator } from '../validators';

export class UserHabit implements IUserHabit {
  id: string;
  user_id: string;
  baby_id: string;
  template_id?: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  reward_stars: number;
  status: UserHabitStatus;
  created_at: Date;
  updated_at: Date;

  constructor(partial: Partial<UserHabit>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新用户习惯实例
   */
  static create(userId: string, data: ICreateUserHabit): UserHabit {
    const validation = HabitValidator.validateUserHabitData({
      user_id: userId,
      baby_id: data.baby_id,
      template_id: data.template_id,
      name: data.name,
      icon: data.icon,
      theme_color: data.theme_color,
      description: data.description,
      frequency: data.frequency,
      preferred_time: data.preferred_time,
      reward_stars: data.reward_stars ?? 1,
      status: data.status ?? UserHabitStatus.ACTIVE,
    });

    if (!validation.isValid) {
      throw new Error(
        `Invalid user habit data: ${validation.errors.join(', ')}`,
      );
    }

    const now = new Date();
    return new UserHabit({
      id: '', // 将由数据库生成
      user_id: userId,
      baby_id: data.baby_id,
      template_id: data.template_id,
      name: data.name,
      icon: data.icon,
      theme_color: data.theme_color,
      description: data.description,
      frequency: data.frequency,
      preferred_time: data.preferred_time,
      reward_stars: data.reward_stars ?? 1,
      status: data.status ?? UserHabitStatus.ACTIVE,
      created_at: now,
      updated_at: now,
    });
  }

  /**
   * 更新用户习惯信息
   */
  update(data: IUpdateUserHabit): void {
    // 验证更新数据
    const updateData = {
      user_id: this.user_id,
      baby_id: this.baby_id,
      template_id: this.template_id,
      name: data.name ?? this.name,
      icon: data.icon ?? this.icon,
      theme_color: data.theme_color ?? this.theme_color,
      description: data.description ?? this.description,
      frequency: data.frequency ?? this.frequency,
      preferred_time: data.preferred_time ?? this.preferred_time,
      reward_stars: data.reward_stars ?? this.reward_stars,
      status: data.status ?? this.status,
    };

    const validation = HabitValidator.validateUserHabitData(updateData);
    if (!validation.isValid) {
      throw new Error(`Invalid update data: ${validation.errors.join(', ')}`);
    }

    // 应用更新
    if (data.name !== undefined) this.name = data.name;
    if (data.icon !== undefined) this.icon = data.icon;
    if (data.theme_color !== undefined) this.theme_color = data.theme_color;
    if (data.description !== undefined) this.description = data.description;
    if (data.frequency !== undefined) this.frequency = data.frequency;
    if (data.preferred_time !== undefined)
      this.preferred_time = data.preferred_time;
    if (data.reward_stars !== undefined) this.reward_stars = data.reward_stars;
    if (data.status !== undefined) this.status = data.status;

    this.updated_at = new Date();
  }

  /**
   * 验证用户习惯数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return HabitValidator.validateUserHabitData({
      user_id: this.user_id,
      baby_id: this.baby_id,
      template_id: this.template_id,
      name: this.name,
      icon: this.icon,
      theme_color: this.theme_color,
      description: this.description,
      frequency: this.frequency,
      preferred_time: this.preferred_time,
      reward_stars: this.reward_stars,
      status: this.status,
    });
  }

  /**
   * 激活习惯
   */
  activate(): void {
    this.status = UserHabitStatus.ACTIVE;
    this.updated_at = new Date();
  }

  /**
   * 归档习惯
   */
  archive(): void {
    this.status = UserHabitStatus.ARCHIVED;
    this.updated_at = new Date();
  }

  /**
   * 检查习惯是否处于活跃状态
   */
  isActive(): boolean {
    return this.status === UserHabitStatus.ACTIVE;
  }

  /**
   * 检查习惯是否已归档
   */
  isArchived(): boolean {
    return this.status === UserHabitStatus.ARCHIVED;
  }

  /**
   * 检查今天是否需要执行此习惯
   * @param dayOfWeek 1-7 表示周一到周日
   */
  isScheduledForDay(dayOfWeek: number): boolean {
    return this.frequency.includes(dayOfWeek);
  }

  /**
   * 检查今天是否需要执行此习惯
   */
  isScheduledForToday(): boolean {
    const today = new Date();
    const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay(); // 转换为1-7格式
    return this.isScheduledForDay(dayOfWeek);
  }

  /**
   * 更新打卡频率
   */
  updateFrequency(frequency: number[]): void {
    if (!HabitValidator.validateFrequency(frequency)) {
      throw new Error('Invalid frequency');
    }

    this.frequency = frequency;
    this.updated_at = new Date();
  }

  /**
   * 更新首选时间
   */
  updatePreferredTime(time?: string): void {
    if (!HabitValidator.validatePreferredTime(time)) {
      throw new Error('Invalid preferred time format');
    }

    this.preferred_time = time;
    this.updated_at = new Date();
  }

  /**
   * 更新奖励星星数
   */
  updateRewardStars(stars: number): void {
    if (!HabitValidator.validateRewardStars(stars)) {
      throw new Error('Invalid reward stars count');
    }

    this.reward_stars = stars;
    this.updated_at = new Date();
  }

  /**
   * 检查习惯是否属于指定用户
   */
  belongsToUser(userId: string): boolean {
    return this.user_id === userId;
  }

  /**
   * 检查习惯是否属于指定宝宝
   */
  belongsToBaby(babyId: string): boolean {
    return this.baby_id === babyId;
  }

  /**
   * 检查习惯是否基于模板创建
   */
  isBasedOnTemplate(): boolean {
    return !!this.template_id;
  }

  /**
   * 获取习惯的显示名称（用于UI展示）
   */
  getDisplayName(): string {
    return this.name;
  }

  /**
   * 获取习惯的完整描述信息
   */
  getFullDescription(): string {
    let description = this.description;

    if (this.preferred_time) {
      description += ` (建议时间: ${this.preferred_time})`;
    }

    return description;
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): Pick<
    UserHabit,
    | 'id'
    | 'user_id'
    | 'baby_id'
    | 'template_id'
    | 'name'
    | 'icon'
    | 'theme_color'
    | 'description'
    | 'frequency'
    | 'preferred_time'
    | 'reward_stars'
    | 'status'
    | 'created_at'
    | 'updated_at'
  > {
    return {
      id: this.id,
      user_id: this.user_id,
      baby_id: this.baby_id,
      template_id: this.template_id,
      name: this.name,
      icon: this.icon,
      theme_color: this.theme_color,
      description: this.description,
      frequency: this.frequency,
      preferred_time: this.preferred_time,
      reward_stars: this.reward_stars,
      status: this.status,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }
}
