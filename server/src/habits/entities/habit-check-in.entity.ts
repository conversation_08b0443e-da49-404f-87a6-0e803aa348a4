import { IHabitCheckIn, ICreateHabitCheckIn } from '../interfaces';
import { HabitValidator } from '../validators';

export class HabitCheckIn implements IHabitCheckIn {
  id: string;
  habit_id: string;
  user_id: string;
  baby_id: string;
  check_in_date: Date;
  check_in_time: Date;
  notes?: string;
  created_at: Date;

  constructor(partial: Partial<HabitCheckIn>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新打卡记录实例
   */
  static create(userId: string, data: ICreateHabitCheckIn): HabitCheckIn {
    const now = new Date();
    const checkInDate =
      data.check_in_date ||
      new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const validation = HabitValidator.validateHabitCheckInData({
      habit_id: data.habit_id,
      user_id: userId,
      baby_id: data.baby_id,
      check_in_date: checkInDate,
      check_in_time: now,
      notes: data.notes,
    });

    if (!validation.isValid) {
      throw new Error(
        `Invalid habit check-in data: ${validation.errors.join(', ')}`,
      );
    }

    return new HabitCheckIn({
      id: '', // 将由数据库生成
      habit_id: data.habit_id,
      user_id: userId,
      baby_id: data.baby_id,
      check_in_date: checkInDate,
      check_in_time: now,
      notes: data.notes,
      created_at: now,
    });
  }

  /**
   * 验证打卡记录数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return HabitValidator.validateHabitCheckInData({
      habit_id: this.habit_id,
      user_id: this.user_id,
      baby_id: this.baby_id,
      check_in_date: this.check_in_date,
      check_in_time: this.check_in_time,
      notes: this.notes,
    });
  }

  /**
   * 更新备注
   */
  updateNotes(notes?: string): void {
    if (notes && notes.length > 500) {
      throw new Error('Notes must be less than 500 characters');
    }

    this.notes = notes;
  }

  /**
   * 检查打卡记录是否属于指定用户
   */
  belongsToUser(userId: string): boolean {
    return this.user_id === userId;
  }

  /**
   * 检查打卡记录是否属于指定宝宝
   */
  belongsToBaby(babyId: string): boolean {
    return this.baby_id === babyId;
  }

  /**
   * 检查打卡记录是否属于指定习惯
   */
  belongsToHabit(habitId: string): boolean {
    return this.habit_id === habitId;
  }

  /**
   * 检查是否是今天的打卡记录
   */
  isToday(): boolean {
    const today = new Date();
    const todayDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const checkInDate = new Date(
      this.check_in_date.getFullYear(),
      this.check_in_date.getMonth(),
      this.check_in_date.getDate(),
    );

    return todayDate.getTime() === checkInDate.getTime();
  }

  /**
   * 检查是否是指定日期的打卡记录
   */
  isOnDate(date: Date): boolean {
    const targetDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
    );
    const checkInDate = new Date(
      this.check_in_date.getFullYear(),
      this.check_in_date.getMonth(),
      this.check_in_date.getDate(),
    );

    return targetDate.getTime() === checkInDate.getTime();
  }

  /**
   * 获取打卡日期的字符串表示（YYYY-MM-DD）
   */
  getDateString(): string {
    const year = this.check_in_date.getFullYear();
    const month = String(this.check_in_date.getMonth() + 1).padStart(2, '0');
    const day = String(this.check_in_date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * 获取打卡时间的字符串表示（HH:mm）
   */
  getTimeString(): string {
    const hours = String(this.check_in_time.getHours()).padStart(2, '0');
    const minutes = String(this.check_in_time.getMinutes()).padStart(2, '0');

    return `${hours}:${minutes}`;
  }

  /**
   * 获取打卡的完整时间描述
   */
  getFullTimeDescription(): string {
    const dateStr = this.getDateString();
    const timeStr = this.getTimeString();

    return `${dateStr} ${timeStr}`;
  }

  /**
   * 检查打卡是否有备注
   */
  hasNotes(): boolean {
    return !!(this.notes && this.notes.trim().length > 0);
  }

  /**
   * 获取打卡的显示备注（如果没有备注则返回默认文本）
   */
  getDisplayNotes(): string {
    return this.notes && this.notes.trim().length > 0 ? this.notes : '无备注';
  }

  /**
   * 检查打卡时间是否在指定时间范围内
   */
  isWithinTimeRange(startTime: string, endTime: string): boolean {
    const checkInTimeStr = this.getTimeString();
    return checkInTimeStr >= startTime && checkInTimeStr <= endTime;
  }

  /**
   * 计算打卡延迟时间（相对于首选时间）
   * @param preferredTime 首选时间 HH:mm 格式
   * @returns 延迟分钟数，负数表示提前
   */
  getDelayMinutes(preferredTime: string): number {
    const [preferredHours, preferredMinutes] = preferredTime
      .split(':')
      .map(Number);
    const checkInHours = this.check_in_time.getHours();
    const checkInMinutes = this.check_in_time.getMinutes();

    const preferredTotalMinutes = preferredHours * 60 + preferredMinutes;
    const checkInTotalMinutes = checkInHours * 60 + checkInMinutes;

    return checkInTotalMinutes - preferredTotalMinutes;
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): Pick<
    HabitCheckIn,
    | 'id'
    | 'habit_id'
    | 'user_id'
    | 'baby_id'
    | 'check_in_date'
    | 'check_in_time'
    | 'notes'
    | 'created_at'
  > {
    return {
      id: this.id,
      habit_id: this.habit_id,
      user_id: this.user_id,
      baby_id: this.baby_id,
      check_in_date: this.check_in_date,
      check_in_time: this.check_in_time,
      notes: this.notes,
      created_at: this.created_at,
    };
  }
}
