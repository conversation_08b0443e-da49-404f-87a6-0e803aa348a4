import {
  IHabitTemplate,
  ICreateHabitTemplate,
  IUpdateHabitTemplate,
} from '../interfaces';
import { HabitValidator } from '../validators';

export class HabitTemplate implements IHabitTemplate {
  id: string;
  category_id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;

  constructor(partial: Partial<HabitTemplate>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新习惯模板实例
   */
  static create(data: ICreateHabitTemplate): HabitTemplate {
    const validation = HabitValidator.validateHabitTemplateData({
      category_id: data.category_id,
      name: data.name,
      icon: data.icon,
      theme_color: data.theme_color,
      description: data.description,
      is_active: data.is_active,
    });

    if (!validation.isValid) {
      throw new Error(
        `Invalid habit template data: ${validation.errors.join(', ')}`,
      );
    }

    const now = new Date();
    return new HabitTemplate({
      id: '', // 将由数据库生成
      category_id: data.category_id,
      name: data.name,
      icon: data.icon,
      theme_color: data.theme_color,
      description: data.description,
      is_active: data.is_active ?? true,
      created_at: now,
      updated_at: now,
    });
  }

  /**
   * 更新习惯模板信息
   */
  update(data: IUpdateHabitTemplate): void {
    // 验证更新数据
    const updateData = {
      category_id: data.category_id ?? this.category_id,
      name: data.name ?? this.name,
      icon: data.icon ?? this.icon,
      theme_color: data.theme_color ?? this.theme_color,
      description: data.description ?? this.description,
      is_active: data.is_active ?? this.is_active,
    };

    const validation = HabitValidator.validateHabitTemplateData(updateData);
    if (!validation.isValid) {
      throw new Error(`Invalid update data: ${validation.errors.join(', ')}`);
    }

    // 应用更新
    if (data.category_id !== undefined) this.category_id = data.category_id;
    if (data.name !== undefined) this.name = data.name;
    if (data.icon !== undefined) this.icon = data.icon;
    if (data.theme_color !== undefined) this.theme_color = data.theme_color;
    if (data.description !== undefined) this.description = data.description;
    if (data.is_active !== undefined) this.is_active = data.is_active;

    this.updated_at = new Date();
  }

  /**
   * 验证习惯模板数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return HabitValidator.validateHabitTemplateData({
      category_id: this.category_id,
      name: this.name,
      icon: this.icon,
      theme_color: this.theme_color,
      description: this.description,
      is_active: this.is_active,
    });
  }

  /**
   * 激活模板
   */
  activate(): void {
    this.is_active = true;
    this.updated_at = new Date();
  }

  /**
   * 停用模板
   */
  deactivate(): void {
    this.is_active = false;
    this.updated_at = new Date();
  }

  /**
   * 更新主题色
   */
  updateThemeColor(themeColor: string): void {
    if (!HabitValidator.validateThemeColor(themeColor)) {
      throw new Error('Invalid theme color format');
    }

    this.theme_color = themeColor;
    this.updated_at = new Date();
  }

  /**
   * 更新描述
   */
  updateDescription(description: string): void {
    if (!HabitValidator.validateDescription(description)) {
      throw new Error('Invalid description');
    }

    this.description = description;
    this.updated_at = new Date();
  }

  /**
   * 检查模板是否属于指定分类
   */
  belongsToCategory(categoryId: string): boolean {
    return this.category_id === categoryId;
  }

  /**
   * 检查模板名称是否匹配（用于搜索）
   */
  matchesName(searchTerm: string): boolean {
    return this.name.toLowerCase().includes(searchTerm.toLowerCase());
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): Pick<
    HabitTemplate,
    | 'id'
    | 'category_id'
    | 'name'
    | 'icon'
    | 'theme_color'
    | 'description'
    | 'is_active'
    | 'created_at'
    | 'updated_at'
  > {
    return {
      id: this.id,
      category_id: this.category_id,
      name: this.name,
      icon: this.icon,
      theme_color: this.theme_color,
      description: this.description,
      is_active: this.is_active,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }
}
