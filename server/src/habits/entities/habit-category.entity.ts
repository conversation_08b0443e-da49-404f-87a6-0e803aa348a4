import {
  IHabitCategory,
  ICreateHabitCategory,
  IUpdateHabitCategory,
} from '../interfaces';
import { HabitValidator } from '../validators';

export class HabitCategory implements IHabitCategory {
  id: string;
  name: string;
  icon: string;
  parent_id?: string;
  level: number;
  sort_order: number;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;

  // 关联关系（可选）
  parent?: IHabitCategory;
  children?: IHabitCategory[];

  constructor(partial: Partial<HabitCategory>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新习惯分类实例
   */
  static create(data: ICreateHabitCategory): HabitCategory {
    // 验证层级结构
    if (data.level === 1 && data.parent_id) {
      throw new Error('一级分类不能有父分类');
    }
    if (data.level === 2 && !data.parent_id) {
      throw new Error('二级分类必须指定父分类');
    }
    if (data.level < 1 || data.level > 2) {
      throw new Error('分类层级只能是1或2');
    }

    const validation = HabitValidator.validateHabitCategoryData({
      name: data.name,
      icon: data.icon,
      sort_order: data.sort_order,
      is_active: data.is_active,
    });

    if (!validation.isValid) {
      throw new Error(
        `Invalid habit category data: ${validation.errors.join(', ')}`,
      );
    }

    const now = new Date();
    return new HabitCategory({
      id: '', // 将由数据库生成
      name: data.name,
      icon: data.icon,
      parent_id: data.parent_id,
      level: data.level,
      sort_order: data.sort_order ?? 0,
      is_active: data.is_active ?? true,
      created_at: now,
      updated_at: now,
    });
  }

  /**
   * 更新习惯分类信息
   */
  update(data: IUpdateHabitCategory): void {
    // 验证更新数据
    const updateData = {
      name: data.name ?? this.name,
      icon: data.icon ?? this.icon,
      sort_order: data.sort_order ?? this.sort_order,
      is_active: data.is_active ?? this.is_active,
    };

    const validation = HabitValidator.validateHabitCategoryData(updateData);
    if (!validation.isValid) {
      throw new Error(`Invalid update data: ${validation.errors.join(', ')}`);
    }

    // 应用更新
    if (data.name !== undefined) this.name = data.name;
    if (data.icon !== undefined) this.icon = data.icon;
    if (data.sort_order !== undefined) this.sort_order = data.sort_order;
    if (data.is_active !== undefined) this.is_active = data.is_active;

    this.updated_at = new Date();
  }

  /**
   * 验证习惯分类数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return HabitValidator.validateHabitCategoryData({
      name: this.name,
      icon: this.icon,
      sort_order: this.sort_order,
      is_active: this.is_active,
    });
  }

  /**
   * 激活分类
   */
  activate(): void {
    this.is_active = true;
    this.updated_at = new Date();
  }

  /**
   * 停用分类
   */
  deactivate(): void {
    this.is_active = false;
    this.updated_at = new Date();
  }

  /**
   * 更新排序顺序
   */
  updateSortOrder(sortOrder: number): void {
    if (!HabitValidator.validateSortOrder(sortOrder)) {
      throw new Error('Invalid sort order');
    }

    this.sort_order = sortOrder;
    this.updated_at = new Date();
  }

  /**
   * 检查分类是否可以删除（需要检查是否有关联的模板）
   */
  canBeDeleted(): boolean {
    // 这个方法的具体实现需要在服务层中检查是否有关联的模板
    // 这里只是提供接口，实际逻辑在服务层
    return this.is_active; // 简单示例，实际需要检查关联数据
  }

  /**
   * 检查是否为一级分类
   */
  isTopLevel(): boolean {
    return this.level === 1 && !this.parent_id;
  }

  /**
   * 检查是否为二级分类
   */
  isSubCategory(): boolean {
    return this.level === 2 && !!this.parent_id;
  }

  /**
   * 检查是否有子分类
   */
  hasChildren(): boolean {
    return this.children ? this.children.length > 0 : false;
  }

  /**
   * 获取所有子分类ID
   */
  getChildrenIds(): string[] {
    return this.children ? this.children.map((child) => child.id) : [];
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): Pick<
    HabitCategory,
    | 'id'
    | 'name'
    | 'icon'
    | 'parent_id'
    | 'level'
    | 'sort_order'
    | 'is_active'
    | 'created_at'
    | 'updated_at'
  > {
    return {
      id: this.id,
      name: this.name,
      icon: this.icon,
      parent_id: this.parent_id,
      level: this.level,
      sort_order: this.sort_order,
      is_active: this.is_active,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }
}
