import { HabitCategory } from '../habit-category.entity';
import { ICreateHabitCategory, IUpdateHabitCategory } from '../../interfaces';

describe('HabitCategory Entity', () => {
  const validCategoryData: ICreateHabitCategory = {
    name: '生活自理',
    icon: 'life-care-icon',
    sort_order: 1,
    is_active: true,
  };

  describe('create', () => {
    it('should create a new habit category with valid data', () => {
      const category = HabitCategory.create(validCategoryData);

      expect(category.name).toBe(validCategoryData.name);
      expect(category.icon).toBe(validCategoryData.icon);
      expect(category.sort_order).toBe(validCategoryData.sort_order);
      expect(category.is_active).toBe(validCategoryData.is_active);
      expect(category.created_at).toBeInstanceOf(Date);
      expect(category.updated_at).toBeInstanceOf(Date);
      expect(category.id).toBe(''); // 由数据库生成
    });

    it('should create a habit category with default values', () => {
      const minimalData: ICreateHabitCategory = {
        name: '健康习惯',
        icon: 'health-icon',
      };

      const category = HabitCategory.create(minimalData);

      expect(category.name).toBe(minimalData.name);
      expect(category.icon).toBe(minimalData.icon);
      expect(category.sort_order).toBe(0); // 默认值
      expect(category.is_active).toBe(true); // 默认值
    });

    it('should throw error with invalid name', () => {
      const invalidData = {
        ...validCategoryData,
        name: '', // 空名称
      };

      expect(() => HabitCategory.create(invalidData)).toThrow(
        'Invalid habit category data',
      );
    });

    it('should throw error with invalid icon', () => {
      const invalidData = {
        ...validCategoryData,
        icon: '', // 空图标
      };

      expect(() => HabitCategory.create(invalidData)).toThrow(
        'Invalid habit category data',
      );
    });

    it('should throw error with invalid sort_order', () => {
      const invalidData = {
        ...validCategoryData,
        sort_order: -1, // 负数
      };

      expect(() => HabitCategory.create(invalidData)).toThrow(
        'Invalid habit category data',
      );
    });
  });

  describe('update', () => {
    let category: HabitCategory;

    beforeEach(() => {
      category = HabitCategory.create(validCategoryData);
    });

    it('should update category name', () => {
      const updateData: IUpdateHabitCategory = {
        name: '更新后的分类名',
      };

      category.update(updateData);

      expect(category.name).toBe(updateData.name);
      expect(category.updated_at).toBeInstanceOf(Date);
    });

    it('should update category icon', () => {
      const updateData: IUpdateHabitCategory = {
        icon: 'new-icon',
      };

      category.update(updateData);

      expect(category.icon).toBe(updateData.icon);
    });

    it('should update sort order', () => {
      const updateData: IUpdateHabitCategory = {
        sort_order: 5,
      };

      category.update(updateData);

      expect(category.sort_order).toBe(updateData.sort_order);
    });

    it('should update active status', () => {
      const updateData: IUpdateHabitCategory = {
        is_active: false,
      };

      category.update(updateData);

      expect(category.is_active).toBe(false);
    });

    it('should throw error with invalid update data', () => {
      const invalidData: IUpdateHabitCategory = {
        name: '', // 空名称
      };

      expect(() => category.update(invalidData)).toThrow('Invalid update data');
    });

    it('should not update unchanged fields', () => {
      const originalName = category.name;
      const updateData: IUpdateHabitCategory = {
        icon: 'new-icon',
      };

      category.update(updateData);

      expect(category.name).toBe(originalName);
      expect(category.icon).toBe(updateData.icon);
    });
  });

  describe('validate', () => {
    it('should return valid for correct data', () => {
      const category = HabitCategory.create(validCategoryData);
      const validation = category.validate();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect data', () => {
      const category = HabitCategory.create(validCategoryData);
      // 直接修改属性以测试验证
      category.name = '';

      const validation = category.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('activate', () => {
    it('should activate the category', () => {
      const category = HabitCategory.create({
        ...validCategoryData,
        is_active: false,
      });

      category.activate();

      expect(category.is_active).toBe(true);
      expect(category.updated_at).toBeInstanceOf(Date);
    });
  });

  describe('deactivate', () => {
    it('should deactivate the category', () => {
      const category = HabitCategory.create(validCategoryData);

      category.deactivate();

      expect(category.is_active).toBe(false);
      expect(category.updated_at).toBeInstanceOf(Date);
    });
  });

  describe('updateSortOrder', () => {
    it('should update sort order with valid value', () => {
      const category = HabitCategory.create(validCategoryData);

      category.updateSortOrder(10);

      expect(category.sort_order).toBe(10);
      expect(category.updated_at).toBeInstanceOf(Date);
    });

    it('should throw error with invalid sort order', () => {
      const category = HabitCategory.create(validCategoryData);

      expect(() => category.updateSortOrder(-1)).toThrow('Invalid sort order');
      expect(() => category.updateSortOrder(10000)).toThrow(
        'Invalid sort order',
      );
    });
  });

  describe('canBeDeleted', () => {
    it('should return true for active category', () => {
      const category = HabitCategory.create(validCategoryData);

      expect(category.canBeDeleted()).toBe(true);
    });

    it('should return false for inactive category', () => {
      const category = HabitCategory.create({
        ...validCategoryData,
        is_active: false,
      });

      expect(category.canBeDeleted()).toBe(false);
    });
  });

  describe('toResponse', () => {
    it('should return response DTO format', () => {
      const category = HabitCategory.create(validCategoryData);
      category.id = 'test-id'; // 模拟数据库生成的ID

      const response = category.toResponse();

      expect(response).toEqual({
        id: 'test-id',
        name: validCategoryData.name,
        icon: validCategoryData.icon,
        sort_order: validCategoryData.sort_order,
        is_active: validCategoryData.is_active,
        created_at: category.created_at,
        updated_at: category.updated_at,
      });
    });
  });

  describe('constructor', () => {
    it('should create instance with partial data', () => {
      const partialData = {
        id: 'test-id',
        name: '测试分类',
        icon: 'test-icon',
        sort_order: 1,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const category = new HabitCategory(partialData);

      expect(category.id).toBe(partialData.id);
      expect(category.name).toBe(partialData.name);
      expect(category.icon).toBe(partialData.icon);
      expect(category.sort_order).toBe(partialData.sort_order);
      expect(category.is_active).toBe(partialData.is_active);
      expect(category.created_at).toBe(partialData.created_at);
      expect(category.updated_at).toBe(partialData.updated_at);
    });
  });
});
