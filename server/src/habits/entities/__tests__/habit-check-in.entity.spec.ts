import { HabitCheckIn } from '../habit-check-in.entity';
import { ICreateHabitCheckIn } from '../../interfaces';

describe('HabitCheckIn Entity', () => {
  const userId = '123e4567-e89b-12d3-a456-426614174000';
  const validCheckInData: ICreateHabitCheckIn = {
    habit_id: '456e7890-e12b-34c5-a678-901234567890',
    baby_id: '987fcdeb-51a2-43d1-8c67-123456789abc',
    check_in_date: new Date('2024-01-15'),
    notes: '今天刷牙很认真',
  };

  describe('create', () => {
    it('should create a new habit check-in with valid data', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.habit_id).toBe(validCheckInData.habit_id);
      expect(checkIn.user_id).toBe(userId);
      expect(checkIn.baby_id).toBe(validCheckInData.baby_id);
      expect(checkIn.check_in_date).toEqual(validCheckInData.check_in_date);
      expect(checkIn.check_in_time).toBeInstanceOf(Date);
      expect(checkIn.notes).toBe(validCheckInData.notes);
      expect(checkIn.created_at).toBeInstanceOf(Date);
      expect(checkIn.id).toBe(''); // 由数据库生成
    });

    it('should create a habit check-in with default date (today)', () => {
      const minimalData: ICreateHabitCheckIn = {
        habit_id: '456e7890-e12b-34c5-a678-901234567890',
        baby_id: '987fcdeb-51a2-43d1-8c67-123456789abc',
      };

      const checkIn = HabitCheckIn.create(userId, minimalData);

      expect(checkIn.habit_id).toBe(minimalData.habit_id);
      expect(checkIn.user_id).toBe(userId);
      expect(checkIn.baby_id).toBe(minimalData.baby_id);
      expect(checkIn.check_in_date).toBeInstanceOf(Date);
      expect(checkIn.check_in_time).toBeInstanceOf(Date);
      expect(checkIn.notes).toBeUndefined();
    });

    it('should throw error with invalid habit_id', () => {
      const invalidData = {
        ...validCheckInData,
        habit_id: 'invalid-uuid',
      };

      expect(() => HabitCheckIn.create(userId, invalidData)).toThrow(
        'Invalid habit check-in data',
      );
    });

    it('should throw error with invalid user_id', () => {
      expect(() =>
        HabitCheckIn.create('invalid-uuid', validCheckInData),
      ).toThrow('Invalid habit check-in data');
    });

    it('should throw error with invalid baby_id', () => {
      const invalidData = {
        ...validCheckInData,
        baby_id: 'invalid-uuid',
      };

      expect(() => HabitCheckIn.create(userId, invalidData)).toThrow(
        'Invalid habit check-in data',
      );
    });

    it('should throw error with future check-in date', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1); // 明天

      const invalidData = {
        ...validCheckInData,
        check_in_date: futureDate,
      };

      expect(() => HabitCheckIn.create(userId, invalidData)).toThrow(
        'Invalid habit check-in data',
      );
    });
  });

  describe('validate', () => {
    it('should return valid for correct data', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const validation = checkIn.validate();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect data', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      // 直接修改属性以测试验证
      checkIn.habit_id = 'invalid-uuid';

      const validation = checkIn.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('updateNotes', () => {
    it('should update notes with valid value', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const newNotes = '更新后的备注';

      checkIn.updateNotes(newNotes);

      expect(checkIn.notes).toBe(newNotes);
    });

    it('should update notes to undefined', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      checkIn.updateNotes(undefined);

      expect(checkIn.notes).toBeUndefined();
    });

    it('should throw error with notes too long', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const longNotes = 'a'.repeat(501); // 超过500字符

      expect(() => checkIn.updateNotes(longNotes)).toThrow(
        'Notes must be less than 500 characters',
      );
    });
  });

  describe('belongsToUser', () => {
    it('should return true for matching user', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.belongsToUser(userId)).toBe(true);
    });

    it('should return false for non-matching user', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.belongsToUser('different-user-id')).toBe(false);
    });
  });

  describe('belongsToBaby', () => {
    it('should return true for matching baby', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.belongsToBaby(validCheckInData.baby_id)).toBe(true);
    });

    it('should return false for non-matching baby', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.belongsToBaby('different-baby-id')).toBe(false);
    });
  });

  describe('belongsToHabit', () => {
    it('should return true for matching habit', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.belongsToHabit(validCheckInData.habit_id)).toBe(true);
    });

    it('should return false for non-matching habit', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.belongsToHabit('different-habit-id')).toBe(false);
    });
  });

  describe('isToday', () => {
    it("should return true for today's check-in", () => {
      const todayData = {
        ...validCheckInData,
        check_in_date: new Date(), // 今天
      };
      const checkIn = HabitCheckIn.create(userId, todayData);

      expect(checkIn.isToday()).toBe(true);
    });

    it('should return false for non-today check-in', () => {
      const yesterdayData = {
        ...validCheckInData,
        check_in_date: new Date('2024-01-15'), // 指定日期
      };
      const checkIn = HabitCheckIn.create(userId, yesterdayData);

      expect(checkIn.isToday()).toBe(false);
    });
  });

  describe('isOnDate', () => {
    it('should return true for matching date', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const targetDate = new Date('2024-01-15');

      expect(checkIn.isOnDate(targetDate)).toBe(true);
    });

    it('should return false for non-matching date', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const targetDate = new Date('2024-01-16');

      expect(checkIn.isOnDate(targetDate)).toBe(false);
    });
  });

  describe('getDateString', () => {
    it('should return date in YYYY-MM-DD format', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.getDateString()).toBe('2024-01-15');
    });
  });

  describe('getTimeString', () => {
    it('should return time in HH:mm format', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const timeString = checkIn.getTimeString();

      expect(timeString).toMatch(/^\d{2}:\d{2}$/);
    });
  });

  describe('getFullTimeDescription', () => {
    it('should return full time description', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      const fullDescription = checkIn.getFullTimeDescription();

      expect(fullDescription).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/);
      expect(fullDescription).toContain('2024-01-15');
    });
  });

  describe('hasNotes', () => {
    it('should return true when notes exist', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.hasNotes()).toBe(true);
    });

    it('should return false when notes are empty', () => {
      const checkIn = HabitCheckIn.create(userId, {
        ...validCheckInData,
        notes: undefined,
      });

      expect(checkIn.hasNotes()).toBe(false);
    });

    it('should return false when notes are whitespace only', () => {
      const checkIn = HabitCheckIn.create(userId, {
        ...validCheckInData,
        notes: '   ',
      });

      expect(checkIn.hasNotes()).toBe(false);
    });
  });

  describe('getDisplayNotes', () => {
    it('should return notes when they exist', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);

      expect(checkIn.getDisplayNotes()).toBe(validCheckInData.notes);
    });

    it('should return default text when notes are empty', () => {
      const checkIn = HabitCheckIn.create(userId, {
        ...validCheckInData,
        notes: undefined,
      });

      expect(checkIn.getDisplayNotes()).toBe('无备注');
    });
  });

  describe('isWithinTimeRange', () => {
    it('should return true when check-in time is within range', () => {
      // 创建一个特定时间的打卡记录
      const specificTime = new Date('2024-01-15T10:30:00');
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      checkIn.check_in_time = specificTime;

      expect(checkIn.isWithinTimeRange('10:00', '11:00')).toBe(true);
    });

    it('should return false when check-in time is outside range', () => {
      const specificTime = new Date('2024-01-15T12:30:00');
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      checkIn.check_in_time = specificTime;

      expect(checkIn.isWithinTimeRange('10:00', '11:00')).toBe(false);
    });
  });

  describe('getDelayMinutes', () => {
    it('should return positive delay for late check-in', () => {
      const specificTime = new Date('2024-01-15T10:30:00'); // 10:30
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      checkIn.check_in_time = specificTime;

      const delay = checkIn.getDelayMinutes('10:00'); // 首选时间 10:00
      expect(delay).toBe(30); // 延迟30分钟
    });

    it('should return negative delay for early check-in', () => {
      const specificTime = new Date('2024-01-15T09:30:00'); // 09:30
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      checkIn.check_in_time = specificTime;

      const delay = checkIn.getDelayMinutes('10:00'); // 首选时间 10:00
      expect(delay).toBe(-30); // 提前30分钟
    });

    it('should return zero for on-time check-in', () => {
      const specificTime = new Date('2024-01-15T10:00:00'); // 10:00
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      checkIn.check_in_time = specificTime;

      const delay = checkIn.getDelayMinutes('10:00'); // 首选时间 10:00
      expect(delay).toBe(0); // 准时
    });
  });

  describe('toResponse', () => {
    it('should return response DTO format', () => {
      const checkIn = HabitCheckIn.create(userId, validCheckInData);
      checkIn.id = 'test-id'; // 模拟数据库生成的ID

      const response = checkIn.toResponse();

      expect(response).toEqual({
        id: 'test-id',
        habit_id: validCheckInData.habit_id,
        user_id: userId,
        baby_id: validCheckInData.baby_id,
        check_in_date: validCheckInData.check_in_date,
        check_in_time: checkIn.check_in_time,
        notes: validCheckInData.notes,
        created_at: checkIn.created_at,
      });
    });
  });

  describe('constructor', () => {
    it('should create instance with partial data', () => {
      const partialData = {
        id: 'test-id',
        habit_id: '456e7890-e12b-34c5-a678-901234567890',
        user_id: userId,
        baby_id: '987fcdeb-51a2-43d1-8c67-123456789abc',
        check_in_date: new Date('2024-01-15'),
        check_in_time: new Date('2024-01-15T10:30:00'),
        notes: '测试备注',
        created_at: new Date(),
      };

      const checkIn = new HabitCheckIn(partialData);

      expect(checkIn.id).toBe(partialData.id);
      expect(checkIn.habit_id).toBe(partialData.habit_id);
      expect(checkIn.user_id).toBe(partialData.user_id);
      expect(checkIn.baby_id).toBe(partialData.baby_id);
      expect(checkIn.check_in_date).toBe(partialData.check_in_date);
      expect(checkIn.check_in_time).toBe(partialData.check_in_time);
      expect(checkIn.notes).toBe(partialData.notes);
      expect(checkIn.created_at).toBe(partialData.created_at);
    });
  });
});
