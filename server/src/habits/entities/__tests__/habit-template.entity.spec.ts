import { HabitTemplate } from '../habit-template.entity';
import { ICreateHabitTemplate, IUpdateHabitTemplate } from '../../interfaces';

describe('HabitTemplate Entity', () => {
  const validTemplateData: ICreateHabitTemplate = {
    category_id: '123e4567-e89b-12d3-a456-426614174000',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
    is_active: true,
  };

  describe('create', () => {
    it('should create a new habit template with valid data', () => {
      const template = HabitTemplate.create(validTemplateData);

      expect(template.category_id).toBe(validTemplateData.category_id);
      expect(template.name).toBe(validTemplateData.name);
      expect(template.icon).toBe(validTemplateData.icon);
      expect(template.theme_color).toBe(validTemplateData.theme_color);
      expect(template.description).toBe(validTemplateData.description);
      expect(template.is_active).toBe(validTemplateData.is_active);
      expect(template.created_at).toBeInstanceOf(Date);
      expect(template.updated_at).toBeInstanceOf(Date);
      expect(template.id).toBe(''); // 由数据库生成
    });

    it('should create a habit template with default values', () => {
      const minimalData: ICreateHabitTemplate = {
        category_id: '123e4567-e89b-12d3-a456-426614174000',
        name: '户外活动',
        icon: 'outdoor-icon',
        theme_color: '#4ECDC4',
        description: '每天进行户外活动',
      };

      const template = HabitTemplate.create(minimalData);

      expect(template.category_id).toBe(minimalData.category_id);
      expect(template.name).toBe(minimalData.name);
      expect(template.icon).toBe(minimalData.icon);
      expect(template.theme_color).toBe(minimalData.theme_color);
      expect(template.description).toBe(minimalData.description);
      expect(template.is_active).toBe(true); // 默认值
    });

    it('should throw error with invalid category_id', () => {
      const invalidData = {
        ...validTemplateData,
        category_id: 'invalid-uuid',
      };

      expect(() => HabitTemplate.create(invalidData)).toThrow(
        'Invalid habit template data',
      );
    });

    it('should throw error with invalid name', () => {
      const invalidData = {
        ...validTemplateData,
        name: '', // 空名称
      };

      expect(() => HabitTemplate.create(invalidData)).toThrow(
        'Invalid habit template data',
      );
    });

    it('should throw error with invalid theme_color', () => {
      const invalidData = {
        ...validTemplateData,
        theme_color: 'invalid-color', // 无效颜色格式
      };

      expect(() => HabitTemplate.create(invalidData)).toThrow(
        'Invalid habit template data',
      );
    });

    it('should throw error with invalid icon', () => {
      const invalidData = {
        ...validTemplateData,
        icon: '', // 空图标
      };

      expect(() => HabitTemplate.create(invalidData)).toThrow(
        'Invalid habit template data',
      );
    });
  });

  describe('update', () => {
    let template: HabitTemplate;

    beforeEach(() => {
      template = HabitTemplate.create(validTemplateData);
    });

    it('should update template name', () => {
      const updateData: IUpdateHabitTemplate = {
        name: '更新后的习惯名',
      };

      template.update(updateData);

      expect(template.name).toBe(updateData.name);
      expect(template.updated_at).toBeInstanceOf(Date);
    });

    it('should update template category', () => {
      const updateData: IUpdateHabitTemplate = {
        category_id: '987fcdeb-51a2-43d1-9c67-123456789abc',
      };

      template.update(updateData);

      expect(template.category_id).toBe(updateData.category_id);
    });

    it('should update template icon', () => {
      const updateData: IUpdateHabitTemplate = {
        icon: 'new-icon',
      };

      template.update(updateData);

      expect(template.icon).toBe(updateData.icon);
    });

    it('should update theme color', () => {
      const updateData: IUpdateHabitTemplate = {
        theme_color: '#00FF00',
      };

      template.update(updateData);

      expect(template.theme_color).toBe(updateData.theme_color);
    });

    it('should update description', () => {
      const updateData: IUpdateHabitTemplate = {
        description: '新的描述内容',
      };

      template.update(updateData);

      expect(template.description).toBe(updateData.description);
    });

    it('should update active status', () => {
      const updateData: IUpdateHabitTemplate = {
        is_active: false,
      };

      template.update(updateData);

      expect(template.is_active).toBe(false);
    });

    it('should throw error with invalid update data', () => {
      const invalidData: IUpdateHabitTemplate = {
        name: '', // 空名称
      };

      expect(() => template.update(invalidData)).toThrow('Invalid update data');
    });

    it('should not update unchanged fields', () => {
      const originalName = template.name;
      const updateData: IUpdateHabitTemplate = {
        icon: 'new-icon',
      };

      template.update(updateData);

      expect(template.name).toBe(originalName);
      expect(template.icon).toBe(updateData.icon);
    });
  });

  describe('validate', () => {
    it('should return valid for correct data', () => {
      const template = HabitTemplate.create(validTemplateData);
      const validation = template.validate();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect data', () => {
      const template = HabitTemplate.create(validTemplateData);
      // 直接修改属性以测试验证
      template.name = '';

      const validation = template.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('activate', () => {
    it('should activate the template', () => {
      const template = HabitTemplate.create({
        ...validTemplateData,
        is_active: false,
      });

      template.activate();

      expect(template.is_active).toBe(true);
      expect(template.updated_at).toBeInstanceOf(Date);
    });
  });

  describe('deactivate', () => {
    it('should deactivate the template', () => {
      const template = HabitTemplate.create(validTemplateData);

      template.deactivate();

      expect(template.is_active).toBe(false);
      expect(template.updated_at).toBeInstanceOf(Date);
    });
  });

  describe('updateThemeColor', () => {
    it('should update theme color with valid value', () => {
      const template = HabitTemplate.create(validTemplateData);

      template.updateThemeColor('#00FF00');

      expect(template.theme_color).toBe('#00FF00');
      expect(template.updated_at).toBeInstanceOf(Date);
    });

    it('should throw error with invalid theme color', () => {
      const template = HabitTemplate.create(validTemplateData);

      expect(() => template.updateThemeColor('invalid-color')).toThrow(
        'Invalid theme color format',
      );
      expect(() => template.updateThemeColor('#GGG')).toThrow(
        'Invalid theme color format',
      );
    });
  });

  describe('updateDescription', () => {
    it('should update description with valid value', () => {
      const template = HabitTemplate.create(validTemplateData);
      const newDescription = '新的描述内容';

      template.updateDescription(newDescription);

      expect(template.description).toBe(newDescription);
      expect(template.updated_at).toBeInstanceOf(Date);
    });

    it('should throw error with invalid description', () => {
      const template = HabitTemplate.create(validTemplateData);
      const longDescription = 'a'.repeat(501); // 超过500字符

      expect(() => template.updateDescription(longDescription)).toThrow(
        'Invalid description',
      );
    });
  });

  describe('belongsToCategory', () => {
    it('should return true for matching category', () => {
      const template = HabitTemplate.create(validTemplateData);

      expect(template.belongsToCategory(validTemplateData.category_id)).toBe(
        true,
      );
    });

    it('should return false for non-matching category', () => {
      const template = HabitTemplate.create(validTemplateData);

      expect(template.belongsToCategory('different-category-id')).toBe(false);
    });
  });

  describe('matchesName', () => {
    it('should return true for matching name (case insensitive)', () => {
      const template = HabitTemplate.create(validTemplateData);

      expect(template.matchesName('刷牙')).toBe(true);
      expect(template.matchesName('睡前')).toBe(true);
      expect(template.matchesName('BRUSH')).toBe(false); // 中文模板不匹配英文
    });

    it('should return false for non-matching name', () => {
      const template = HabitTemplate.create(validTemplateData);

      expect(template.matchesName('洗脸')).toBe(false);
      expect(template.matchesName('运动')).toBe(false);
    });
  });

  describe('toResponse', () => {
    it('should return response DTO format', () => {
      const template = HabitTemplate.create(validTemplateData);
      template.id = 'test-id'; // 模拟数据库生成的ID

      const response = template.toResponse();

      expect(response).toEqual({
        id: 'test-id',
        category_id: validTemplateData.category_id,
        name: validTemplateData.name,
        icon: validTemplateData.icon,
        theme_color: validTemplateData.theme_color,
        description: validTemplateData.description,
        is_active: validTemplateData.is_active,
        created_at: template.created_at,
        updated_at: template.updated_at,
      });
    });
  });

  describe('constructor', () => {
    it('should create instance with partial data', () => {
      const partialData = {
        id: 'test-id',
        category_id: '123e4567-e89b-12d3-a456-426614174000',
        name: '测试模板',
        icon: 'test-icon',
        theme_color: '#FF0000',
        description: '测试描述',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const template = new HabitTemplate(partialData);

      expect(template.id).toBe(partialData.id);
      expect(template.category_id).toBe(partialData.category_id);
      expect(template.name).toBe(partialData.name);
      expect(template.icon).toBe(partialData.icon);
      expect(template.theme_color).toBe(partialData.theme_color);
      expect(template.description).toBe(partialData.description);
      expect(template.is_active).toBe(partialData.is_active);
      expect(template.created_at).toBe(partialData.created_at);
      expect(template.updated_at).toBe(partialData.updated_at);
    });
  });
});
