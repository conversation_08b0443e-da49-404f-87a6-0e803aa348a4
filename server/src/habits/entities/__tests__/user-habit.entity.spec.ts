import { UserHabit } from '../user-habit.entity';
import {
  ICreateUserHabit,
  IUpdateUserHabit,
  UserHabitStatus,
} from '../../interfaces';

describe('UserHabit Entity', () => {
  const userId = '123e4567-e89b-12d3-a456-426614174000';
  const validHabitData: ICreateUserHabit = {
    baby_id: '987fcdeb-51a2-43d1-8c67-123456789abc',
    template_id: '456e7890-e12b-34c5-a678-901234567890',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
    frequency: [1, 2, 3, 4, 5, 6, 7], // 每天
    preferred_time: '20:30',
    reward_stars: 3,
    status: UserHabitStatus.ACTIVE,
  };

  describe('create', () => {
    it('should create a new user habit with valid data', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.user_id).toBe(userId);
      expect(habit.baby_id).toBe(validHabitData.baby_id);
      expect(habit.template_id).toBe(validHabitData.template_id);
      expect(habit.name).toBe(validHabitData.name);
      expect(habit.icon).toBe(validHabitData.icon);
      expect(habit.theme_color).toBe(validHabitData.theme_color);
      expect(habit.description).toBe(validHabitData.description);
      expect(habit.frequency).toEqual(validHabitData.frequency);
      expect(habit.preferred_time).toBe(validHabitData.preferred_time);
      expect(habit.reward_stars).toBe(validHabitData.reward_stars);
      expect(habit.status).toBe(validHabitData.status);
      expect(habit.created_at).toBeInstanceOf(Date);
      expect(habit.updated_at).toBeInstanceOf(Date);
      expect(habit.id).toBe(''); // 由数据库生成
    });

    it('should create a user habit with default values', () => {
      const minimalData: ICreateUserHabit = {
        baby_id: '987fcdeb-51a2-43d1-8c67-123456789abc',
        name: '户外活动',
        icon: 'outdoor-icon',
        theme_color: '#4ECDC4',
        description: '每天进行户外活动',
        frequency: [1, 3, 5], // 周一、三、五
      };

      const habit = UserHabit.create(userId, minimalData);

      expect(habit.user_id).toBe(userId);
      expect(habit.baby_id).toBe(minimalData.baby_id);
      expect(habit.template_id).toBeUndefined();
      expect(habit.name).toBe(minimalData.name);
      expect(habit.icon).toBe(minimalData.icon);
      expect(habit.theme_color).toBe(minimalData.theme_color);
      expect(habit.description).toBe(minimalData.description);
      expect(habit.frequency).toEqual(minimalData.frequency);
      expect(habit.preferred_time).toBeUndefined();
      expect(habit.reward_stars).toBe(1); // 默认值
      expect(habit.status).toBe(UserHabitStatus.ACTIVE); // 默认值
    });

    it('should throw error with invalid user_id', () => {
      expect(() => UserHabit.create('invalid-uuid', validHabitData)).toThrow(
        'Invalid user habit data',
      );
    });

    it('should throw error with invalid baby_id', () => {
      const invalidData = {
        ...validHabitData,
        baby_id: 'invalid-uuid',
      };

      expect(() => UserHabit.create(userId, invalidData)).toThrow(
        'Invalid user habit data',
      );
    });

    it('should throw error with invalid name', () => {
      const invalidData = {
        ...validHabitData,
        name: '', // 空名称
      };

      expect(() => UserHabit.create(userId, invalidData)).toThrow(
        'Invalid user habit data',
      );
    });

    it('should throw error with invalid frequency', () => {
      const invalidData = {
        ...validHabitData,
        frequency: [8, 9], // 无效的星期数
      };

      expect(() => UserHabit.create(userId, invalidData)).toThrow(
        'Invalid user habit data',
      );
    });

    it('should throw error with invalid preferred_time', () => {
      const invalidData = {
        ...validHabitData,
        preferred_time: '25:00', // 无效时间格式
      };

      expect(() => UserHabit.create(userId, invalidData)).toThrow(
        'Invalid user habit data',
      );
    });

    it('should throw error with invalid reward_stars', () => {
      const invalidData = {
        ...validHabitData,
        reward_stars: 6, // 超出范围
      };

      expect(() => UserHabit.create(userId, invalidData)).toThrow(
        'Invalid user habit data',
      );
    });
  });

  describe('update', () => {
    let habit: UserHabit;

    beforeEach(() => {
      habit = UserHabit.create(userId, validHabitData);
    });

    it('should update habit name', () => {
      const updateData: IUpdateUserHabit = {
        name: '更新后的习惯名',
      };

      habit.update(updateData);

      expect(habit.name).toBe(updateData.name);
      expect(habit.updated_at).toBeInstanceOf(Date);
    });

    it('should update habit frequency', () => {
      const updateData: IUpdateUserHabit = {
        frequency: [1, 3, 5], // 周一、三、五
      };

      habit.update(updateData);

      expect(habit.frequency).toEqual(updateData.frequency);
    });

    it('should update preferred time', () => {
      const updateData: IUpdateUserHabit = {
        preferred_time: '19:00',
      };

      habit.update(updateData);

      expect(habit.preferred_time).toBe(updateData.preferred_time);
    });

    it('should update reward stars', () => {
      const updateData: IUpdateUserHabit = {
        reward_stars: 5,
      };

      habit.update(updateData);

      expect(habit.reward_stars).toBe(updateData.reward_stars);
    });

    it('should update status', () => {
      const updateData: IUpdateUserHabit = {
        status: UserHabitStatus.ARCHIVED,
      };

      habit.update(updateData);

      expect(habit.status).toBe(UserHabitStatus.ARCHIVED);
    });

    it('should throw error with invalid update data', () => {
      const invalidData: IUpdateUserHabit = {
        name: '', // 空名称
      };

      expect(() => habit.update(invalidData)).toThrow('Invalid update data');
    });

    it('should not update unchanged fields', () => {
      const originalName = habit.name;
      const updateData: IUpdateUserHabit = {
        icon: 'new-icon',
      };

      habit.update(updateData);

      expect(habit.name).toBe(originalName);
      expect(habit.icon).toBe(updateData.icon);
    });
  });

  describe('validate', () => {
    it('should return valid for correct data', () => {
      const habit = UserHabit.create(userId, validHabitData);
      const validation = habit.validate();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect data', () => {
      const habit = UserHabit.create(userId, validHabitData);
      // 直接修改属性以测试验证
      habit.name = '';

      const validation = habit.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('activate', () => {
    it('should activate the habit', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        status: UserHabitStatus.ARCHIVED,
      });

      habit.activate();

      expect(habit.status).toBe(UserHabitStatus.ACTIVE);
      expect(habit.updated_at).toBeInstanceOf(Date);
    });
  });

  describe('archive', () => {
    it('should archive the habit', () => {
      const habit = UserHabit.create(userId, validHabitData);

      habit.archive();

      expect(habit.status).toBe(UserHabitStatus.ARCHIVED);
      expect(habit.updated_at).toBeInstanceOf(Date);
    });
  });

  describe('isActive', () => {
    it('should return true for active habit', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.isActive()).toBe(true);
    });

    it('should return false for archived habit', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        status: UserHabitStatus.ARCHIVED,
      });

      expect(habit.isActive()).toBe(false);
    });
  });

  describe('isArchived', () => {
    it('should return true for archived habit', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        status: UserHabitStatus.ARCHIVED,
      });

      expect(habit.isArchived()).toBe(true);
    });

    it('should return false for active habit', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.isArchived()).toBe(false);
    });
  });

  describe('isScheduledForDay', () => {
    it('should return true for scheduled day', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        frequency: [1, 3, 5], // 周一、三、五
      });

      expect(habit.isScheduledForDay(1)).toBe(true); // 周一
      expect(habit.isScheduledForDay(3)).toBe(true); // 周三
      expect(habit.isScheduledForDay(5)).toBe(true); // 周五
    });

    it('should return false for non-scheduled day', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        frequency: [1, 3, 5], // 周一、三、五
      });

      expect(habit.isScheduledForDay(2)).toBe(false); // 周二
      expect(habit.isScheduledForDay(4)).toBe(false); // 周四
      expect(habit.isScheduledForDay(6)).toBe(false); // 周六
      expect(habit.isScheduledForDay(7)).toBe(false); // 周日
    });
  });

  describe('isScheduledForToday', () => {
    it('should return correct value based on current day', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        frequency: [1, 2, 3, 4, 5, 6, 7], // 每天
      });

      expect(habit.isScheduledForToday()).toBe(true);
    });
  });

  describe('updateFrequency', () => {
    it('should update frequency with valid value', () => {
      const habit = UserHabit.create(userId, validHabitData);
      const newFrequency = [2, 4, 6]; // 周二、四、六

      habit.updateFrequency(newFrequency);

      expect(habit.frequency).toEqual(newFrequency);
      expect(habit.updated_at).toBeInstanceOf(Date);
    });

    it('should throw error with invalid frequency', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(() => habit.updateFrequency([8, 9])).toThrow('Invalid frequency');
      expect(() => habit.updateFrequency([])).toThrow('Invalid frequency');
    });
  });

  describe('updatePreferredTime', () => {
    it('should update preferred time with valid value', () => {
      const habit = UserHabit.create(userId, validHabitData);

      habit.updatePreferredTime('08:30');

      expect(habit.preferred_time).toBe('08:30');
      expect(habit.updated_at).toBeInstanceOf(Date);
    });

    it('should update preferred time to undefined', () => {
      const habit = UserHabit.create(userId, validHabitData);

      habit.updatePreferredTime(undefined);

      expect(habit.preferred_time).toBeUndefined();
      expect(habit.updated_at).toBeInstanceOf(Date);
    });

    it('should throw error with invalid time format', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(() => habit.updatePreferredTime('25:00')).toThrow(
        'Invalid preferred time format',
      );
      expect(() => habit.updatePreferredTime('invalid')).toThrow(
        'Invalid preferred time format',
      );
    });
  });

  describe('updateRewardStars', () => {
    it('should update reward stars with valid value', () => {
      const habit = UserHabit.create(userId, validHabitData);

      habit.updateRewardStars(5);

      expect(habit.reward_stars).toBe(5);
      expect(habit.updated_at).toBeInstanceOf(Date);
    });

    it('should throw error with invalid stars count', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(() => habit.updateRewardStars(0)).toThrow(
        'Invalid reward stars count',
      );
      expect(() => habit.updateRewardStars(6)).toThrow(
        'Invalid reward stars count',
      );
    });
  });

  describe('belongsToUser', () => {
    it('should return true for matching user', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.belongsToUser(userId)).toBe(true);
    });

    it('should return false for non-matching user', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.belongsToUser('different-user-id')).toBe(false);
    });
  });

  describe('belongsToBaby', () => {
    it('should return true for matching baby', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.belongsToBaby(validHabitData.baby_id)).toBe(true);
    });

    it('should return false for non-matching baby', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.belongsToBaby('different-baby-id')).toBe(false);
    });
  });

  describe('isBasedOnTemplate', () => {
    it('should return true when template_id exists', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.isBasedOnTemplate()).toBe(true);
    });

    it('should return false when template_id is undefined', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        template_id: undefined,
      });

      expect(habit.isBasedOnTemplate()).toBe(false);
    });
  });

  describe('getDisplayName', () => {
    it('should return habit name', () => {
      const habit = UserHabit.create(userId, validHabitData);

      expect(habit.getDisplayName()).toBe(validHabitData.name);
    });
  });

  describe('getFullDescription', () => {
    it('should return description with preferred time', () => {
      const habit = UserHabit.create(userId, validHabitData);

      const fullDescription = habit.getFullDescription();

      expect(fullDescription).toBe(
        `${validHabitData.description} (建议时间: ${validHabitData.preferred_time})`,
      );
    });

    it('should return description without preferred time when not set', () => {
      const habit = UserHabit.create(userId, {
        ...validHabitData,
        preferred_time: undefined,
      });

      const fullDescription = habit.getFullDescription();

      expect(fullDescription).toBe(validHabitData.description);
    });
  });

  describe('toResponse', () => {
    it('should return response DTO format', () => {
      const habit = UserHabit.create(userId, validHabitData);
      habit.id = 'test-id'; // 模拟数据库生成的ID

      const response = habit.toResponse();

      expect(response).toEqual({
        id: 'test-id',
        user_id: userId,
        baby_id: validHabitData.baby_id,
        template_id: validHabitData.template_id,
        name: validHabitData.name,
        icon: validHabitData.icon,
        theme_color: validHabitData.theme_color,
        description: validHabitData.description,
        frequency: validHabitData.frequency,
        preferred_time: validHabitData.preferred_time,
        reward_stars: validHabitData.reward_stars,
        status: validHabitData.status,
        created_at: habit.created_at,
        updated_at: habit.updated_at,
      });
    });
  });

  describe('constructor', () => {
    it('should create instance with partial data', () => {
      const partialData = {
        id: 'test-id',
        user_id: userId,
        baby_id: '987fcdeb-51a2-43d1-8c67-123456789abc',
        name: '测试习惯',
        icon: 'test-icon',
        theme_color: '#FF0000',
        description: '测试描述',
        frequency: [1, 2, 3],
        reward_stars: 2,
        status: UserHabitStatus.ACTIVE,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const habit = new UserHabit(partialData);

      expect(habit.id).toBe(partialData.id);
      expect(habit.user_id).toBe(partialData.user_id);
      expect(habit.baby_id).toBe(partialData.baby_id);
      expect(habit.name).toBe(partialData.name);
      expect(habit.icon).toBe(partialData.icon);
      expect(habit.theme_color).toBe(partialData.theme_color);
      expect(habit.description).toBe(partialData.description);
      expect(habit.frequency).toEqual(partialData.frequency);
      expect(habit.reward_stars).toBe(partialData.reward_stars);
      expect(habit.status).toBe(partialData.status);
      expect(habit.created_at).toBe(partialData.created_at);
      expect(habit.updated_at).toBe(partialData.updated_at);
    });
  });
});
