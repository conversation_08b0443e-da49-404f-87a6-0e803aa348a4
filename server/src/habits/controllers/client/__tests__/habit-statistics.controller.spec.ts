import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { HabitStatisticsController } from '../habit-statistics.controller';
import { HabitStatisticsService } from '../../../services/habit-statistics.service';
import { BabyRepository } from '../../../../baby/baby.repository';
import { BusinessException } from '../../../../common/exceptions/business.exception';
import { ErrorCode } from '../../../../common/enums';
import { StatisticsViewType } from '../../../interfaces/habit-statistics.interface';

describe('HabitStatisticsController', () => {
  let controller: HabitStatisticsController;
  let habitStatisticsService: jest.Mocked<HabitStatisticsService>;
  let babyRepository: jest.Mocked<BabyRepository>;

  const mockUserId = 'user-123';
  const mockBabyId = 'baby-123';
  const mockHabitId = 'habit-123';

  const mockDefaultBaby = {
    id: mockBabyId,
    owner_id: mockUserId,
    nickname: '小宝',
    birth_date: new Date('2023-01-01'),
    gender: 1, // 1: 男, 2: 女
    avatar_url: undefined,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockDailyStats = {
    date: new Date('2024-01-01'),
    habits: [
      {
        habit_id: mockHabitId,
        habit_name: '睡前刷牙',
        habit_icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        is_completed: true,
        check_in_time: new Date('2024-01-01T20:00:00Z'),
      },
    ],
    completion_rate: 1.0,
  };

  const mockWeeklyStats = {
    week_start: new Date('2024-01-01'),
    week_end: new Date('2024-01-07'),
    daily_completion: [
      {
        date: new Date('2024-01-01'),
        completion_rate: 1.0,
        completed_count: 1,
        total_count: 1,
      },
    ],
    overall_completion_rate: 1.0,
  };

  const mockMonthlyStats = {
    year: 2024,
    month: 1,
    calendar_data: [
      {
        date: new Date('2024-01-01'),
        completion_rate: 1.0,
        completed_count: 1,
        total_count: 1,
      },
    ],
    overall_completion_rate: 1.0,
  };

  const mockOverviewStats = {
    today_completion_rate: 1.0,
    today_completed_count: 1,
    today_total_count: 1,
    today_timeline: [
      {
        habit_id: mockHabitId,
        habit_name: '睡前刷牙',
        habit_icon: 'brush-teeth-icon',
        theme_color: '#FF6B6B',
        is_completed: true,
        check_in_time: new Date('2024-01-01T20:00:00Z'),
        preferred_time: '20:00',
      },
    ],
  };

  const mockHabitDetailStats = {
    habit_id: mockHabitId,
    current_month_data: [
      {
        date: new Date('2024-01-01'),
        is_completed: true,
        check_in_time: new Date('2024-01-01T20:00:00Z'),
        notes: '今天刷牙很认真',
      },
    ],
    completion_rate: 1.0,
    streak_days: 5,
    total_check_ins: 30,
  };

  beforeEach(async () => {
    const mockHabitStatisticsService = {
      getDailyStatistics: jest.fn(),
      getWeeklyStatistics: jest.fn(),
      getMonthlyStatistics: jest.fn(),
      getOverviewStatistics: jest.fn(),
      getHabitDetailStatistics: jest.fn(),
    };

    const mockBabyRepository = {
      findFirstBabyByOwner: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HabitStatisticsController],
      providers: [
        {
          provide: HabitStatisticsService,
          useValue: mockHabitStatisticsService,
        },
        {
          provide: BabyRepository,
          useValue: mockBabyRepository,
        },
      ],
    }).compile();

    controller = module.get<HabitStatisticsController>(
      HabitStatisticsController,
    );
    habitStatisticsService = module.get(HabitStatisticsService);
    babyRepository = module.get(BabyRepository);

    // Default mock behavior - return default baby
    babyRepository.findFirstBabyByOwner.mockResolvedValue(mockDefaultBaby);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getDailyStatistics', () => {
    it('should return daily statistics', async () => {
      habitStatisticsService.getDailyStatistics.mockResolvedValue(
        mockDailyStats,
      );

      const query = { baby_id: mockBabyId, date: '2024-01-01' };
      const result = await controller.getDailyStatistics(mockUserId, query);

      expect(result).toEqual(mockDailyStats);
      expect(habitStatisticsService.getDailyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date('2024-01-01'),
      );
    });

    it('should use default baby when baby_id is not provided', async () => {
      habitStatisticsService.getDailyStatistics.mockResolvedValue(
        mockDailyStats,
      );

      const query = { date: '2024-01-01' };
      const result = await controller.getDailyStatistics(
        mockUserId,
        query as any,
      );

      expect(result).toEqual(mockDailyStats);
      expect(babyRepository.findFirstBabyByOwner).toHaveBeenCalledWith(
        mockUserId,
      );
      expect(habitStatisticsService.getDailyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date('2024-01-01'),
      );
    });

    it('should throw BusinessException when user has no babies', async () => {
      babyRepository.findFirstBabyByOwner.mockResolvedValue(null);

      const query = { date: '2024-01-01' };

      await expect(
        controller.getDailyStatistics(mockUserId, query as any),
      ).rejects.toThrow(BusinessException);
    });

    it('should use current date when date is not provided', async () => {
      habitStatisticsService.getDailyStatistics.mockResolvedValue(
        mockDailyStats,
      );

      const query = { baby_id: mockBabyId };
      await controller.getDailyStatistics(mockUserId, query);

      expect(habitStatisticsService.getDailyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        expect.any(Date),
      );
    });
  });

  describe('getWeeklyStatistics', () => {
    it('should return weekly statistics', async () => {
      habitStatisticsService.getWeeklyStatistics.mockResolvedValue(
        mockWeeklyStats,
      );

      const query = { baby_id: mockBabyId, week_start: '2024-01-01' };
      const result = await controller.getWeeklyStatistics(mockUserId, query);

      expect(result).toEqual(mockWeeklyStats);
      expect(habitStatisticsService.getWeeklyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date('2024-01-01'),
      );
    });

    it('should use default baby when baby_id is not provided', async () => {
      habitStatisticsService.getWeeklyStatistics.mockResolvedValue(
        mockWeeklyStats,
      );

      const query = { week_start: '2024-01-01' };
      const result = await controller.getWeeklyStatistics(
        mockUserId,
        query as any,
      );

      expect(result).toEqual(mockWeeklyStats);
      expect(babyRepository.findFirstBabyByOwner).toHaveBeenCalledWith(
        mockUserId,
      );
      expect(habitStatisticsService.getWeeklyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date('2024-01-01'),
      );
    });
  });

  describe('getMonthlyStatistics', () => {
    it('should return monthly statistics', async () => {
      habitStatisticsService.getMonthlyStatistics.mockResolvedValue(
        mockMonthlyStats,
      );

      const query = { baby_id: mockBabyId, year: 2024, month: 1 };
      const result = await controller.getMonthlyStatistics(mockUserId, query);

      expect(result).toEqual(mockMonthlyStats);
      expect(habitStatisticsService.getMonthlyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        2024,
        1,
      );
    });

    it('should use current year and month when not provided', async () => {
      habitStatisticsService.getMonthlyStatistics.mockResolvedValue(
        mockMonthlyStats,
      );

      const query = { baby_id: mockBabyId };
      await controller.getMonthlyStatistics(mockUserId, query);

      const now = new Date();
      expect(habitStatisticsService.getMonthlyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        now.getFullYear(),
        now.getMonth() + 1,
      );
    });

    it('should use default baby when baby_id is not provided', async () => {
      habitStatisticsService.getMonthlyStatistics.mockResolvedValue(
        mockMonthlyStats,
      );

      const query = { year: 2024, month: 1 };
      const result = await controller.getMonthlyStatistics(mockUserId, query);

      expect(result).toEqual(mockMonthlyStats);
      expect(babyRepository.findFirstBabyByOwner).toHaveBeenCalledWith(
        mockUserId,
      );
      expect(habitStatisticsService.getMonthlyStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        2024,
        1,
      );
    });
  });

  describe('getOverviewStatistics', () => {
    it('should return overview statistics', async () => {
      habitStatisticsService.getOverviewStatistics.mockResolvedValue(
        mockOverviewStats,
      );

      const result = await controller.getOverviewStatistics(
        mockUserId,
        mockBabyId,
      );

      expect(result).toEqual(mockOverviewStats);
      expect(habitStatisticsService.getOverviewStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
      );
    });

    it('should use default baby when baby_id is not provided', async () => {
      habitStatisticsService.getOverviewStatistics.mockResolvedValue(
        mockOverviewStats,
      );

      const result = await controller.getOverviewStatistics(mockUserId);

      expect(result).toEqual(mockOverviewStats);
      expect(babyRepository.findFirstBabyByOwner).toHaveBeenCalledWith(
        mockUserId,
      );
      expect(habitStatisticsService.getOverviewStatistics).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
      );
    });
  });

  describe('getHabitDetailStatistics', () => {
    it('should return habit detail statistics', async () => {
      habitStatisticsService.getHabitDetailStatistics.mockResolvedValue(
        mockHabitDetailStats,
      );

      const query = { baby_id: mockBabyId, year: 2024, month: 1 } as any;
      const result = await controller.getHabitDetailStatistics(
        { id: mockHabitId },
        mockUserId,
        query,
      );

      expect(result).toEqual(mockHabitDetailStats);
      expect(
        habitStatisticsService.getHabitDetailStatistics,
      ).toHaveBeenCalledWith(mockHabitId, mockUserId, mockBabyId, 2024, 1);
    });

    it('should use default baby when baby_id is not provided', async () => {
      habitStatisticsService.getHabitDetailStatistics.mockResolvedValue(
        mockHabitDetailStats,
      );

      const query = { year: 2024, month: 1 } as any;
      const result = await controller.getHabitDetailStatistics(
        { id: mockHabitId },
        mockUserId,
        query,
      );

      expect(result).toEqual(mockHabitDetailStats);
      expect(babyRepository.findFirstBabyByOwner).toHaveBeenCalledWith(
        mockUserId,
      );
      expect(
        habitStatisticsService.getHabitDetailStatistics,
      ).toHaveBeenCalledWith(mockHabitId, mockUserId, mockBabyId, 2024, 1);
    });
  });

  describe('getStatistics', () => {
    it('should return daily statistics when view_type is daily', async () => {
      habitStatisticsService.getDailyStatistics.mockResolvedValue(
        mockDailyStats,
      );

      const query = {
        baby_id: mockBabyId,
        view_type: StatisticsViewType.DAILY,
      };
      const result = await controller.getStatistics(mockUserId, query);

      expect(result).toEqual(mockDailyStats);
    });

    it('should return weekly statistics when view_type is weekly', async () => {
      habitStatisticsService.getWeeklyStatistics.mockResolvedValue(
        mockWeeklyStats,
      );

      const query = {
        baby_id: mockBabyId,
        view_type: StatisticsViewType.WEEKLY,
      };
      const result = await controller.getStatistics(mockUserId, query);

      expect(result).toEqual(mockWeeklyStats);
    });

    it('should return monthly statistics when view_type is monthly', async () => {
      habitStatisticsService.getMonthlyStatistics.mockResolvedValue(
        mockMonthlyStats,
      );

      const query = {
        baby_id: mockBabyId,
        view_type: StatisticsViewType.MONTHLY,
      };
      const result = await controller.getStatistics(mockUserId, query);

      expect(result).toEqual(mockMonthlyStats);
    });

    it('should return overview statistics when view_type is not specified', async () => {
      habitStatisticsService.getOverviewStatistics.mockResolvedValue(
        mockOverviewStats,
      );

      const query = { baby_id: mockBabyId };
      const result = await controller.getStatistics(mockUserId, query);

      expect(result).toEqual(mockOverviewStats);
    });

    it('should use default baby when baby_id is not provided', async () => {
      habitStatisticsService.getDailyStatistics.mockResolvedValue(
        mockDailyStats,
      );

      const query = { view_type: StatisticsViewType.DAILY };
      const result = await controller.getStatistics(mockUserId, query);

      expect(result).toEqual(mockDailyStats);
      expect(babyRepository.findFirstBabyByOwner).toHaveBeenCalledWith(
        mockUserId,
      );
    });
  });
});
