import { Test, TestingModule } from '@nestjs/testing';
import { UserHabitController } from '../user-habit.controller';
import { UserHabitService } from '../../../services/user-habit.service';
import { UserHabitResponseDto } from '../../../dto/user-habit/user-habit-response.dto';
import { CreateUserHabitDto } from '../../../dto/user-habit/create-user-habit.dto';
import { UpdateUserHabitDto } from '../../../dto/user-habit/update-user-habit.dto';
import { UserHabitStatus } from '../../../interfaces/user-habit.interface';

describe('UserHabitController', () => {
  let controller: UserHabitController;
  let userHabitService: jest.Mocked<UserHabitService>;

  const mockUserId = 'user-123';
  const mockHabitId = 'habit-123';
  const mockBabyId = 'baby-123';

  const mockUserHabit: UserHabitResponseDto = {
    id: mockHabitId,
    user_id: mockUserId,
    baby_id: mockBabyId,
    template_id: 'template-123',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿',
    frequency: [1, 2, 3, 4, 5, 6, 7],
    preferred_time: '20:00',
    reward_stars: 1,
    status: UserHabitStatus.ACTIVE,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockCreateDto: CreateUserHabitDto = {
    baby_id: mockBabyId,
    template_id: 'template-123',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿',
    frequency: [1, 2, 3, 4, 5, 6, 7],
    preferred_time: '20:00',
    reward_stars: 1,
    status: UserHabitStatus.ACTIVE,
  };

  const mockUpdateDto: UpdateUserHabitDto = {
    name: '更新后的习惯',
    description: '更新后的描述',
  };

  beforeEach(async () => {
    const mockUserHabitService = {
      findUserHabits: jest.fn(),
      getTodayHabits: jest.fn(),
      getHabitStats: jest.fn(),
      searchHabits: jest.fn(),
      findById: jest.fn(),
      create: jest.fn(),
      createFromTemplates: jest.fn(),
      update: jest.fn(),
      activate: jest.fn(),
      archive: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserHabitController],
      providers: [
        {
          provide: UserHabitService,
          useValue: mockUserHabitService,
        },
      ],
    }).compile();

    controller = module.get<UserHabitController>(UserHabitController);
    userHabitService = module.get(UserHabitService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getMyHabits', () => {
    it('should return user habits', async () => {
      const query = { baby_id: mockBabyId };
      userHabitService.findUserHabits.mockResolvedValue([mockUserHabit]);

      const result = await controller.getMyHabits(mockUserId, query);

      expect(result).toEqual([mockUserHabit]);
      expect(userHabitService.findUserHabits).toHaveBeenCalledWith(
        mockUserId,
        query,
      );
    });
  });

  describe('getTodayHabits', () => {
    it('should return today habits', async () => {
      userHabitService.getTodayHabits.mockResolvedValue([mockUserHabit]);

      const result = await controller.getTodayHabits(mockUserId, mockBabyId);

      expect(result).toEqual([mockUserHabit]);
      expect(userHabitService.getTodayHabits).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
      );
    });
  });

  describe('getHabitStats', () => {
    it('should return habit statistics', async () => {
      const mockStats = {
        total: 10,
        active: 8,
        archived: 2,
        todayScheduled: 5,
      };
      userHabitService.getHabitStats.mockResolvedValue(mockStats);

      const result = await controller.getHabitStats(mockUserId, mockBabyId);

      expect(result).toEqual(mockStats);
      expect(userHabitService.getHabitStats).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
      );
    });
  });

  describe('searchHabits', () => {
    it('should search habits', async () => {
      const searchTerm = '刷牙';
      userHabitService.searchHabits.mockResolvedValue([mockUserHabit]);

      const result = await controller.searchHabits(
        mockUserId,
        searchTerm,
        mockBabyId,
        20,
        0,
      );

      expect(result).toEqual([mockUserHabit]);
      expect(userHabitService.searchHabits).toHaveBeenCalledWith(
        mockUserId,
        searchTerm,
        mockBabyId,
        20,
        0,
      );
    });
  });

  describe('getHabitById', () => {
    it('should return habit by id', async () => {
      userHabitService.findById.mockResolvedValue(mockUserHabit);

      const result = await controller.getHabitById(
        { id: mockHabitId },
        mockUserId,
      );

      expect(result).toEqual(mockUserHabit);
      expect(userHabitService.findById).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
      );
    });
  });

  describe('createHabit', () => {
    it('should create a new habit', async () => {
      userHabitService.create.mockResolvedValue(mockUserHabit);

      const result = await controller.createHabit(mockUserId, mockCreateDto);

      expect(result).toEqual(mockUserHabit);
      expect(userHabitService.create).toHaveBeenCalledWith(
        mockUserId,
        mockCreateDto,
      );
    });
  });

  describe('createFromTemplates', () => {
    it('should create habits from templates', async () => {
      const templateIds = ['template-1', 'template-2'];
      const body = { baby_id: mockBabyId, template_ids: templateIds };
      userHabitService.createFromTemplates.mockResolvedValue([mockUserHabit]);

      const result = await controller.createFromTemplates(mockUserId, body);

      expect(result).toEqual([mockUserHabit]);
      expect(userHabitService.createFromTemplates).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        templateIds,
      );
    });
  });

  describe('updateHabit', () => {
    it('should update a habit', async () => {
      const updatedHabit = { ...mockUserHabit, name: '更新后的习惯' };
      userHabitService.update.mockResolvedValue(updatedHabit);

      const result = await controller.updateHabit(
        { id: mockHabitId },
        mockUserId,
        mockUpdateDto,
      );

      expect(result).toEqual(updatedHabit);
      expect(userHabitService.update).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        mockUpdateDto,
      );
    });
  });

  describe('activateHabit', () => {
    it('should activate a habit', async () => {
      userHabitService.activate.mockResolvedValue(mockUserHabit);

      const result = await controller.activateHabit(
        { id: mockHabitId },
        mockUserId,
      );

      expect(result).toEqual(mockUserHabit);
      expect(userHabitService.activate).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
      );
    });
  });

  describe('archiveHabit', () => {
    it('should archive a habit', async () => {
      const archivedHabit = {
        ...mockUserHabit,
        status: UserHabitStatus.ARCHIVED,
      };
      userHabitService.archive.mockResolvedValue(archivedHabit);

      const result = await controller.archiveHabit(
        { id: mockHabitId },
        mockUserId,
      );

      expect(result).toEqual(archivedHabit);
      expect(userHabitService.archive).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
      );
    });
  });

  describe('deleteHabit', () => {
    it('should delete a habit', async () => {
      userHabitService.delete.mockResolvedValue(undefined);

      await controller.deleteHabit({ id: mockHabitId }, mockUserId);

      expect(userHabitService.delete).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
      );
    });
  });
});
