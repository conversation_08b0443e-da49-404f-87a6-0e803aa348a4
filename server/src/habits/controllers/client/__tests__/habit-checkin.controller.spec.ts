import { Test, TestingModule } from '@nestjs/testing';
import { HabitCheckInController } from '../habit-checkin.controller';
import { HabitCheckInService } from '../../../services/habit-checkin.service';
import { HabitCheckInResponseDto } from '../../../dto/habit-check-in/habit-check-in-response.dto';
import { CreateHabitCheckInDto } from '../../../dto/habit-check-in/create-habit-check-in.dto';

describe('HabitCheckInController', () => {
  let controller: HabitCheckInController;
  let habitCheckInService: jest.Mocked<HabitCheckInService>;

  const mockUserId = 'user-123';
  const mockHabitId = 'habit-123';
  const mockBabyId = 'baby-123';
  const mockCheckInId = 'checkin-123';

  const mockCheckIn: HabitCheckInResponseDto = {
    id: mockCheckInId,
    habit_id: mockHabitId,
    user_id: mockUserId,
    baby_id: mockBabyId,
    check_in_date: new Date('2024-01-01'),
    check_in_time: new Date('2024-01-01T20:00:00Z'),
    notes: '今天刷牙很认真',
    created_at: new Date(),
  };

  const mockCreateDto: CreateHabitCheckInDto = {
    habit_id: mockHabitId,
    baby_id: mockBabyId,
    notes: '今天刷牙很认真',
  };

  beforeEach(async () => {
    const mockHabitCheckInService = {
      checkIn: jest.fn(),
      findCheckIns: jest.fn(),
      hasCheckedInToday: jest.fn(),
      findByDate: jest.fn(),
      getCheckInStats: jest.fn(),
      getTodayCheckIns: jest.fn(),
      findUserCheckIns: jest.fn(),
      getMonthlyStats: jest.fn(),
      deleteCheckIn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HabitCheckInController],
      providers: [
        {
          provide: HabitCheckInService,
          useValue: mockHabitCheckInService,
        },
      ],
    }).compile();

    controller = module.get<HabitCheckInController>(HabitCheckInController);
    habitCheckInService = module.get(HabitCheckInService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('checkIn', () => {
    it('should create a check-in record', async () => {
      habitCheckInService.checkIn.mockResolvedValue(mockCheckIn);

      const result = await controller.checkIn(
        { id: mockHabitId },
        mockUserId,
        mockCreateDto,
      );

      expect(result).toEqual(mockCheckIn);
      expect(habitCheckInService.checkIn).toHaveBeenCalledWith(mockUserId, {
        ...mockCreateDto,
        habit_id: mockHabitId,
      });
    });
  });

  describe('getHabitCheckIns', () => {
    it('should return habit check-ins', async () => {
      const query = { limit: 20, offset: 0 };
      habitCheckInService.findCheckIns.mockResolvedValue([mockCheckIn]);

      const result = await controller.getHabitCheckIns(
        { id: mockHabitId },
        mockUserId,
        query,
      );

      expect(result).toEqual([mockCheckIn]);
      expect(habitCheckInService.findCheckIns).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        query,
      );
    });
  });

  describe('hasCheckedInToday', () => {
    it('should return today check-in status when checked in', async () => {
      habitCheckInService.hasCheckedInToday.mockResolvedValue(true);
      habitCheckInService.findByDate.mockResolvedValue(mockCheckIn);

      const result = await controller.hasCheckedInToday(
        { id: mockHabitId },
        mockUserId,
      );

      expect(result).toEqual({
        hasCheckedIn: true,
        checkIn: mockCheckIn,
      });
      expect(habitCheckInService.hasCheckedInToday).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
      );
    });

    it('should return today check-in status when not checked in', async () => {
      habitCheckInService.hasCheckedInToday.mockResolvedValue(false);

      const result = await controller.hasCheckedInToday(
        { id: mockHabitId },
        mockUserId,
      );

      expect(result).toEqual({
        hasCheckedIn: false,
        checkIn: undefined,
      });
      expect(habitCheckInService.hasCheckedInToday).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
      );
    });
  });

  describe('getCheckInStats', () => {
    it('should return check-in statistics', async () => {
      const mockStats = {
        totalCheckIns: 10,
        streakDays: 5,
        completionRate: 80.5,
        lastCheckIn: new Date(),
      };
      habitCheckInService.getCheckInStats.mockResolvedValue(mockStats);

      const result = await controller.getCheckInStats(
        { id: mockHabitId },
        mockUserId,
        '2024-01-01',
        '2024-01-31',
      );

      expect(result).toEqual(mockStats);
      expect(habitCheckInService.getCheckInStats).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        '2024-01-01',
        '2024-01-31',
      );
    });
  });

  describe('getCheckInByDate', () => {
    it('should return check-in for specific date', async () => {
      const date = '2024-01-01';
      habitCheckInService.findByDate.mockResolvedValue(mockCheckIn);

      const result = await controller.getCheckInByDate(
        mockHabitId,
        date,
        mockUserId,
      );

      expect(result).toEqual(mockCheckIn);
      expect(habitCheckInService.findByDate).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        date,
      );
    });

    it('should return null when no check-in found for date', async () => {
      const date = '2024-01-01';
      habitCheckInService.findByDate.mockResolvedValue(null);

      const result = await controller.getCheckInByDate(
        mockHabitId,
        date,
        mockUserId,
      );

      expect(result).toBeNull();
      expect(habitCheckInService.findByDate).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        date,
      );
    });
  });

  describe('getTodayCheckIns', () => {
    it('should return today check-ins', async () => {
      habitCheckInService.getTodayCheckIns.mockResolvedValue([mockCheckIn]);

      const result = await controller.getTodayCheckIns(mockUserId, mockBabyId);

      expect(result).toEqual([mockCheckIn]);
      expect(habitCheckInService.getTodayCheckIns).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
      );
    });
  });

  describe('getUserCheckIns', () => {
    it('should return user check-ins', async () => {
      const query = { limit: 20, offset: 0 };
      habitCheckInService.findUserCheckIns.mockResolvedValue([mockCheckIn]);

      const result = await controller.getUserCheckIns(mockUserId, query);

      expect(result).toEqual([mockCheckIn]);
      expect(habitCheckInService.findUserCheckIns).toHaveBeenCalledWith(
        mockUserId,
        query,
      );
    });
  });

  describe('getMonthlyStats', () => {
    it('should return monthly statistics', async () => {
      const mockMonthlyStats = [
        { date: '2024-01-01', count: 3 },
        { date: '2024-01-02', count: 2 },
      ];
      habitCheckInService.getMonthlyStats.mockResolvedValue(mockMonthlyStats);

      const result = await controller.getMonthlyStats(
        mockUserId,
        2024,
        1,
        mockBabyId,
        mockHabitId,
      );

      expect(result).toEqual(mockMonthlyStats);
      expect(habitCheckInService.getMonthlyStats).toHaveBeenCalledWith(
        mockUserId,
        2024,
        1,
        mockBabyId,
        mockHabitId,
      );
    });
  });

  describe('deleteCheckIn', () => {
    it('should delete check-in record', async () => {
      habitCheckInService.deleteCheckIn.mockResolvedValue(undefined);

      await controller.deleteCheckIn({ id: mockCheckInId }, mockUserId);

      expect(habitCheckInService.deleteCheckIn).toHaveBeenCalledWith(
        mockCheckInId,
        mockUserId,
      );
    });
  });
});
