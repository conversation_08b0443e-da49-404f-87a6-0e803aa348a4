import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { OptionalJwtAuthGuard } from '../../../auth/guards/optional-jwt-auth.guard';
import { HabitCategoryService } from '../../services/habit-category.service';
import { HabitTemplateService } from '../../services/habit-template.service';
import {
  HabitLibraryResponseDto,
  HabitLibraryCategoryDto,
  HabitLibraryTemplateDto,
} from '../../dto/library/habit-library-response.dto';
import { HabitCategoryQueryDto } from '../../dto/habit-category/habit-category-query.dto';
import { HabitTemplateQueryDto } from '../../dto/habit-template/habit-template-query.dto';
import { HabitTemplateResponseDto } from '../../dto/habit-template/habit-template-response.dto';
import { UuidParamDto } from '../../dto/common/uuid-param.dto';
import { IHabitCategoryTree } from '../../interfaces/habit-category.interface';

@ApiTags('习惯库')
@Controller('habits/library')
@UseGuards(OptionalJwtAuthGuard)
@ApiBearerAuth()
export class HabitLibraryController {
  constructor(
    private readonly habitCategoryService: HabitCategoryService,
    private readonly habitTemplateService: HabitTemplateService,
  ) { }

  @Get()
  @ApiOperation({ summary: '获取习惯库（按层级分类显示）' })
  @ApiResponse({
    status: 200,
    description: '成功获取习惯库',
    type: HabitLibraryResponseDto,
  })
  async getHabitLibrary(): Promise<HabitLibraryResponseDto> {
    // 获取分类树形结构
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = true;
    const categoryTree =
      await this.habitCategoryService.findCategoryTree(categoryQuery);

    // 构建层级分类结构，只为二级分类（或没有子分类的一级分类）获取模板
    const categoriesWithTemplates: HabitLibraryCategoryDto[] = [];

    for (const topCategory of categoryTree) {
      if (topCategory.children && topCategory.children.length > 0) {
        // 有子分类的一级分类，处理其子分类
        for (const subCategory of topCategory.children) {
          const templates = await this.habitTemplateService.findByCategory(
            subCategory.id,
            false,
          );

          const libraryCategory = new HabitLibraryCategoryDto();
          libraryCategory.id = subCategory.id;
          libraryCategory.name = `${topCategory.name} - ${subCategory.name}`;
          libraryCategory.icon = subCategory.icon;
          libraryCategory.parent_id = subCategory.parent_id;
          libraryCategory.level = subCategory.level;
          libraryCategory.sort_order = subCategory.sort_order;
          libraryCategory.templates = templates.map((template) => {
            const libraryTemplate = new HabitLibraryTemplateDto();
            libraryTemplate.id = template.id;
            libraryTemplate.name = template.name;
            libraryTemplate.icon = template.icon;
            libraryTemplate.theme_color = template.theme_color;
            libraryTemplate.description = template.description;
            return libraryTemplate;
          });

          categoriesWithTemplates.push(libraryCategory);
        }
      } else {
        // 没有子分类的一级分类，直接获取其模板
        const templates = await this.habitTemplateService.findByCategory(
          topCategory.id,
          false,
        );

        const libraryCategory = new HabitLibraryCategoryDto();
        libraryCategory.id = topCategory.id;
        libraryCategory.name = topCategory.name;
        libraryCategory.icon = topCategory.icon;
        libraryCategory.level = topCategory.level;
        libraryCategory.sort_order = topCategory.sort_order;
        libraryCategory.templates = templates.map((template) => {
          const libraryTemplate = new HabitLibraryTemplateDto();
          libraryTemplate.id = template.id;
          libraryTemplate.name = template.name;
          libraryTemplate.icon = template.icon;
          libraryTemplate.theme_color = template.theme_color;
          libraryTemplate.description = template.description;
          return libraryTemplate;
        });

        categoriesWithTemplates.push(libraryCategory);
      }
    }

    const response = new HabitLibraryResponseDto();
    response.categories = categoriesWithTemplates;
    return response;
  }

  @Get('categories')
  @ApiOperation({ summary: '获取习惯分类列表（支持层级）' })
  @ApiResponse({
    status: 200,
    description: '成功获取分类列表',
    type: [HabitLibraryCategoryDto],
  })
  async getCategories(
    @Query() query: HabitCategoryQueryDto,
  ): Promise<HabitLibraryCategoryDto[]> {
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = query.is_active ?? true;
    categoryQuery.level = query.level;
    categoryQuery.parent_id = query.parent_id;

    const categories = await this.habitCategoryService.findAll(categoryQuery);

    return categories.map((category) => {
      const libraryCategory = new HabitLibraryCategoryDto();
      libraryCategory.id = category.id;
      libraryCategory.name = category.name;
      libraryCategory.icon = category.icon;
      libraryCategory.parent_id = category.parent_id;
      libraryCategory.level = category.level;
      libraryCategory.sort_order = category.sort_order;
      libraryCategory.templates = []; // 不包含模板，只返回分类信息
      return libraryCategory;
    });
  }

  @Get('categories/tree')
  @ApiOperation({ summary: '获取分类树形结构' })
  @ApiResponse({
    status: 200,
    description: '成功获取分类树',
    type: 'object',
  })
  async getCategoryTree(
    @Query() query: HabitCategoryQueryDto,
  ): Promise<IHabitCategoryTree[]> {
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = query.is_active ?? true;

    return this.habitCategoryService.findCategoryTree(categoryQuery);
  }

  @Get('templates/:categoryId')
  @ApiOperation({ summary: '获取指定分类下的习惯模板' })
  @ApiResponse({
    status: 200,
    description: '成功获取模板列表',
    type: [HabitLibraryTemplateDto],
  })
  async getTemplatesByCategory(
    @Param() params: UuidParamDto,
  ): Promise<HabitLibraryTemplateDto[]> {
    const templates = await this.habitTemplateService.findByCategory(
      params.id,
      false,
    );

    return templates.map((template) => {
      const libraryTemplate = new HabitLibraryTemplateDto();
      libraryTemplate.id = template.id;
      libraryTemplate.name = template.name;
      libraryTemplate.icon = template.icon;
      libraryTemplate.theme_color = template.theme_color;
      libraryTemplate.description = template.description;
      return libraryTemplate;
    });
  }

  @Get('search')
  @ApiOperation({ summary: '搜索习惯模板' })
  @ApiResponse({
    status: 200,
    description: '成功搜索模板',
    type: [HabitLibraryTemplateDto],
  })
  async searchTemplates(
    @Query() query: HabitTemplateQueryDto,
  ): Promise<HabitLibraryTemplateDto[]> {
    // 确保只搜索活跃的模板
    const searchQuery = { ...query, include_inactive: false };
    const templates = await this.habitTemplateService.findAll(searchQuery);

    return templates.map((template) => {
      const libraryTemplate = new HabitLibraryTemplateDto();
      libraryTemplate.id = template.id;
      libraryTemplate.name = template.name;
      libraryTemplate.icon = template.icon;
      libraryTemplate.theme_color = template.theme_color;
      libraryTemplate.description = template.description;
      return libraryTemplate;
    });
  }

  @Get('template/:id')
  @ApiOperation({ summary: '根据模版ID获取习惯模版详情' })
  @ApiResponse({
    status: 200,
    description: '成功获取模版详情',
    type: HabitTemplateResponseDto,
  })
  async getTemplateById(
    @Param() params: UuidParamDto,
  ): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.findById(params.id);
  }
}
