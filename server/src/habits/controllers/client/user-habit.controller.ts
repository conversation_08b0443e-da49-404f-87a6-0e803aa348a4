import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';
import { UserHabitService } from '../../services/user-habit.service';
import { CreateUserHabitDto } from '../../dto/user-habit/create-user-habit.dto';
import { UpdateUserHabitDto } from '../../dto/user-habit/update-user-habit.dto';
import { UserHabitResponseDto } from '../../dto/user-habit/user-habit-response.dto';
import { UserHabitQueryDto } from '../../dto/user-habit/user-habit-query.dto';
import { UuidParamDto } from '../../dto/common/uuid-param.dto';

@ApiTags('用户习惯')
@Controller('habits/my-habits')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserHabitController {
  constructor(private readonly userHabitService: UserHabitService) {}

  @Get()
  @ApiOperation({ summary: '获取我的习惯列表' })
  @ApiResponse({
    status: 200,
    description: '成功获取习惯列表',
    type: [UserHabitResponseDto],
  })
  async getMyHabits(
    @CurrentUser('id') userId: string,
    @Query() query: UserHabitQueryDto,
  ): Promise<UserHabitResponseDto[]> {
    return this.userHabitService.findUserHabits(userId, query);
  }

  @Get('today')
  @ApiOperation({ summary: '获取今日习惯列表' })
  @ApiResponse({
    status: 200,
    description: '成功获取今日习惯',
    type: [UserHabitResponseDto],
  })
  async getTodayHabits(
    @CurrentUser('id') userId: string,
    @Query('baby_id') babyId?: string,
  ): Promise<UserHabitResponseDto[]> {
    return this.userHabitService.getTodayHabits(userId, babyId);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取习惯统计信息' })
  @ApiResponse({
    status: 200,
    description: '成功获取统计信息',
  })
  async getHabitStats(
    @CurrentUser('id') userId: string,
    @Query('baby_id') babyId?: string,
  ): Promise<{
    total: number;
    active: number;
    archived: number;
    todayScheduled: number;
  }> {
    return this.userHabitService.getHabitStats(userId, babyId);
  }

  @Get('search')
  @ApiOperation({ summary: '搜索我的习惯' })
  @ApiResponse({
    status: 200,
    description: '成功搜索习惯',
    type: [UserHabitResponseDto],
  })
  async searchHabits(
    @CurrentUser('id') userId: string,
    @Query('q') searchTerm: string,
    @Query('baby_id') babyId?: string,
    @Query('limit') limit = 20,
    @Query('offset') offset = 0,
  ): Promise<UserHabitResponseDto[]> {
    return this.userHabitService.searchHabits(
      userId,
      searchTerm,
      babyId,
      limit,
      offset,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: '获取习惯详情' })
  @ApiResponse({
    status: 200,
    description: '成功获取习惯详情',
    type: UserHabitResponseDto,
  })
  async getHabitById(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
  ): Promise<UserHabitResponseDto> {
    return this.userHabitService.findById(params.id, userId);
  }

  @Post()
  @ApiOperation({ summary: '创建新习惯' })
  @ApiResponse({
    status: 201,
    description: '成功创建习惯',
    type: UserHabitResponseDto,
  })
  async createHabit(
    @CurrentUser('id') userId: string,
    @Body() createDto: CreateUserHabitDto,
  ): Promise<UserHabitResponseDto> {
    return this.userHabitService.create(userId, createDto);
  }

  @Post('from-templates')
  @ApiOperation({ summary: '从模板批量创建习惯' })
  @ApiResponse({
    status: 201,
    description: '成功从模板创建习惯',
    type: [UserHabitResponseDto],
  })
  async createFromTemplates(
    @CurrentUser('id') userId: string,
    @Body() body: { baby_id: string; template_ids: string[] },
  ): Promise<UserHabitResponseDto[]> {
    return this.userHabitService.createFromTemplates(
      userId,
      body.baby_id,
      body.template_ids,
    );
  }

  @Put(':id')
  @ApiOperation({ summary: '更新习惯' })
  @ApiResponse({
    status: 200,
    description: '成功更新习惯',
    type: UserHabitResponseDto,
  })
  async updateHabit(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
    @Body() updateDto: UpdateUserHabitDto,
  ): Promise<UserHabitResponseDto> {
    return this.userHabitService.update(params.id, userId, updateDto);
  }

  @Put(':id/activate')
  @ApiOperation({ summary: '激活习惯' })
  @ApiResponse({
    status: 200,
    description: '成功激活习惯',
    type: UserHabitResponseDto,
  })
  async activateHabit(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
  ): Promise<UserHabitResponseDto> {
    return this.userHabitService.activate(params.id, userId);
  }

  @Put(':id/archive')
  @ApiOperation({ summary: '归档习惯' })
  @ApiResponse({
    status: 200,
    description: '成功归档习惯',
    type: UserHabitResponseDto,
  })
  async archiveHabit(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
  ): Promise<UserHabitResponseDto> {
    return this.userHabitService.archive(params.id, userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除习惯' })
  @ApiResponse({
    status: 204,
    description: '成功删除习惯',
  })
  async deleteHabit(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
  ): Promise<void> {
    return this.userHabitService.delete(params.id, userId);
  }
}
