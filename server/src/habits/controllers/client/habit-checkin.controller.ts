import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';
import { HabitCheckInService } from '../../services/habit-checkin.service';
import { CreateHabitCheckInDto } from '../../dto/habit-check-in/create-habit-check-in.dto';
import { HabitCheckInResponseDto } from '../../dto/habit-check-in/habit-check-in-response.dto';
import { HabitCheckInQueryDto } from '../../dto/habit-check-in/habit-check-in-query.dto';
import { UuidParamDto } from '../../dto/common/uuid-param.dto';
import { BabyService } from '../../../baby/baby.service';

@ApiTags('习惯打卡')
@Controller('habits')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class HabitCheckInController {
  constructor(
    private readonly habitCheckInService: HabitCheckInService,
    private readonly babyService: BabyService,
  ) {}

  @Post('/check-in')
  @ApiOperation({ summary: '执行习惯打卡' })
  @ApiResponse({
    status: 201,
    description: '成功完成打卡',
    type: HabitCheckInResponseDto,
  })
  async checkIn(
    // @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
    @Body() createDto: CreateHabitCheckInDto,
  ): Promise<HabitCheckInResponseDto> {
    // 确保习惯ID与路径参数一致
    // createDto.habit_id = params.id;
    if (!createDto.baby_id) {
      const defaultBaby = await this.babyService.getDefaultBaby(userId);
      if (defaultBaby) {
        createDto.baby_id = defaultBaby.id;
      }
    }
    return this.habitCheckInService.checkIn(userId, createDto);
  }

  @Get(':id/check-ins')
  @ApiOperation({ summary: '获取习惯的打卡记录' })
  @ApiResponse({
    status: 200,
    description: '成功获取打卡记录',
    type: [HabitCheckInResponseDto],
  })
  async getHabitCheckIns(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
    @Query() query: HabitCheckInQueryDto,
  ): Promise<HabitCheckInResponseDto[]> {
    return this.habitCheckInService.findCheckIns(params.id, userId, query);
  }

  @Get(':id/check-ins/today')
  @ApiOperation({ summary: '检查今日是否已打卡' })
  @ApiResponse({
    status: 200,
    description: '返回今日打卡状态',
  })
  async hasCheckedInToday(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
  ): Promise<{ hasCheckedIn: boolean; checkIn?: HabitCheckInResponseDto }> {
    const hasCheckedIn = await this.habitCheckInService.hasCheckedInToday(
      params.id,
      userId,
    );

    let checkIn: HabitCheckInResponseDto | undefined;
    if (hasCheckedIn) {
      const today = new Date().toISOString().split('T')[0];
      const result = await this.habitCheckInService.findByDate(
        params.id,
        userId,
        today,
      );
      checkIn = result || undefined;
    }

    return {
      hasCheckedIn,
      checkIn: checkIn || undefined,
    };
  }

  @Get(':id/check-ins/stats')
  @ApiOperation({ summary: '获取习惯打卡统计' })
  @ApiResponse({
    status: 200,
    description: '成功获取打卡统计',
  })
  async getCheckInStats(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
  ): Promise<{
    totalCheckIns: number;
    streakDays: number;
    completionRate: number;
    lastCheckIn?: Date;
  }> {
    return this.habitCheckInService.getCheckInStats(
      params.id,
      userId,
      startDate,
      endDate,
    );
  }

  @Get(':id/check-ins/:date')
  @ApiOperation({ summary: '获取指定日期的打卡记录' })
  @ApiResponse({
    status: 200,
    description: '成功获取指定日期的打卡记录',
    type: HabitCheckInResponseDto,
  })
  async getCheckInByDate(
    @Param('id') habitId: string,
    @Param('date') date: string,
    @CurrentUser('id') userId: string,
  ): Promise<HabitCheckInResponseDto | null> {
    return this.habitCheckInService.findByDate(habitId, userId, date);
  }

  @Get('check-ins/today')
  @ApiOperation({ summary: '获取今日所有打卡记录' })
  @ApiResponse({
    status: 200,
    description: '成功获取今日打卡记录',
    type: [HabitCheckInResponseDto],
  })
  async getTodayCheckIns(
    @CurrentUser('id') userId: string,
    @Query('baby_id') babyId?: string,
  ): Promise<HabitCheckInResponseDto[]> {
    let finalBabyId = babyId;
    if (!finalBabyId) {
      const defaultBaby = await this.babyService.getDefaultBaby(userId);
      if (defaultBaby) {
        finalBabyId = defaultBaby.id;
      }
    }
    return this.habitCheckInService.getTodayCheckIns(userId, finalBabyId);
  }

  @Get('check-ins')
  @ApiOperation({ summary: '获取用户的所有打卡记录' })
  @ApiResponse({
    status: 200,
    description: '成功获取打卡记录',
    type: [HabitCheckInResponseDto],
  })
  async getUserCheckIns(
    @CurrentUser('id') userId: string,
    @Query() query: HabitCheckInQueryDto,
  ): Promise<HabitCheckInResponseDto[]> {
    return this.habitCheckInService.findUserCheckIns(userId, query);
  }

  @Get('check-ins/monthly-stats')
  @ApiOperation({ summary: '获取月度打卡统计' })
  @ApiResponse({
    status: 200,
    description: '成功获取月度统计',
  })
  async getMonthlyStats(
    @CurrentUser('id') userId: string,
    @Query('year') year: number,
    @Query('month') month: number,
    @Query('baby_id') babyId?: string,
    @Query('habit_id') habitId?: string,
  ): Promise<{ date: string; count: number }[]> {
    let finalBabyId = babyId;
    if (!finalBabyId) {
      const defaultBaby = await this.babyService.getDefaultBaby(userId);
      if (defaultBaby) {
        finalBabyId = defaultBaby.id;
      }
    }
    return this.habitCheckInService.getMonthlyStats(
      userId,
      year,
      month,
      finalBabyId,
      habitId,
    );
  }

  @Delete('check-ins/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除打卡记录（仅限当天）' })
  @ApiResponse({
    status: 204,
    description: '成功删除打卡记录',
  })
  async deleteCheckIn(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
  ): Promise<void> {
    return this.habitCheckInService.deleteCheckIn(params.id, userId);
  }
}
