import {
  Controller,
  Get,
  Query,
  Param,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';
import { HabitStatisticsService } from '../../services/habit-statistics.service';
import { BabyRepository } from '../../../baby/baby.repository';
import { BusinessException } from '../../../common/exceptions/business.exception';
import { ErrorCode } from '../../../common/enums';
import {
  StatisticsQueryDto,
  HabitDetailStatisticsQueryDto,
} from '../../dto/statistics/statistics-query.dto';
import {
  DailyStatisticsResponseDto,
  WeeklyStatisticsResponseDto,
  MonthlyStatisticsResponseDto,
  OverviewStatisticsResponseDto,
  HabitDetailStatisticsResponseDto,
} from '../../dto/statistics/statistics-response.dto';
import { StatisticsViewType } from '../../interfaces/habit-statistics.interface';
import { UuidParamDto } from '../../dto/common/uuid-param.dto';

@ApiTags('习惯统计')
@Controller('habits/statistics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class HabitStatisticsController {
  constructor(
    private readonly habitStatisticsService: HabitStatisticsService,
    private readonly babyRepository: BabyRepository,
  ) {}

  /**
   * 解析baby_id，如果没有提供则使用用户的默认宝宝
   */
  private async resolveBabyId(
    userId: string,
    providedBabyId?: string,
  ): Promise<string> {
    if (providedBabyId) {
      return providedBabyId;
    }

    const defaultBaby = await this.babyRepository.findFirstBabyByOwner(userId);
    if (!defaultBaby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        '请先创建宝宝档案',
        HttpStatus.BAD_REQUEST,
      );
    }

    return defaultBaby.id;
  }

  @Get('daily')
  @ApiOperation({ summary: '获取日统计数据' })
  @ApiResponse({
    status: 200,
    description: '成功获取日统计',
    type: DailyStatisticsResponseDto,
  })
  async getDailyStatistics(
    @CurrentUser('id') userId: string,
    @Query() query: StatisticsQueryDto,
  ): Promise<DailyStatisticsResponseDto> {
    const babyId = await this.resolveBabyId(userId, query.baby_id);
    const date = query.date ? new Date(query.date) : new Date();

    const statistics = await this.habitStatisticsService.getDailyStatistics(
      userId,
      babyId,
      date,
    );

    const response = new DailyStatisticsResponseDto();
    response.date = statistics.date;
    response.habits = statistics.habits;
    response.completion_rate = statistics.completion_rate;

    return response;
  }

  @Get('weekly')
  @ApiOperation({ summary: '获取周统计数据' })
  @ApiResponse({
    status: 200,
    description: '成功获取周统计',
    type: WeeklyStatisticsResponseDto,
  })
  async getWeeklyStatistics(
    @CurrentUser('id') userId: string,
    @Query() query: StatisticsQueryDto,
  ): Promise<WeeklyStatisticsResponseDto> {
    const babyId = await this.resolveBabyId(userId, query.baby_id);

    // 如果没有提供周开始日期，使用本周一
    let weekStart: Date;
    if (query.week_start) {
      weekStart = new Date(query.week_start);
    } else {
      const today = new Date();
      const dayOfWeek = today.getDay();
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 周日为0，需要特殊处理
      weekStart = new Date(today);
      weekStart.setDate(today.getDate() + mondayOffset);
    }

    const statistics = await this.habitStatisticsService.getWeeklyStatistics(
      userId,
      babyId,
      weekStart,
    );

    const response = new WeeklyStatisticsResponseDto();
    response.week_start = statistics.week_start;
    response.week_end = statistics.week_end;
    response.daily_completion = statistics.daily_completion;
    response.overall_completion_rate = statistics.overall_completion_rate;

    return response;
  }

  @Get('monthly')
  @ApiOperation({ summary: '获取月统计数据' })
  @ApiResponse({
    status: 200,
    description: '成功获取月统计',
    type: MonthlyStatisticsResponseDto,
  })
  async getMonthlyStatistics(
    @CurrentUser('id') userId: string,
    @Query() query: StatisticsQueryDto,
  ): Promise<MonthlyStatisticsResponseDto> {
    const babyId = await this.resolveBabyId(userId, query.baby_id);
    const now = new Date();
    const year = query.year || now.getFullYear();
    const month = query.month || now.getMonth() + 1;

    const statistics = await this.habitStatisticsService.getMonthlyStatistics(
      userId,
      babyId,
      year,
      month,
    );

    const response = new MonthlyStatisticsResponseDto();
    response.year = statistics.year;
    response.month = statistics.month;
    response.calendar_data = statistics.calendar_data;
    response.overall_completion_rate = statistics.overall_completion_rate;

    return response;
  }

  @Get('overview')
  @ApiOperation({ summary: '获取首页概览统计' })
  @ApiResponse({
    status: 200,
    description: '成功获取概览统计',
    type: OverviewStatisticsResponseDto,
  })
  async getOverviewStatistics(
    @CurrentUser('id') userId: string,
    @Query('baby_id') providedBabyId?: string,
  ): Promise<OverviewStatisticsResponseDto> {
    const babyId = await this.resolveBabyId(userId, providedBabyId);

    const statistics = await this.habitStatisticsService.getOverviewStatistics(
      userId,
      babyId,
    );

    const response = new OverviewStatisticsResponseDto();
    response.today_completion_rate = statistics.today_completion_rate;
    response.today_completed_count = statistics.today_completed_count;
    response.today_total_count = statistics.today_total_count;
    response.today_timeline = statistics.today_timeline;

    return response;
  }

  @Get('habit/:id')
  @ApiOperation({ summary: '获取习惯详情统计' })
  @ApiResponse({
    status: 200,
    description: '成功获取习惯详情统计',
    type: HabitDetailStatisticsResponseDto,
  })
  async getHabitDetailStatistics(
    @Param() params: UuidParamDto,
    @CurrentUser('id') userId: string,
    @Query() query: HabitDetailStatisticsQueryDto & { baby_id?: string },
  ): Promise<HabitDetailStatisticsResponseDto> {
    const babyId = await this.resolveBabyId(userId, query.baby_id);
    const now = new Date();
    const year = query.year || now.getFullYear();
    const month = query.month || now.getMonth() + 1;

    const statistics =
      await this.habitStatisticsService.getHabitDetailStatistics(
        params.id,
        userId,
        babyId,
        year,
        month,
      );

    const response = new HabitDetailStatisticsResponseDto();
    response.habit_id = statistics.habit_id;
    response.current_month_data = statistics.current_month_data;
    response.completion_rate = statistics.completion_rate;
    response.streak_days = statistics.streak_days;
    response.total_check_ins = statistics.total_check_ins;

    return response;
  }

  @Get()
  @ApiOperation({ summary: '获取统计数据（根据视图类型）' })
  @ApiResponse({
    status: 200,
    description: '成功获取统计数据',
  })
  async getStatistics(
    @CurrentUser('id') userId: string,
    @Query() query: StatisticsQueryDto,
  ): Promise<
    | DailyStatisticsResponseDto
    | WeeklyStatisticsResponseDto
    | MonthlyStatisticsResponseDto
    | OverviewStatisticsResponseDto
  > {
    switch (query.view_type) {
      case StatisticsViewType.DAILY:
        return this.getDailyStatistics(userId, query);

      case StatisticsViewType.WEEKLY:
        return this.getWeeklyStatistics(userId, query);

      case StatisticsViewType.MONTHLY:
        return this.getMonthlyStatistics(userId, query);

      default:
        // 默认返回概览统计
        return this.getOverviewStatistics(userId, query.baby_id);
    }
  }
}
