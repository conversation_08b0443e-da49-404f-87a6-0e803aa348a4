import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AdminJwtAuthGuard } from '../../../auth/guards/admin-jwt-auth.guard';
import { HabitCategoryService } from '../../services/habit-category.service';
import { CreateHabitCategoryDto } from '../../dto/habit-category/create-habit-category.dto';
import { UpdateHabitCategoryDto } from '../../dto/habit-category/update-habit-category.dto';
import { UpdateCategoryStatusDto } from '../../dto/habit-category/update-category-status.dto';
import { HabitCategoryResponseDto } from '../../dto/habit-category/habit-category-response.dto';
import { HabitCategoryQueryDto } from '../../dto/habit-category/habit-category-query.dto';
import { IHabitCategoryTree } from '../../interfaces/habit-category.interface';

@Controller('admin/habit-categories')
@UseGuards(AdminJwtAuthGuard)
export class AdminHabitCategoryController {
  constructor(private readonly habitCategoryService: HabitCategoryService) {}

  /**
   * 获取所有习惯分类
   */
  @Get()
  async findAll(
    @Query() query: HabitCategoryQueryDto,
  ): Promise<HabitCategoryResponseDto[]> {
    return this.habitCategoryService.findAll(query);
  }

  /**
   * 获取分类树形结构
   */
  @Get('tree')
  async findCategoryTree(
    @Query() query: HabitCategoryQueryDto,
  ): Promise<IHabitCategoryTree[]> {
    return this.habitCategoryService.findCategoryTree(query);
  }

  /**
   * 获取一级分类列表
   */
  @Get('top-level')
  async findTopLevelCategories(
    @Query('is_active') isActive?: boolean,
  ): Promise<HabitCategoryResponseDto[]> {
    return this.habitCategoryService.findTopLevelCategories(isActive);
  }

  /**
   * 获取子分类列表
   */
  @Get(':id/children')
  async findChildren(
    @Param('id') parentId: string,
  ): Promise<HabitCategoryResponseDto[]> {
    return this.habitCategoryService.findChildren(parentId);
  }

  /**
   * 根据ID获取习惯分类
   */
  @Get(':id')
  async findById(@Param('id') id: string): Promise<HabitCategoryResponseDto> {
    return this.habitCategoryService.findById(id);
  }

  /**
   * 创建习惯分类
   */
  @Post()
  async create(
    @Body() createDto: CreateHabitCategoryDto,
  ): Promise<HabitCategoryResponseDto> {
    return this.habitCategoryService.create(createDto);
  }

  /**
   * 更新习惯分类
   */
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateHabitCategoryDto,
  ): Promise<HabitCategoryResponseDto> {
    return this.habitCategoryService.update(id, updateDto);
  }

  /**
   * 删除习惯分类
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id') id: string): Promise<void> {
    return this.habitCategoryService.delete(id);
  }

  /**
   * 更新分类状态
   */
  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body() statusDto: UpdateCategoryStatusDto,
  ): Promise<HabitCategoryResponseDto> {
    return await this.habitCategoryService.updateStatus(id, statusDto.is_active);
  }

  /**
   * 批量创建分类
   */
  @Post('batch')
  async createBatch(
    @Body() categories: CreateHabitCategoryDto[],
  ): Promise<HabitCategoryResponseDto[]> {
    return this.habitCategoryService.createBatch(categories);
  }

  /**
   * 重新排序分类
   */
  @Post('reorder')
  @HttpCode(HttpStatus.NO_CONTENT)
  async reorderCategories(
    @Body() categoryOrders: { id: string; sort_order: number }[],
  ): Promise<void> {
    return this.habitCategoryService.reorderCategories(categoryOrders);
  }

  /**
   * 获取分类统计信息
   */
  @Get('stats')
  async getCategoryStats(): Promise<
    (HabitCategoryResponseDto & { template_count: number })[]
  > {
    return this.habitCategoryService.getCategoryStats();
  }
}
