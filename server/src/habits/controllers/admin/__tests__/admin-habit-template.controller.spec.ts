import { Test, TestingModule } from '@nestjs/testing';
import { AdminHabitTemplateController } from '../admin-habit-template.controller';
import { HabitTemplateService } from '../../../services/habit-template.service';
import { CreateHabitTemplateDto } from '../../../dto/habit-template/create-habit-template.dto';
import { UpdateHabitTemplateDto } from '../../../dto/habit-template/update-habit-template.dto';
import { HabitTemplateResponseDto } from '../../../dto/habit-template/habit-template-response.dto';
import { HabitTemplateQueryDto } from '../../../dto/habit-template/habit-template-query.dto';

describe('AdminHabitTemplateController', () => {
  let controller: AdminHabitTemplateController;
  let service: HabitTemplateService;

  const mockHabitTemplateService = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findByCategory: jest.fn(),
    createBatch: jest.fn(),
    activate: jest.fn(),
    deactivate: jest.fn(),
  };

  const mockTemplate: HabitTemplateResponseDto = {
    id: 'template-1',
    category_id: 'category-1',
    name: '睡前刷牙',
    icon: 'tooth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
    is_active: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminHabitTemplateController],
      providers: [
        {
          provide: HabitTemplateService,
          useValue: mockHabitTemplateService,
        },
      ],
    }).compile();

    controller = module.get<AdminHabitTemplateController>(
      AdminHabitTemplateController,
    );
    service = module.get<HabitTemplateService>(HabitTemplateService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all habit templates', async () => {
      const query: HabitTemplateQueryDto = {};
      const expectedResult = [mockTemplate];

      mockHabitTemplateService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(query);

      expect(result).toEqual(expectedResult);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });

    it('should return templates with query filters', async () => {
      const query: HabitTemplateQueryDto = {
        category_id: 'category-1',
        search: '刷牙',
        include_inactive: true,
        page: 1,
        limit: 10,
      };
      const expectedResult = [mockTemplate];

      mockHabitTemplateService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(query);

      expect(result).toEqual(expectedResult);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('findById', () => {
    it('should return a habit template by id', async () => {
      const id = 'template-1';

      mockHabitTemplateService.findById.mockResolvedValue(mockTemplate);

      const result = await controller.findById(id);

      expect(result).toEqual(mockTemplate);
      expect(service.findById).toHaveBeenCalledWith(id);
    });
  });

  describe('create', () => {
    it('should create a new habit template', async () => {
      const createDto: CreateHabitTemplateDto = {
        category_id: 'category-1',
        name: '睡前刷牙',
        icon: 'tooth-icon',
        theme_color: '#FF6B6B',
        description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        is_active: true,
      };

      mockHabitTemplateService.create.mockResolvedValue(mockTemplate);

      const result = await controller.create(createDto);

      expect(result).toEqual(mockTemplate);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });
  });

  describe('update', () => {
    it('should update a habit template', async () => {
      const id = 'template-1';
      const updateDto: UpdateHabitTemplateDto = {
        name: '睡前刷牙（更新）',
        description: '更新的描述',
      };
      const updatedTemplate = { ...mockTemplate, ...updateDto };

      mockHabitTemplateService.update.mockResolvedValue(updatedTemplate);

      const result = await controller.update(id, updateDto);

      expect(result).toEqual(updatedTemplate);
      expect(service.update).toHaveBeenCalledWith(id, updateDto);
    });
  });

  describe('delete', () => {
    it('should delete a habit template', async () => {
      const id = 'template-1';

      mockHabitTemplateService.delete.mockResolvedValue(undefined);

      const result = await controller.delete(id);

      expect(result).toBeUndefined();
      expect(service.delete).toHaveBeenCalledWith(id);
    });
  });

  describe('findByCategory', () => {
    it('should return templates by category', async () => {
      const categoryId = 'category-1';
      const expectedResult = [mockTemplate];

      mockHabitTemplateService.findByCategory.mockResolvedValue(expectedResult);

      const result = await controller.findByCategory(categoryId);

      expect(result).toEqual(expectedResult);
      expect(service.findByCategory).toHaveBeenCalledWith(
        categoryId,
        undefined,
      );
    });

    it('should return templates by category including inactive', async () => {
      const categoryId = 'category-1';
      const includeInactive = true;
      const expectedResult = [mockTemplate];

      mockHabitTemplateService.findByCategory.mockResolvedValue(expectedResult);

      const result = await controller.findByCategory(
        categoryId,
        includeInactive,
      );

      expect(result).toEqual(expectedResult);
      expect(service.findByCategory).toHaveBeenCalledWith(
        categoryId,
        includeInactive,
      );
    });
  });

  describe('createBatch', () => {
    it('should create multiple habit templates', async () => {
      const templates: CreateHabitTemplateDto[] = [
        {
          category_id: 'category-1',
          name: '睡前刷牙',
          icon: 'tooth-icon',
          theme_color: '#FF6B6B',
          description: '睡前刷干净牙齿',
        },
        {
          category_id: 'category-1',
          name: '饭前洗手',
          icon: 'hand-icon',
          theme_color: '#4ECDC4',
          description: '小手洗干净，病菌不来烦',
        },
      ];
      const expectedResult = [
        mockTemplate,
        { ...mockTemplate, id: 'template-2', name: '饭前洗手' },
      ];

      mockHabitTemplateService.createBatch.mockResolvedValue(expectedResult);

      const result = await controller.createBatch(templates);

      expect(result).toEqual(expectedResult);
      expect(service.createBatch).toHaveBeenCalledWith(templates);
    });
  });

  describe('activate', () => {
    it('should activate a habit template', async () => {
      const id = 'template-1';
      const activatedTemplate = { ...mockTemplate, is_active: true };

      mockHabitTemplateService.activate.mockResolvedValue(activatedTemplate);

      const result = await controller.activate(id);

      expect(result).toEqual(activatedTemplate);
      expect(service.activate).toHaveBeenCalledWith(id);
    });
  });

  describe('deactivate', () => {
    it('should deactivate a habit template', async () => {
      const id = 'template-1';
      const deactivatedTemplate = { ...mockTemplate, is_active: false };

      mockHabitTemplateService.deactivate.mockResolvedValue(
        deactivatedTemplate,
      );

      const result = await controller.deactivate(id);

      expect(result).toEqual(deactivatedTemplate);
      expect(service.deactivate).toHaveBeenCalledWith(id);
    });
  });
});
