/**
 * 习惯相关数据验证器
 */
export class HabitValidator {
  // 通用验证方法
  static validateUUID(id: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  static validateName(name: string): boolean {
    return (
      typeof name === 'string' && name.trim().length > 0 && name.length <= 100
    );
  }

  static validateIcon(icon: string): boolean {
    return (
      typeof icon === 'string' && icon.trim().length > 0 && icon.length <= 255
    );
  }

  static validateDescription(description?: string): boolean {
    if (!description) return true; // 可选字段
    return typeof description === 'string' && description.length <= 500;
  }

  static validateSortOrder(sortOrder: number): boolean {
    return typeof sortOrder === 'number' && sortOrder >= 0 && sortOrder <= 9999;
  }

  static validateThemeColor(themeColor: string): boolean {
    // 验证HEX颜色格式 #RRGGBB
    const hexColorRegex = /^#[0-9A-Fa-f]{6}$/;
    return hexColorRegex.test(themeColor);
  }

  static validateRewardStars(stars: number): boolean {
    return typeof stars === 'number' && stars >= 1 && stars <= 5;
  }

  static validateFrequency(frequency: number[]): boolean {
    if (!Array.isArray(frequency) || frequency.length === 0) return false;

    // 检查数组中的每个值都是1-7之间的整数（周一到周日）
    return (
      frequency.every(
        (day) =>
          typeof day === 'number' &&
          day >= 1 &&
          day <= 7 &&
          Number.isInteger(day),
      ) &&
      // 检查没有重复值
      new Set(frequency).size === frequency.length
    );
  }

  static validatePreferredTime(time?: string): boolean {
    if (!time) return true; // 可选字段

    // 验证HH:mm格式
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  static validateStatus(status: string): boolean {
    return status === 'active' || status === 'archived';
  }

  // 习惯分类验证
  static validateHabitCategoryData(data: {
    name: string;
    icon: string;
    sort_order?: number;
    is_active?: boolean;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateName(data.name)) {
      errors.push('Category name must be between 1 and 100 characters');
    }

    if (!this.validateIcon(data.icon)) {
      errors.push('Category icon must be between 1 and 255 characters');
    }

    if (
      data.sort_order !== undefined &&
      !this.validateSortOrder(data.sort_order)
    ) {
      errors.push('Sort order must be between 0 and 9999');
    }

    if (data.is_active !== undefined && typeof data.is_active !== 'boolean') {
      errors.push('is_active must be a boolean value');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // 习惯模板验证
  static validateHabitTemplateData(data: {
    category_id: string;
    name: string;
    icon: string;
    theme_color: string;
    description?: string;
    is_active?: boolean;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateUUID(data.category_id)) {
      errors.push('Invalid category_id format');
    }

    if (!this.validateName(data.name)) {
      errors.push('Template name must be between 1 and 100 characters');
    }

    if (!this.validateIcon(data.icon)) {
      errors.push('Template icon must be between 1 and 255 characters');
    }

    if (!this.validateThemeColor(data.theme_color)) {
      errors.push('Theme color must be in HEX format (#RRGGBB)');
    }

    if (!this.validateDescription(data.description)) {
      errors.push('Description must be less than 500 characters');
    }

    if (data.is_active !== undefined && typeof data.is_active !== 'boolean') {
      errors.push('is_active must be a boolean value');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // 用户习惯验证
  static validateUserHabitData(data: {
    user_id: string;
    baby_id: string;
    template_id?: string;
    name: string;
    icon: string;
    theme_color: string;
    description?: string;
    frequency: number[];
    preferred_time?: string;
    reward_stars: number;
    status: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateUUID(data.user_id)) {
      errors.push('Invalid user_id format');
    }

    if (!this.validateUUID(data.baby_id)) {
      errors.push('Invalid baby_id format');
    }

    if (
      data.template_id !== undefined &&
      data.template_id !== null &&
      !this.validateUUID(data.template_id)
    ) {
      errors.push('Invalid template_id format');
    }

    if (!this.validateName(data.name)) {
      errors.push('Habit name must be between 1 and 100 characters');
    }

    if (!this.validateIcon(data.icon)) {
      errors.push('Habit icon must be between 1 and 255 characters');
    }

    if (!this.validateThemeColor(data.theme_color)) {
      errors.push('Theme color must be in HEX format (#RRGGBB)');
    }

    if (!this.validateDescription(data.description)) {
      errors.push('Description must be less than 500 characters');
    }

    if (!this.validateFrequency(data.frequency)) {
      errors.push('Frequency must be an array of unique weekday numbers (1-7)');
    }

    if (!this.validatePreferredTime(data.preferred_time)) {
      errors.push('Preferred time must be in HH:mm format');
    }

    if (!this.validateRewardStars(data.reward_stars)) {
      errors.push('Reward stars must be between 1 and 5');
    }

    if (!this.validateStatus(data.status)) {
      errors.push('Status must be either "active" or "archived"');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // 打卡记录验证
  static validateHabitCheckInData(data: {
    habit_id: string;
    user_id: string;
    baby_id: string;
    check_in_date: Date;
    check_in_time: Date;
    notes?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateUUID(data.habit_id)) {
      errors.push('Invalid habit_id format');
    }

    if (!this.validateUUID(data.user_id)) {
      errors.push('Invalid user_id format');
    }

    if (!this.validateUUID(data.baby_id)) {
      errors.push('Invalid baby_id format');
    }

    if (
      !(data.check_in_date instanceof Date) ||
      isNaN(data.check_in_date.getTime())
    ) {
      errors.push('Invalid check_in_date');
    }

    if (
      !(data.check_in_time instanceof Date) ||
      isNaN(data.check_in_time.getTime())
    ) {
      errors.push('Invalid check_in_time');
    }

    // 检查打卡日期不能是未来日期
    const today = new Date();
    today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
    if (data.check_in_date > today) {
      errors.push('Check-in date cannot be in the future');
    }

    // 检查打卡时间不能是未来时间
    if (data.check_in_time > new Date()) {
      errors.push('Check-in time cannot be in the future');
    }

    if (data.notes && typeof data.notes !== 'string') {
      errors.push('Notes must be a string');
    }

    if (data.notes && data.notes.length > 500) {
      errors.push('Notes must be less than 500 characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
