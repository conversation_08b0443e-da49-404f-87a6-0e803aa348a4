import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

/**
 * Custom validator for habit frequency array
 * Ensures frequency contains valid weekday numbers (1-7) and no duplicates
 */
@ValidatorConstraint({ name: 'isValidFrequency', async: false })
export class IsValidFrequencyConstraint
  implements ValidatorConstraintInterface
{
  validate(frequency: number[], args: ValidationArguments) {
    if (!Array.isArray(frequency)) {
      return false;
    }

    // Check if all values are valid weekdays (1-7)
    const validDays = frequency.every(
      (day) => Number.isInteger(day) && day >= 1 && day <= 7,
    );
    if (!validDays) {
      return false;
    }

    // Check for duplicates
    const uniqueDays = new Set(frequency);
    return uniqueDays.size === frequency.length;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Frequency must contain unique weekday numbers between 1 and 7';
  }
}

/**
 * Custom validator for time format (HH:mm)
 */
@ValidatorConstraint({ name: 'isValidTimeFormat', async: false })
export class IsValidTimeFormatConstraint
  implements ValidatorConstraintInterface
{
  validate(time: string, args: ValidationArguments) {
    if (typeof time !== 'string') {
      return false;
    }

    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Time must be in HH:mm format (e.g., 08:30, 14:45)';
  }
}

/**
 * Custom validator for hex color format
 */
@ValidatorConstraint({ name: 'isValidHexColor', async: false })
export class IsValidHexColorConstraint implements ValidatorConstraintInterface {
  validate(color: string, args: ValidationArguments) {
    if (typeof color !== 'string') {
      return false;
    }

    const hexColorRegex = /^#[0-9A-Fa-f]{6}$/;
    return hexColorRegex.test(color);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Color must be a valid 6-digit hex color (e.g., #FF0000, #00FF00)';
  }
}

/**
 * Custom validator for date range validation
 */
@ValidatorConstraint({ name: 'isValidDateRange', async: false })
export class IsValidDateRangeConstraint
  implements ValidatorConstraintInterface
{
  validate(endDate: string, args: ValidationArguments) {
    const startDate = (args.object as any).start_date;

    if (!startDate || !endDate) {
      return true; // Let other validators handle required validation
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    return start <= end;
  }

  defaultMessage(args: ValidationArguments) {
    return 'End date must be after or equal to start date';
  }
}

/**
 * Custom validator for reward stars range
 */
@ValidatorConstraint({ name: 'isValidRewardStars', async: false })
export class IsValidRewardStarsConstraint
  implements ValidatorConstraintInterface
{
  validate(stars: number, args: ValidationArguments) {
    return Number.isInteger(stars) && stars >= 1 && stars <= 5;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Reward stars must be an integer between 1 and 5';
  }
}
