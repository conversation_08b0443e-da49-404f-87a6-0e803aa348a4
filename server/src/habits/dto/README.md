# Habits Module DTOs

This directory contains all Data Transfer Objects (DTOs) for the habits module, organized by functionality.

## Structure

```
dto/
├── common/              # Common DTOs used across the module
│   ├── pagination.dto.ts
│   └── uuid-param.dto.ts
├── habit-category/      # Habit category management DTOs
│   ├── create-habit-category.dto.ts
│   ├── update-habit-category.dto.ts
│   ├── habit-category-response.dto.ts
│   └── habit-category-query.dto.ts
├── habit-template/      # Habit template management DTOs
│   ├── create-habit-template.dto.ts
│   ├── update-habit-template.dto.ts
│   ├── habit-template-response.dto.ts
│   └── habit-template-query.dto.ts
├── user-habit/          # User habit management DTOs
│   ├── create-user-habit.dto.ts
│   ├── update-user-habit.dto.ts
│   ├── user-habit-response.dto.ts
│   └── user-habit-query.dto.ts
├── habit-check-in/      # Habit check-in DTOs
│   ├── create-habit-check-in.dto.ts
│   ├── habit-check-in-response.dto.ts
│   └── habit-check-in-query.dto.ts
├── statistics/          # Statistics and analytics DTOs
│   ├── statistics-query.dto.ts
│   └── statistics-response.dto.ts
├── library/             # Habit library browsing DTOs
│   └── habit-library-response.dto.ts
└── transformers/        # Data transformation utilities
    └── habit-data.transformer.ts
```

## Validation Features

### Built-in Validations

All DTOs include comprehensive validation using class-validator decorators:

- **Required Fields**: `@IsNotEmpty()`, `@IsString()`, `@IsUUID()`
- **Data Types**: `@IsInt()`, `@IsBoolean()`, `@IsArray()`, `@IsEnum()`
- **Constraints**: `@Min()`, `@Max()`, `@MaxLength()`, `@ArrayMinSize()`, `@ArrayMaxSize()`
- **Format Validation**: `@IsDateString()`, `@Matches()` for regex patterns

### Custom Validators

The module includes custom validators for domain-specific validation:

- **IsValidFrequencyConstraint**: Validates habit frequency arrays (1-7, no duplicates)
- **IsValidTimeFormatConstraint**: Validates time format (HH:mm)
- **IsValidHexColorConstraint**: Validates hex color format (#RRGGBB)
- **IsValidRewardStarsConstraint**: Validates reward stars (1-5)
- **IsValidDateRangeConstraint**: Validates date ranges

### Data Transformers

Custom transformers automatically clean and format input data:

- **TransformSanitizeText**: Trims whitespace and normalizes spaces
- **TransformStringToBoolean**: Converts string query params to boolean
- **TransformStringToInt**: Converts string query params to integers
- **TransformFrequencyArray**: Removes duplicates and sorts frequency arrays
- **TransformTimeString**: Ensures proper time format (HH:mm)
- **TransformHexColor**: Ensures proper hex color format (#RRGGBB)

## Usage Examples

### Creating a User Habit

```typescript
import { CreateUserHabitDto } from './dto';

const createHabitDto: CreateUserHabitDto = {
  baby_id: '123e4567-e89b-12d3-a456-426614174000',
  name: 'Brush Teeth',
  icon: 'tooth-icon',
  theme_color: '#4CAF50',
  description: 'Brush teeth before bedtime',
  frequency: [1, 2, 3, 4, 5], // Monday to Friday
  preferred_time: '20:00',
  reward_stars: 3,
  status: UserHabitStatus.ACTIVE
};
```

### Querying Habits

```typescript
import { UserHabitQueryDto } from './dto';

const queryDto: UserHabitQueryDto = {
  baby_id: '123e4567-e89b-12d3-a456-426614174000',
  status: UserHabitStatus.ACTIVE,
  limit: 10,
  offset: 0
};
```

### Check-in Creation

```typescript
import { CreateHabitCheckInDto } from './dto';

const checkInDto: CreateHabitCheckInDto = {
  habit_id: '123e4567-e89b-12d3-a456-426614174000',
  baby_id: '123e4567-e89b-12d3-a456-426614174001',
  check_in_date: '2024-01-01',
  notes: 'Great job today!'
};
```

## Validation Pipeline

The DTOs work with the custom validation pipeline:

1. **Input Transformation**: Raw input is transformed using class-transformer
2. **Validation**: Data is validated using class-validator decorators
3. **Custom Validation**: Domain-specific validators are applied
4. **Error Handling**: Validation errors are formatted and returned as BadRequestException

## Response DTOs

Response DTOs define the structure of API responses and include:

- **Core Entity Data**: All entity properties
- **Computed Fields**: Calculated values like completion rates
- **Related Data**: Optional joined data from related entities
- **Metadata**: Additional context like timestamps and counts

## Testing

All DTOs include comprehensive unit tests covering:

- Valid input scenarios
- Invalid input validation
- Edge cases and boundary conditions
- Transformation behavior
- Custom validator functionality

Run DTO tests with:
```bash
yarn test src/habits/dto/__tests__
```