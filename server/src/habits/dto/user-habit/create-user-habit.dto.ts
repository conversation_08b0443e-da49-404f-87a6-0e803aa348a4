import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsArray,
  IsInt,
  Min,
  <PERSON>,
  <PERSON>Length,
  Matches,
  IsEnum,
  ArrayMinSize,
  ArrayMaxSize,
  IsUUID,
  Validate,
} from 'class-validator';
import { UserHabitStatus } from '../../interfaces';
import {
  TransformSanitizeText,
  TransformFrequencyArray,
  TransformTimeString,
  TransformHexColor,
} from '../transformers';
import {
  IsValidFrequencyConstraint,
  IsValidTimeFormatConstraint,
  IsValidHexColorConstraint,
  IsValidRewardStarsConstraint,
} from '../../validators';

export class CreateUserHabitDto {
  @IsUUID()
  @IsOptional()
  baby_id?: string;

  @IsUUID()
  @IsOptional()
  template_id?: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @TransformSanitizeText()
  name: string;

  @IsString()
  @IsNotEmpty()
  icon: string;

  @IsString()
  @IsNotEmpty()
  @Validate(IsValidHexColorConstraint)
  @TransformHexColor()
  theme_color: string;

  @IsString()
  @IsOptional()
  @TransformSanitizeText()
  description?: string;

  @IsArray()
  @ArrayMinSize(1, { message: 'At least one day must be selected' })
  @ArrayMaxSize(7, { message: 'Maximum 7 days can be selected' })
  @Validate(IsValidFrequencyConstraint)
  @TransformFrequencyArray()
  frequency: number[];

  @IsString()
  @IsOptional()
  @Validate(IsValidTimeFormatConstraint)
  @TransformTimeString()
  preferred_time?: string;

  @IsInt()
  @Validate(IsValidRewardStarsConstraint)
  @IsOptional()
  reward_stars?: number = 1;

  @IsEnum(UserHabitStatus)
  @IsOptional()
  status?: UserHabitStatus = UserHabitStatus.ACTIVE;
}
