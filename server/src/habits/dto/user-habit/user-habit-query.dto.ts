import { IsOptional, IsU<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';
import { UserHabitStatus } from '../../interfaces';

export class UserHabitQueryDto {
  @IsUUID()
  @IsOptional()
  baby_id?: string;

  @IsUUID()
  @IsOptional()
  template_id?: string;

  @IsEnum(UserHabitStatus)
  @IsOptional()
  status?: UserHabitStatus;

  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;

  @IsInt()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  offset?: number = 0;
}
