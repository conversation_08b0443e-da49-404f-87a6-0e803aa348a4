import { UserHabitStatus } from '../../interfaces';

export class UserHabitResponseDto {
  id: string;
  user_id: string;
  baby_id: string;
  template_id?: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  reward_stars: number;
  status: UserHabitStatus;
  created_at: Date;
  updated_at: Date;

  // Optional template information when joined
  template?: {
    id: string;
    name: string;
    icon: string;
    category_name: string;
  };

  // Optional baby information when joined
  baby?: {
    id: string;
    nickname: string;
    avatar?: string;
  };

  // Optional today's check-in status
  today_checked_in?: boolean;
  today_check_in_time?: Date;
}
