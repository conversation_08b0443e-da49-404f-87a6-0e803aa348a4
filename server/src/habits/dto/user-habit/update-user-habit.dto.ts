import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>y,
  <PERSON><PERSON>nt,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  IsEnum,
  ArrayMinSize,
  ArrayMaxSize,
} from 'class-validator';
import { UserHabitStatus } from '../../interfaces';

export class UpdateUserHabitDto {
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @IsString()
  @IsOptional()
  icon?: string;

  @IsString()
  @IsOptional()
  @Matches(/^#[0-9A-Fa-f]{6}$/, {
    message: 'Theme color must be a valid HEX color (e.g., #FF0000)',
  })
  theme_color?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsArray()
  @ArrayMinSize(1, { message: 'At least one day must be selected' })
  @ArrayMaxSize(7, { message: 'Maximum 7 days can be selected' })
  @IsInt({ each: true })
  @Min(1, { each: true })
  @Max(7, { each: true })
  @IsOptional()
  frequency?: number[];

  @IsString()
  @IsOptional()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Preferred time must be in HH:mm format',
  })
  preferred_time?: string;

  @IsInt()
  @Min(1)
  @Max(5)
  @IsOptional()
  reward_stars?: number;

  @IsEnum(UserHabitStatus)
  @IsOptional()
  status?: UserHabitStatus;
}
