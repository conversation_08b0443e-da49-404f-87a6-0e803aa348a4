import { Transform } from 'class-transformer';

/**
 * Transform string to boolean for query parameters
 */
export const TransformStringToBoolean = () =>
  Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  });

/**
 * Transform string to integer for query parameters
 */
export const TransformStringToInt = () =>
  Transform(({ value }) => {
    const parsed = parseInt(value);
    return isNaN(parsed) ? value : parsed;
  });

/**
 * Transform string to number for query parameters
 */
export const TransformStringToNumber = () =>
  Transform(({ value }) => {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? value : parsed;
  });

/**
 * Transform comma-separated string to number array
 */
export const TransformStringToNumberArray = () =>
  Transform(({ value }) => {
    if (typeof value === 'string') {
      return value
        .split(',')
        .map((v) => parseInt(v.trim()))
        .filter((v) => !isNaN(v));
    }
    return value;
  });

/**
 * Transform and validate frequency array
 */
export const TransformFrequencyArray = () =>
  Transform(({ value }) => {
    if (Array.isArray(value)) {
      // Remove duplicates and sort
      const uniqueFrequency = [...new Set(value)].sort((a, b) => a - b);
      return uniqueFrequency;
    }
    return value;
  });

/**
 * Transform time string to ensure proper format
 */
export const TransformTimeString = () =>
  Transform(({ value }) => {
    if (typeof value === 'string') {
      // Ensure time is in HH:mm format
      const timeParts = value.split(':');
      if (timeParts.length === 2) {
        const hours = timeParts[0].padStart(2, '0');
        const minutes = timeParts[1].padStart(2, '0');
        return `${hours}:${minutes}`;
      }
    }
    return value;
  });

/**
 * Transform hex color to ensure proper format
 */
export const TransformHexColor = () =>
  Transform(({ value }) => {
    if (typeof value === 'string') {
      // Ensure color starts with # and is uppercase
      let color = value.trim();
      if (!color.startsWith('#')) {
        color = '#' + color;
      }
      return color.toUpperCase();
    }
    return value;
  });

/**
 * Transform and sanitize text input
 */
export const TransformSanitizeText = () =>
  Transform(({ value }) => {
    if (typeof value === 'string') {
      // Trim whitespace and remove extra spaces
      return value.trim().replace(/\s+/g, ' ');
    }
    return value;
  });
