import {
  IsString,
  IsOptional,
  IsUUID,
  IsDateString,
  Max<PERSON>ength,
} from 'class-validator';
import { TransformSanitizeText } from '../transformers';

export class CreateHabitCheckInDto {
  @IsUUID()
  habit_id: string;

  @IsUUID()
  @IsOptional()
  baby_id: string;

  @IsDateString()
  @IsOptional()
  check_in_date?: string; // ISO date string, defaults to today

  @IsString()
  @IsOptional()
  @MaxLength(500)
  @TransformSanitizeText()
  notes?: string;
}
