import {
  IsOptional,
  IsUUID,
  <PERSON>Date<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class HabitCheckInQueryDto {
  @IsUUID()
  @IsOptional()
  habit_id?: string;

  @IsUUID()
  @IsOptional()
  baby_id?: string;

  @IsDateString()
  @IsOptional()
  start_date?: string;

  @IsDateString()
  @IsOptional()
  end_date?: string;

  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;

  @IsInt()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  offset?: number = 0;
}
