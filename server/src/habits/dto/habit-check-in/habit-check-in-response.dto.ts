export class HabitCheckInResponseDto {
  id: string;
  habit_id: string;
  user_id: string;
  baby_id: string;
  check_in_date: Date;
  check_in_time: Date;
  notes?: string;
  created_at: Date;

  // Optional habit information when joined
  habit?: {
    id: string;
    name: string;
    icon: string;
    theme_color: string;
    reward_stars: number;
  };

  // Optional baby information when joined
  baby?: {
    id: string;
    nickname: string;
    avatar?: string;
  };
}
