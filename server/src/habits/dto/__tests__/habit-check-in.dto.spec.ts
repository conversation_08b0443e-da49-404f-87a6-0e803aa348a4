import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CreateHabitCheckInDto, HabitCheckInQueryDto } from '../habit-check-in';

describe('HabitCheckIn DTOs', () => {
  describe('CreateHabitCheckInDto', () => {
    it('should validate valid data', async () => {
      const dto = plainToClass(CreateHabitCheckInDto, {
        habit_id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-************',
        check_in_date: '2024-01-01',
        notes: 'Great job today!',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate without optional fields', async () => {
      const dto = plainToClass(CreateHabitCheckInDto, {
        habit_id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation for invalid UUID', async () => {
      const dto = plainToClass(CreateHabitCheckInDto, {
        habit_id: 'invalid-uuid',
        baby_id: '123e4567-e89b-12d3-a456-************',
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'habit_id')).toBe(true);
    });

    it('should fail validation for invalid date', async () => {
      const dto = plainToClass(CreateHabitCheckInDto, {
        habit_id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-************',
        check_in_date: 'invalid-date',
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'check_in_date')).toBe(
        true,
      );
    });
  });

  describe('HabitCheckInQueryDto', () => {
    it('should validate valid query parameters', async () => {
      const dto = plainToClass(HabitCheckInQueryDto, {
        habit_id: '123e4567-e89b-12d3-a456-************',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        limit: '10',
        offset: '0',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.limit).toBe(10);
      expect(dto.offset).toBe(0);
    });

    it('should validate empty query', async () => {
      const dto = plainToClass(HabitCheckInQueryDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
