import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CreateUserHabitDto, UpdateUserHabitDto } from '../user-habit';
import { UserHabitStatus } from '../../interfaces';

describe('UserHabit DTOs', () => {
  describe('CreateUserHabitDto', () => {
    it('should validate valid data', async () => {
      const dto = plainToClass(CreateUserHabitDto, {
        baby_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Habit',
        icon: 'test-icon',
        theme_color: '#FF0000',
        description: 'Test description',
        frequency: [1, 2, 3, 4, 5],
        preferred_time: '08:30',
        reward_stars: 3,
        status: UserHabitStatus.ACTIVE,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation for invalid theme_color', async () => {
      const dto = plainToClass(CreateUserHabitDto, {
        baby_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Habit',
        icon: 'test-icon',
        theme_color: 'invalid-color',
        frequency: [1, 2, 3],
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'theme_color')).toBe(
        true,
      );
    });

    it('should fail validation for invalid frequency', async () => {
      const dto = plainToClass(CreateUserHabitDto, {
        baby_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Habit',
        icon: 'test-icon',
        theme_color: '#FF0000',
        frequency: [8, 9], // Invalid days
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'frequency')).toBe(true);
    });

    it('should fail validation for invalid preferred_time', async () => {
      const dto = plainToClass(CreateUserHabitDto, {
        baby_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Habit',
        icon: 'test-icon',
        theme_color: '#FF0000',
        frequency: [1, 2, 3],
        preferred_time: '25:00', // Invalid time
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'preferred_time')).toBe(
        true,
      );
    });

    it('should fail validation for invalid reward_stars', async () => {
      const dto = plainToClass(CreateUserHabitDto, {
        baby_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Habit',
        icon: 'test-icon',
        theme_color: '#FF0000',
        frequency: [1, 2, 3],
        reward_stars: 6, // Invalid stars count
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'reward_stars')).toBe(
        true,
      );
    });

    it('should validate when baby_id is not provided (optional)', async () => {
      const dto = plainToClass(CreateUserHabitDto, {
        name: 'Test Habit',
        icon: 'test-icon',
        theme_color: '#FF0000',
        description: 'Test description',
        frequency: [1, 2, 3, 4, 5],
        preferred_time: '08:30',
        reward_stars: 3,
        status: UserHabitStatus.ACTIVE,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.baby_id).toBeUndefined();
    });
  });

  describe('UpdateUserHabitDto', () => {
    it('should validate valid partial data', async () => {
      const dto = plainToClass(UpdateUserHabitDto, {
        name: 'Updated Habit',
        theme_color: '#00FF00',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object', async () => {
      const dto = plainToClass(UpdateUserHabitDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
