import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  CreateHabitCategoryDto,
  UpdateHabitCategoryDto,
  HabitCategoryQueryDto,
} from '../habit-category';

describe('HabitCategory DTOs', () => {
  describe('CreateHabitCategoryDto', () => {
    it('should validate valid data', async () => {
      const dto = plainToClass(CreateHabitCategoryDto, {
        name: 'Test Category',
        icon: 'test-icon',
        sort_order: 1,
        is_active: true,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation for missing required fields', async () => {
      const dto = plainToClass(CreateHabitCategoryDto, {});

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some((error) => error.property === 'name')).toBe(true);
      expect(errors.some((error) => error.property === 'icon')).toBe(true);
    });

    it('should fail validation for invalid sort_order', async () => {
      const dto = plainToClass(CreateHabitCategoryDto, {
        name: 'Test Category',
        icon: 'test-icon',
        sort_order: -1,
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'sort_order')).toBe(
        true,
      );
    });
  });

  describe('UpdateHabitCategoryDto', () => {
    it('should validate valid partial data', async () => {
      const dto = plainToClass(UpdateHabitCategoryDto, {
        name: 'Updated Category',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object', async () => {
      const dto = plainToClass(UpdateHabitCategoryDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('HabitCategoryQueryDto', () => {
    it('should validate valid query parameters', async () => {
      const dto = plainToClass(HabitCategoryQueryDto, {
        is_active: 'true',
        limit: '10',
        offset: '0',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.is_active).toBe(true);
      expect(dto.limit).toBe(10);
      expect(dto.offset).toBe(0);
    });

    it('should fail validation for invalid limit', async () => {
      const dto = plainToClass(HabitCategoryQueryDto, {
        limit: '200',
      });

      const errors = await validate(dto);
      expect(errors.some((error) => error.property === 'limit')).toBe(true);
    });
  });
});
