import {
  IsOptional,
  IsBoolean,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>UI<PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class HabitCategoryQueryDto {
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  is_active?: boolean;

  @IsInt()
  @Min(1)
  @Max(2)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  level?: number;

  @IsUUID()
  @IsOptional()
  parent_id?: string;

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  include_children?: boolean;

  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  pageSize?: number = 20;

  @IsInt()
  @Min(1)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  current?: number = 1;
}
