import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  Min,
  <PERSON>,
  <PERSON><PERSON>ength,
  <PERSON>UUID,
} from 'class-validator';
import { TransformSanitizeText } from '../transformers';

export class CreateHabitCategoryDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @TransformSanitizeText()
  name: string;

  @IsString()
  @IsNotEmpty()
  icon: string;

  @IsUUID()
  @IsOptional()
  parent_id?: string;

  @IsInt()
  @Min(1)
  @Max(2)
  level: number;

  @IsInt()
  @Min(0)
  @IsOptional()
  sort_order?: number;

  @IsBoolean()
  @IsOptional()
  is_active?: boolean;
}
