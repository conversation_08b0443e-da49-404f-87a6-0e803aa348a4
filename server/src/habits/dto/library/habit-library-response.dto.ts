export class HabitLibraryCategoryDto {
  id: string;
  name: string;
  icon: string;
  parent_id?: string;
  level?: number;
  sort_order: number;
  templates: HabitLibraryTemplateDto[];
}

export class HabitLibraryTemplateDto {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
}

export class HabitLibraryResponseDto {
  categories: HabitLibraryCategoryDto[];
}

import { ApiProperty } from '@nestjs/swagger';

// 新增：树形结构的接口
export interface HabitTemplateInfoDto {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
}

export interface HabitSubCategoryTreeDto {
  id: string;
  name: string;
  icon: string;
  level: number;
  sort_order: number;
  templates: HabitTemplateInfoDto[];
}

export interface HabitCategoryTreeDto {
  id: string;
  name: string;
  icon: string;
  level: number;
  sort_order: number;
  children: HabitSubCategoryTreeDto[];
}

// 用于 Swagger 文档的类
export class HabitTemplateInfoSwaggerDto {
  @ApiProperty({ description: '模板ID' })
  id: string;

  @ApiProperty({ description: '模板名称' })
  name: string;

  @ApiProperty({ description: '模板图标URL' })
  icon: string;

  @ApiProperty({ description: '主题色（HEX格式）' })
  theme_color: string;

  @ApiProperty({ description: '模板描述' })
  description: string;
}

export class HabitSubCategoryTreeSwaggerDto {
  @ApiProperty({ description: '子分类ID' })
  id: string;

  @ApiProperty({ description: '子分类名称' })
  name: string;

  @ApiProperty({ description: '子分类图标URL' })
  icon: string;

  @ApiProperty({ description: '分类层级（固定为2）' })
  level: number;

  @ApiProperty({ description: '排序顺序' })
  sort_order: number;

  @ApiProperty({
    description: '该分类下的习惯模板列表',
    type: [HabitTemplateInfoSwaggerDto]
  })
  templates: HabitTemplateInfoSwaggerDto[];
}

export class HabitCategoryTreeSwaggerDto {
  @ApiProperty({ description: '分类ID' })
  id: string;

  @ApiProperty({ description: '分类名称' })
  name: string;

  @ApiProperty({ description: '分类图标URL' })
  icon: string;

  @ApiProperty({ description: '分类层级（固定为1）' })
  level: number;

  @ApiProperty({ description: '排序顺序' })
  sort_order: number;

  @ApiProperty({
    description: '二级分类列表',
    type: [HabitSubCategoryTreeSwaggerDto]
  })
  children: HabitSubCategoryTreeSwaggerDto[];
}


