export class HabitLibraryCategoryDto {
  id: string;
  name: string;
  icon: string;
  parent_id?: string;
  level?: number;
  sort_order: number;
  templates: HabitLibraryTemplateDto[];
}

export class HabitLibraryTemplateDto {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
}

export class HabitLibraryResponseDto {
  categories: HabitLibraryCategoryDto[];
}
