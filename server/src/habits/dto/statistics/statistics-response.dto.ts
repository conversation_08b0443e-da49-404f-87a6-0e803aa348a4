export class HabitCompletionDto {
  habit_id: string;
  habit_name: string;
  habit_icon: string;
  theme_color: string;
  is_completed: boolean;
  check_in_time?: Date;
}

export class DailyStatisticsResponseDto {
  date: Date;
  habits: HabitCompletionDto[];
  completion_rate: number;
}

export class DailyCompletionDto {
  date: Date;
  completion_rate: number;
  completed_count: number;
  total_count: number;
}

export class WeeklyStatisticsResponseDto {
  week_start: Date;
  week_end: Date;
  daily_completion: DailyCompletionDto[];
  overall_completion_rate: number;
}

export class MonthlyStatisticsResponseDto {
  year: number;
  month: number;
  calendar_data: DailyCompletionDto[];
  overall_completion_rate: number;
}

export class TimelineHabitDto {
  habit_id: string;
  habit_name: string;
  habit_icon: string;
  theme_color: string;
  is_completed: boolean;
  check_in_time?: Date;
  preferred_time?: string;
}

export class OverviewStatisticsResponseDto {
  today_completion_rate: number;
  today_completed_count: number;
  today_total_count: number;
  today_timeline: TimelineHabitDto[];
}

export class HabitDetailCheckInDto {
  date: Date;
  is_completed: boolean;
  check_in_time?: Date;
  notes?: string;
}

export class HabitDetailStatisticsResponseDto {
  habit_id: string;
  current_month_data: HabitDetailCheckInDto[];
  completion_rate: number;
  streak_days: number;
  total_check_ins: number;
}
