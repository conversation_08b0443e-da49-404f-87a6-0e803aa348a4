import {
  IsOptional,
  IsUUID,
  <PERSON>DateString,
  IsEnum,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { StatisticsViewType } from '../../interfaces';

export class StatisticsQueryDto {
  @IsUUID()
  @IsOptional()
  baby_id?: string;

  @IsEnum(StatisticsViewType)
  @IsOptional()
  view_type?: StatisticsViewType = StatisticsViewType.DAILY;

  @IsDateString()
  @IsOptional()
  date?: string; // For daily view

  @IsDateString()
  @IsOptional()
  week_start?: string; // For weekly view

  @IsInt()
  @Min(1)
  @Max(12)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  month?: number; // For monthly view

  @IsInt()
  @Min(2020)
  @Max(2030)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  year?: number; // For monthly view
}

export class HabitDetailStatisticsQueryDto {
  @IsUUID()
  habit_id: string;

  @IsInt()
  @Min(1)
  @Max(12)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  month?: number;

  @IsInt()
  @Min(2020)
  @Max(2030)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  year?: number;
}
