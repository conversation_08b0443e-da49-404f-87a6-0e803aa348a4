import { Injectable, HttpStatus } from '@nestjs/common';
import { HabitCheckInRepository } from '../repositories/habit-checkin.repository';
import { UserHabitRepository } from '../repositories/user-habit.repository';
import { BabyRepository } from '../../baby/baby.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import {
  IHabitCheckIn,
  ICreateHabitCheckIn,
} from '../interfaces/habit-check-in.interface';
import { CreateHabitCheckInDto } from '../dto/habit-check-in/create-habit-check-in.dto';
import { HabitCheckInResponseDto } from '../dto/habit-check-in/habit-check-in-response.dto';
import { HabitCheckInQueryDto } from '../dto/habit-check-in/habit-check-in-query.dto';

@Injectable()
export class HabitCheckInService {
  constructor(
    private readonly habitCheckInRepository: HabitCheckInRepository,
    private readonly userHabitRepository: UserHabitRepository,
    private readonly babyRepository: BabyRepository,
  ) {}

  /**
   * 执行打卡
   */
  async checkIn(
    userId: string,
    createDto: CreateHabitCheckInDto,
  ): Promise<HabitCheckInResponseDto> {
    // 验证习惯是否存在且用户有权限
    const habit = await this.validateHabitAccess(createDto.habit_id, userId);

    // 验证用户是否有权限访问该宝宝
    await this.validateBabyAccess(createDto.baby_id, userId);

    // 验证习惯是否属于该宝宝
    if (habit.baby_id !== createDto.baby_id) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_ACCESS_DENIED,
        '习惯不属于指定的宝宝',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 解析打卡日期
    const checkInDate = createDto.check_in_date
      ? new Date(createDto.check_in_date)
      : new Date();

    // 验证打卡日期
    await this.validateCheckInDate(checkInDate, habit);

    // 检查是否已经打卡
    const hasCheckedIn = await this.habitCheckInRepository.hasCheckedInToday(
      createDto.habit_id,
      userId,
      createDto.baby_id,
    );

    if (hasCheckedIn) {
      throw new BusinessException(
        ErrorCode.HABIT_ALREADY_CHECKED_IN,
        '今日已完成打卡',
        HttpStatus.CONFLICT,
      );
    }

    // 创建打卡记录
    const createData: ICreateHabitCheckIn = {
      habit_id: createDto.habit_id,
      baby_id: createDto.baby_id,
      check_in_date: checkInDate,
      notes: createDto.notes,
    };

    const checkIn = await this.habitCheckInRepository.createCheckIn(
      userId,
      createData,
    );
    return this.mapToResponseDto(checkIn);
  }

  /**
   * 获取习惯的打卡记录列表
   */
  async findCheckIns(
    habitId: string,
    userId: string,
    query: HabitCheckInQueryDto,
  ): Promise<HabitCheckInResponseDto[]> {
    // 验证习惯是否存在且用户有权限
    const habit = await this.validateHabitAccess(habitId, userId);

    const { baby_id, start_date, end_date, limit = 50, offset = 0 } = query;

    // 如果指定了宝宝ID，验证权限
    if (baby_id) {
      await this.validateBabyAccess(baby_id, userId);

      // 验证习惯是否属于该宝宝
      if (habit.baby_id !== baby_id) {
        throw new BusinessException(
          ErrorCode.USER_HABIT_ACCESS_DENIED,
          '习惯不属于指定的宝宝',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    let checkIns: IHabitCheckIn[];

    if (start_date && end_date) {
      // 按日期范围查询
      checkIns = await this.habitCheckInRepository.findByDateRange(
        userId,
        baby_id || habit.baby_id,
        new Date(start_date),
        new Date(end_date),
        habitId,
      );
    } else {
      // 查询习惯的所有打卡记录
      checkIns = await this.habitCheckInRepository.findByHabit(
        habitId,
        userId,
        baby_id || habit.baby_id,
        { page: Math.floor(offset / limit) + 1, limit },
      );
    }

    return checkIns.map((checkIn) => this.mapToResponseDto(checkIn));
  }

  /**
   * 获取指定日期的打卡记录
   */
  async findByDate(
    habitId: string,
    userId: string,
    date: string,
  ): Promise<HabitCheckInResponseDto | null> {
    // 验证习惯是否存在且用户有权限
    const habit = await this.validateHabitAccess(habitId, userId);

    const checkInDate = new Date(date);
    const checkIn = await this.habitCheckInRepository.findByDate(
      habitId,
      userId,
      habit.baby_id,
      checkInDate,
    );

    return checkIn ? this.mapToResponseDto(checkIn) : null;
  }

  /**
   * 获取今日打卡情况
   */
  async getTodayCheckIns(
    userId: string,
    babyId?: string,
  ): Promise<HabitCheckInResponseDto[]> {
    if (babyId) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(babyId, userId);
    }

    const today = new Date();
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const todayEnd = new Date(todayStart);
    todayEnd.setDate(todayEnd.getDate() + 1);

    const checkIns = await this.habitCheckInRepository.findByDateRange(
      userId,
      babyId || '',
      todayStart,
      todayEnd,
    );

    return checkIns.map((checkIn) => this.mapToResponseDto(checkIn));
  }

  /**
   * 获取习惯打卡统计
   */
  async getCheckInStats(
    habitId: string,
    userId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<{
    totalCheckIns: number;
    streakDays: number;
    completionRate: number;
    lastCheckIn?: Date;
  }> {
    // 验证习惯是否存在且用户有权限
    const habit = await this.validateHabitAccess(habitId, userId);

    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    const [totalCheckIns, streakDays] = await Promise.all([
      this.habitCheckInRepository.countCheckIns(
        habitId,
        userId,
        habit.baby_id,
        start,
        end,
      ),
      this.habitCheckInRepository.getStreakDays(habitId, userId, habit.baby_id),
    ]);

    // 获取最近的打卡记录
    const recentCheckIns = await this.habitCheckInRepository.findByHabit(
      habitId,
      userId,
      habit.baby_id,
      { limit: 1 },
    );

    const lastCheckIn =
      recentCheckIns.length > 0 ? recentCheckIns[0].check_in_time : undefined;

    // 计算完成率（如果有日期范围）
    let completionRate = 0;
    if (start && end) {
      const totalDays =
        Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) +
        1;
      completionRate = totalDays > 0 ? (totalCheckIns / totalDays) * 100 : 0;
    }

    return {
      totalCheckIns,
      streakDays,
      completionRate: Math.round(completionRate * 100) / 100, // 保留两位小数
      lastCheckIn,
    };
  }

  /**
   * 获取月度打卡统计
   */
  async getMonthlyStats(
    userId: string,
    year: number,
    month: number,
    babyId?: string,
    habitId?: string,
  ): Promise<{ date: string; count: number }[]> {
    if (babyId) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(babyId, userId);
    }

    if (habitId) {
      // 验证习惯是否存在且用户有权限
      await this.validateHabitAccess(habitId, userId);
    }

    return this.habitCheckInRepository.getMonthlyStats(
      userId,
      babyId || '',
      year,
      month,
      habitId,
    );
  }

  /**
   * 检查今日是否已打卡
   */
  async hasCheckedInToday(habitId: string, userId: string): Promise<boolean> {
    // 验证习惯是否存在且用户有权限
    const habit = await this.validateHabitAccess(habitId, userId);

    return this.habitCheckInRepository.hasCheckedInToday(
      habitId,
      userId,
      habit.baby_id,
    );
  }

  /**
   * 获取用户的所有打卡记录
   */
  async findUserCheckIns(
    userId: string,
    query: HabitCheckInQueryDto,
  ): Promise<HabitCheckInResponseDto[]> {
    const { baby_id, start_date, end_date, limit = 50, offset = 0 } = query;

    if (baby_id) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(baby_id, userId);
    }

    let checkIns: IHabitCheckIn[];

    if (start_date && end_date) {
      // 按日期范围查询
      checkIns = await this.habitCheckInRepository.findByDateRange(
        userId,
        baby_id || '',
        new Date(start_date),
        new Date(end_date),
      );
    } else {
      // 查询用户的所有打卡记录
      checkIns = await this.habitCheckInRepository.findByUser(userId, baby_id, {
        page: Math.floor(offset / limit) + 1,
        limit,
      });
    }

    return checkIns.map((checkIn) => this.mapToResponseDto(checkIn));
  }

  /**
   * 删除打卡记录（仅限当天的记录）
   */
  async deleteCheckIn(checkInId: string, userId: string): Promise<void> {
    const checkIn = await this.habitCheckInRepository.findById(checkInId);

    if (!checkIn) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_NOT_FOUND,
        '打卡记录不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    // 验证用户权限
    if (checkIn.user_id !== userId) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_ACCESS_DENIED,
        '无权删除此打卡记录',
        HttpStatus.FORBIDDEN,
      );
    }

    // 只允许删除当天的打卡记录
    const today = new Date();
    const checkInDate = new Date(checkIn.check_in_date);

    if (checkInDate.toDateString() !== today.toDateString()) {
      throw new BusinessException(
        ErrorCode.HABIT_CHECK_IN_DATE_INVALID,
        '只能删除当天的打卡记录',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.habitCheckInRepository.delete(checkInId);
  }

  /**
   * 验证打卡日期
   */
  private async validateCheckInDate(
    checkInDate: Date,
    habit: any,
  ): Promise<void> {
    const today = new Date();
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const checkInStart = new Date(
      checkInDate.getFullYear(),
      checkInDate.getMonth(),
      checkInDate.getDate(),
    );

    // 不允许未来日期打卡
    if (checkInStart > todayStart) {
      throw new BusinessException(
        ErrorCode.HABIT_CHECK_IN_DATE_INVALID,
        '不能为未来日期打卡',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 不允许补打卡（过去日期）
    if (checkInStart < todayStart) {
      throw new BusinessException(
        ErrorCode.HABIT_BACKDATE_NOT_ALLOWED,
        '不允许补打卡，补打卡需要积分',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 检查今天是否安排了此习惯
    const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay(); // 转换为1-7格式
    if (!habit.frequency.includes(dayOfWeek)) {
      throw new BusinessException(
        ErrorCode.HABIT_NOT_SCHEDULED_TODAY,
        '今日未安排此习惯',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 验证用户是否有权限访问宝宝
   */
  private async validateBabyAccess(
    babyId: string,
    userId: string,
  ): Promise<void> {
    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        '宝宝档案不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const hasAccess = await this.babyRepository.checkBabyAccess(babyId, userId);
    if (!hasAccess) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        '无权访问该宝宝档案',
        HttpStatus.FORBIDDEN,
      );
    }
  }

  /**
   * 验证用户是否有权限访问习惯
   */
  private async validateHabitAccess(
    habitId: string,
    userId: string,
  ): Promise<any> {
    const habit = await this.userHabitRepository.findById(habitId);

    if (!habit) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_NOT_FOUND,
        '用户习惯不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    if (habit.user_id !== userId) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_ACCESS_DENIED,
        '无权访问此习惯',
        HttpStatus.FORBIDDEN,
      );
    }

    return habit;
  }

  /**
   * 将实体映射为响应DTO
   */
  private mapToResponseDto(checkIn: IHabitCheckIn): HabitCheckInResponseDto {
    const dto = new HabitCheckInResponseDto();
    dto.id = checkIn.id;
    dto.habit_id = checkIn.habit_id;
    dto.user_id = checkIn.user_id;
    dto.baby_id = checkIn.baby_id;
    dto.check_in_date = checkIn.check_in_date;
    dto.check_in_time = checkIn.check_in_time;
    dto.notes = checkIn.notes;
    dto.created_at = checkIn.created_at;

    return dto;
  }
}
