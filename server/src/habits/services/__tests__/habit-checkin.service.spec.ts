import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus } from '@nestjs/common';
import { HabitCheckInService } from '../habit-checkin.service';
import { HabitCheckInRepository } from '../../repositories/habit-checkin.repository';
import { UserHabitRepository } from '../../repositories/user-habit.repository';
import { BabyRepository } from '../../../baby/baby.repository';
import { BusinessException } from '../../../common/exceptions/business.exception';
import { ErrorCode } from '../../../common/enums';
import { CreateHabitCheckInDto } from '../../dto/habit-check-in/create-habit-check-in.dto';
import { HabitCheckInQueryDto } from '../../dto/habit-check-in/habit-check-in-query.dto';
import { UserHabitStatus } from '../../interfaces/user-habit.interface';

describe('HabitCheckInService', () => {
  let service: HabitCheckInService;
  let habitCheckInRepository: jest.Mocked<HabitCheckInRepository>;
  let userHabitRepository: jest.Mocked<UserHabitRepository>;
  let babyRepository: jest.Mocked<BabyRepository>;

  const mockUserId = 'user-123';
  const mockBabyId = 'baby-123';
  const mockHabitId = 'habit-123';
  const mockCheckInId = 'checkin-123';

  const mockHabit = {
    id: mockHabitId,
    user_id: mockUserId,
    baby_id: mockBabyId,
    name: '刷牙',
    icon: '🦷',
    theme_color: '#FF6B6B',
    description: '睡前刷牙',
    frequency: [1, 2, 3, 4, 5, 6, 7], // 每天
    preferred_time: '20:00',
    reward_stars: 1,
    status: UserHabitStatus.ACTIVE,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockBaby = {
    id: mockBabyId,
    owner_id: mockUserId,
    nickname: '小宝',
    birth_date: new Date('2020-01-01'),
    gender: 1,
    avatar: null,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockCheckIn = {
    id: mockCheckInId,
    habit_id: mockHabitId,
    user_id: mockUserId,
    baby_id: mockBabyId,
    check_in_date: new Date(),
    check_in_time: new Date(),
    notes: '完成刷牙',
    created_at: new Date(),
  };

  beforeEach(async () => {
    const mockHabitCheckInRepository = {
      createCheckIn: jest.fn(),
      hasCheckedInToday: jest.fn(),
      findByDate: jest.fn(),
      findByHabit: jest.fn(),
      findByDateRange: jest.fn(),
      findByUser: jest.fn(),
      countCheckIns: jest.fn(),
      getStreakDays: jest.fn(),
      getMonthlyStats: jest.fn(),
      findById: jest.fn(),
      delete: jest.fn(),
    };

    const mockUserHabitRepository = {
      findById: jest.fn(),
    };

    const mockBabyRepository = {
      findById: jest.fn(),
      checkBabyAccess: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitCheckInService,
        {
          provide: HabitCheckInRepository,
          useValue: mockHabitCheckInRepository,
        },
        {
          provide: UserHabitRepository,
          useValue: mockUserHabitRepository,
        },
        {
          provide: BabyRepository,
          useValue: mockBabyRepository,
        },
      ],
    }).compile();

    service = module.get<HabitCheckInService>(HabitCheckInService);
    habitCheckInRepository = module.get(HabitCheckInRepository);
    userHabitRepository = module.get(UserHabitRepository);
    babyRepository = module.get(BabyRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('checkIn', () => {
    const createDto: CreateHabitCheckInDto = {
      habit_id: mockHabitId,
      baby_id: mockBabyId,
      notes: '完成刷牙',
    };

    beforeEach(() => {
      userHabitRepository.findById.mockResolvedValue(mockHabit);
      babyRepository.findById.mockResolvedValue(mockBaby);
      babyRepository.checkBabyAccess.mockResolvedValue(true);
      habitCheckInRepository.hasCheckedInToday.mockResolvedValue(false);
      habitCheckInRepository.createCheckIn.mockResolvedValue(mockCheckIn);
    });

    it('should successfully create check-in', async () => {
      const result = await service.checkIn(mockUserId, createDto);

      expect(result.id).toBe(mockCheckInId);
      expect(result.habit_id).toBe(mockHabitId);
      expect(result.user_id).toBe(mockUserId);
      expect(result.baby_id).toBe(mockBabyId);
      expect(habitCheckInRepository.createCheckIn).toHaveBeenCalledWith(
        mockUserId,
        expect.objectContaining({
          habit_id: mockHabitId,
          baby_id: mockBabyId,
          notes: '完成刷牙',
        }),
      );
    });

    it('should throw error if habit not found', async () => {
      userHabitRepository.findById.mockResolvedValue(null);

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_HABIT_NOT_FOUND,
          '用户习惯不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw error if user has no access to habit', async () => {
      userHabitRepository.findById.mockResolvedValue({
        ...mockHabit,
        user_id: 'other-user',
      });

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_HABIT_ACCESS_DENIED,
          '无权访问此习惯',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should throw error if baby not found', async () => {
      babyRepository.findById.mockResolvedValue(null);

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_NOT_FOUND,
          '宝宝档案不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw error if user has no access to baby', async () => {
      babyRepository.checkBabyAccess.mockResolvedValue(false);

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          '无权访问该宝宝档案',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should throw error if habit does not belong to baby', async () => {
      userHabitRepository.findById.mockResolvedValue({
        ...mockHabit,
        baby_id: 'other-baby',
      });

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_HABIT_ACCESS_DENIED,
          '习惯不属于指定的宝宝',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it('should throw error if already checked in today', async () => {
      habitCheckInRepository.hasCheckedInToday.mockResolvedValue(true);

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_ALREADY_CHECKED_IN,
          '今日已完成打卡',
          HttpStatus.CONFLICT,
        ),
      );
    });

    it('should throw error for future date check-in', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const futureDateDto = {
        ...createDto,
        check_in_date: futureDate.toISOString(),
      };

      await expect(service.checkIn(mockUserId, futureDateDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CHECK_IN_DATE_INVALID,
          '不能为未来日期打卡',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it('should throw error for past date check-in (backdate)', async () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      const pastDateDto = {
        ...createDto,
        check_in_date: pastDate.toISOString(),
      };

      await expect(service.checkIn(mockUserId, pastDateDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_BACKDATE_NOT_ALLOWED,
          '不允许补打卡，补打卡需要积分',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it('should throw error if habit not scheduled for today', async () => {
      // Mock habit with frequency that doesn't include today
      const today = new Date();
      const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay();
      const otherDays = [1, 2, 3, 4, 5, 6, 7].filter(
        (day) => day !== dayOfWeek,
      );

      userHabitRepository.findById.mockResolvedValue({
        ...mockHabit,
        frequency: otherDays,
      });

      await expect(service.checkIn(mockUserId, createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_NOT_SCHEDULED_TODAY,
          '今日未安排此习惯',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('findCheckIns', () => {
    const query: HabitCheckInQueryDto = {
      limit: 20,
      offset: 0,
    };

    beforeEach(() => {
      userHabitRepository.findById.mockResolvedValue(mockHabit);
      habitCheckInRepository.findByHabit.mockResolvedValue([mockCheckIn]);
    });

    it('should return check-ins for habit', async () => {
      const result = await service.findCheckIns(mockHabitId, mockUserId, query);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(mockCheckInId);
      expect(habitCheckInRepository.findByHabit).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        mockBabyId,
        expect.objectContaining({ limit: 20 }),
      );
    });

    it('should return check-ins by date range', async () => {
      const queryWithDates = {
        ...query,
        start_date: '2024-01-01',
        end_date: '2024-01-31',
      };

      habitCheckInRepository.findByDateRange.mockResolvedValue([mockCheckIn]);

      const result = await service.findCheckIns(
        mockHabitId,
        mockUserId,
        queryWithDates,
      );

      expect(result).toHaveLength(1);
      expect(habitCheckInRepository.findByDateRange).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date('2024-01-01'),
        new Date('2024-01-31'),
        mockHabitId,
      );
    });
  });

  describe('hasCheckedInToday', () => {
    beforeEach(() => {
      userHabitRepository.findById.mockResolvedValue(mockHabit);
    });

    it('should return true if checked in today', async () => {
      habitCheckInRepository.hasCheckedInToday.mockResolvedValue(true);

      const result = await service.hasCheckedInToday(mockHabitId, mockUserId);

      expect(result).toBe(true);
      expect(habitCheckInRepository.hasCheckedInToday).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        mockBabyId,
      );
    });

    it('should return false if not checked in today', async () => {
      habitCheckInRepository.hasCheckedInToday.mockResolvedValue(false);

      const result = await service.hasCheckedInToday(mockHabitId, mockUserId);

      expect(result).toBe(false);
    });
  });

  describe('getCheckInStats', () => {
    beforeEach(() => {
      userHabitRepository.findById.mockResolvedValue(mockHabit);
      habitCheckInRepository.countCheckIns.mockResolvedValue(10);
      habitCheckInRepository.getStreakDays.mockResolvedValue(5);
      habitCheckInRepository.findByHabit.mockResolvedValue([mockCheckIn]);
    });

    it('should return check-in statistics', async () => {
      const result = await service.getCheckInStats(
        mockHabitId,
        mockUserId,
        '2024-01-01',
        '2024-01-31',
      );

      expect(result.totalCheckIns).toBe(10);
      expect(result.streakDays).toBe(5);
      expect(result.completionRate).toBeGreaterThan(0);
      expect(result.lastCheckIn).toBeDefined();
    });
  });

  describe('deleteCheckIn', () => {
    beforeEach(() => {
      habitCheckInRepository.findById.mockResolvedValue({
        ...mockCheckIn,
        check_in_date: new Date(), // Today's date
      });
    });

    it("should successfully delete today's check-in", async () => {
      await service.deleteCheckIn(mockCheckInId, mockUserId);

      expect(habitCheckInRepository.delete).toHaveBeenCalledWith(mockCheckInId);
    });

    it('should throw error if check-in not found', async () => {
      habitCheckInRepository.findById.mockResolvedValue(null);

      await expect(
        service.deleteCheckIn(mockCheckInId, mockUserId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_HABIT_NOT_FOUND,
          '打卡记录不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw error if user has no access to check-in', async () => {
      habitCheckInRepository.findById.mockResolvedValue({
        ...mockCheckIn,
        user_id: 'other-user',
      });

      await expect(
        service.deleteCheckIn(mockCheckInId, mockUserId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.USER_HABIT_ACCESS_DENIED,
          '无权删除此打卡记录',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should throw error if trying to delete past check-in', async () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      habitCheckInRepository.findById.mockResolvedValue({
        ...mockCheckIn,
        check_in_date: pastDate,
      });

      await expect(
        service.deleteCheckIn(mockCheckInId, mockUserId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CHECK_IN_DATE_INVALID,
          '只能删除当天的打卡记录',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });
});
