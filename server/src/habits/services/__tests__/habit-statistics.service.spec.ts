import { Test, TestingModule } from '@nestjs/testing';
import { HabitStatisticsService } from '../habit-statistics.service';
import { UserHabitRepository } from '../../repositories/user-habit.repository';
import { HabitCheckInRepository } from '../../repositories/habit-checkin.repository';
import {
  IUserHabit,
  UserHabitStatus,
} from '../../interfaces/user-habit.interface';
import { IHabitCheckIn } from '../../interfaces/habit-check-in.interface';

describe('HabitStatisticsService', () => {
  let service: HabitStatisticsService;
  let userHabitRepository: jest.Mocked<UserHabitRepository>;
  let habitCheckInRepository: jest.Mocked<HabitCheckInRepository>;

  const mockUserId = 'user-123';
  const mockBabyId = 'baby-123';
  const mockHabitId = 'habit-123';

  const mockUserHabit: IUserHabit = {
    id: mockHabitId,
    user_id: mockUserId,
    baby_id: mockBabyId,
    template_id: undefined,
    name: '刷牙',
    icon: '🦷',
    theme_color: '#FF6B6B',
    description: '睡前刷牙',
    frequency: [1, 2, 3, 4, 5], // 周一到周五
    preferred_time: '20:00',
    reward_stars: 1,
    status: UserHabitStatus.ACTIVE,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  const mockCheckIn: IHabitCheckIn = {
    id: 'checkin-123',
    habit_id: mockHabitId,
    user_id: mockUserId,
    baby_id: mockBabyId,
    check_in_date: new Date('2024-01-15'),
    check_in_time: new Date('2024-01-15T20:30:00'),
    notes: '很棒！',
    created_at: new Date('2024-01-15T20:30:00'),
  };

  beforeEach(async () => {
    const mockUserHabitRepository = {
      findActiveHabits: jest.fn(),
    };

    const mockHabitCheckInRepository = {
      findByDateRange: jest.fn(),
      getStreakDays: jest.fn(),
      countCheckIns: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitStatisticsService,
        {
          provide: UserHabitRepository,
          useValue: mockUserHabitRepository,
        },
        {
          provide: HabitCheckInRepository,
          useValue: mockHabitCheckInRepository,
        },
      ],
    }).compile();

    service = module.get<HabitStatisticsService>(HabitStatisticsService);
    userHabitRepository = module.get(UserHabitRepository);
    habitCheckInRepository = module.get(HabitCheckInRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDailyStatistics', () => {
    it('should return daily statistics with completed habits', async () => {
      // 模拟周一
      const testDate = new Date('2024-01-15'); // 周一

      userHabitRepository.findActiveHabits.mockResolvedValue([mockUserHabit]);
      habitCheckInRepository.findByDateRange.mockResolvedValue([mockCheckIn]);

      const result = await service.getDailyStatistics(
        mockUserId,
        mockBabyId,
        testDate,
      );

      expect(result).toEqual({
        date: testDate,
        habits: [
          {
            habit_id: mockHabitId,
            habit_name: '刷牙',
            habit_icon: '🦷',
            theme_color: '#FF6B6B',
            is_completed: true,
            check_in_time: mockCheckIn.check_in_time,
          },
        ],
        completion_rate: 1,
      });

      expect(userHabitRepository.findActiveHabits).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
      );
      expect(habitCheckInRepository.findByDateRange).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        expect.any(Date),
        expect.any(Date),
      );
    });

    it('should return daily statistics with incomplete habits', async () => {
      const testDate = new Date('2024-01-15'); // 周一

      userHabitRepository.findActiveHabits.mockResolvedValue([mockUserHabit]);
      habitCheckInRepository.findByDateRange.mockResolvedValue([]); // 没有打卡记录

      const result = await service.getDailyStatistics(
        mockUserId,
        mockBabyId,
        testDate,
      );

      expect(result).toEqual({
        date: testDate,
        habits: [
          {
            habit_id: mockHabitId,
            habit_name: '刷牙',
            habit_icon: '🦷',
            theme_color: '#FF6B6B',
            is_completed: false,
            check_in_time: undefined,
          },
        ],
        completion_rate: 0,
      });
    });

    it('should filter habits by day of week', async () => {
      const testDate = new Date('2024-01-14'); // 周日

      userHabitRepository.findActiveHabits.mockResolvedValue([mockUserHabit]);
      habitCheckInRepository.findByDateRange.mockResolvedValue([]);

      const result = await service.getDailyStatistics(
        mockUserId,
        mockBabyId,
        testDate,
      );

      // 周日不在频率范围内，所以没有习惯
      expect(result).toEqual({
        date: testDate,
        habits: [],
        completion_rate: 0,
      });
    });

    it('should handle empty habits list', async () => {
      const testDate = new Date('2024-01-15');

      userHabitRepository.findActiveHabits.mockResolvedValue([]);
      habitCheckInRepository.findByDateRange.mockResolvedValue([]);

      const result = await service.getDailyStatistics(
        mockUserId,
        mockBabyId,
        testDate,
      );

      expect(result).toEqual({
        date: testDate,
        habits: [],
        completion_rate: 0,
      });
    });
  });

  describe('getWeeklyStatistics', () => {
    it('should return weekly statistics', async () => {
      const weekStart = new Date('2024-01-15'); // 周一

      // 模拟每天都有一个习惯，前3天完成，后4天未完成
      userHabitRepository.findActiveHabits.mockResolvedValue([mockUserHabit]);

      // 模拟前3天完成，后4天未完成
      habitCheckInRepository.findByDateRange.mockImplementation(
        (userId, babyId, startDate) => {
          const day = startDate.getDate();
          // 15, 16, 17日完成，18, 19, 20, 21日未完成
          return Promise.resolve(day <= 17 ? [mockCheckIn] : []);
        },
      );

      const result = await service.getWeeklyStatistics(
        mockUserId,
        mockBabyId,
        weekStart,
      );

      expect(result.week_start).toEqual(weekStart);
      expect(result.week_end).toEqual(new Date('2024-01-21'));
      expect(result.daily_completion).toHaveLength(7);
      // 实际的完成率取决于具体的日期和频率设置
      expect(result.overall_completion_rate).toBeGreaterThanOrEqual(0);
      expect(result.overall_completion_rate).toBeLessThanOrEqual(1);

      // 检查每日完成数据的结构
      result.daily_completion.forEach((day) => {
        expect(day).toHaveProperty('date');
        expect(day).toHaveProperty('completion_rate');
        expect(day).toHaveProperty('completed_count');
        expect(day).toHaveProperty('total_count');
        expect(day.completion_rate).toBeGreaterThanOrEqual(0);
        expect(day.completion_rate).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('getMonthlyStatistics', () => {
    it('should return monthly statistics', async () => {
      const year = 2024;
      const month = 1;

      userHabitRepository.findActiveHabits.mockResolvedValue([mockUserHabit]);

      // 模拟前15天完成，后16天未完成
      habitCheckInRepository.findByDateRange.mockImplementation(
        (userId, babyId, startDate) => {
          const day = startDate.getDate();
          // 只有工作日（周一到周五）才有习惯，所以需要检查是否在频率范围内
          const dayOfWeek = startDate.getDay() === 0 ? 7 : startDate.getDay();
          const isWorkDay = [1, 2, 3, 4, 5].includes(dayOfWeek);
          return Promise.resolve(day <= 15 && isWorkDay ? [mockCheckIn] : []);
        },
      );

      const result = await service.getMonthlyStatistics(
        mockUserId,
        mockBabyId,
        year,
        month,
      );

      expect(result.year).toBe(year);
      expect(result.month).toBe(month);
      expect(result.calendar_data).toHaveLength(31); // 1月有31天

      // 检查结果结构
      expect(result.calendar_data).toHaveLength(31);
      expect(result.overall_completion_rate).toBeGreaterThanOrEqual(0);
      expect(result.overall_completion_rate).toBeLessThanOrEqual(1);
    });
  });

  describe('getOverviewStatistics', () => {
    it('should return overview statistics with timeline', async () => {
      const today = new Date();

      userHabitRepository.findActiveHabits.mockResolvedValue([mockUserHabit]);
      habitCheckInRepository.findByDateRange.mockResolvedValue([mockCheckIn]);

      const result = await service.getOverviewStatistics(
        mockUserId,
        mockBabyId,
      );

      expect(result.today_completion_rate).toBeDefined();
      expect(result.today_completed_count).toBeDefined();
      expect(result.today_total_count).toBeDefined();
      expect(result.today_timeline).toBeDefined();
      expect(Array.isArray(result.today_timeline)).toBe(true);

      if (result.today_timeline.length > 0) {
        expect(result.today_timeline[0]).toHaveProperty('habit_id');
        expect(result.today_timeline[0]).toHaveProperty('habit_name');
        expect(result.today_timeline[0]).toHaveProperty('habit_icon');
        expect(result.today_timeline[0]).toHaveProperty('theme_color');
        expect(result.today_timeline[0]).toHaveProperty('is_completed');
        expect(result.today_timeline[0]).toHaveProperty('preferred_time');
      }
    });
  });

  describe('getHabitDetailStatistics', () => {
    it('should return habit detail statistics', async () => {
      const year = 2024;
      const month = 1;

      habitCheckInRepository.findByDateRange.mockResolvedValue([mockCheckIn]);
      habitCheckInRepository.getStreakDays.mockResolvedValue(5);
      habitCheckInRepository.countCheckIns.mockResolvedValue(20);

      const result = await service.getHabitDetailStatistics(
        mockHabitId,
        mockUserId,
        mockBabyId,
        year,
        month,
      );

      expect(result.habit_id).toBe(mockHabitId);
      expect(result.current_month_data).toHaveLength(31); // 1月有31天
      expect(result.completion_rate).toBeDefined();
      expect(result.streak_days).toBe(5);
      expect(result.total_check_ins).toBe(20);

      expect(habitCheckInRepository.findByDateRange).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date(2024, 0, 1),
        new Date(2024, 0, 31),
        mockHabitId,
      );
      expect(habitCheckInRepository.getStreakDays).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        mockBabyId,
      );
      expect(habitCheckInRepository.countCheckIns).toHaveBeenCalledWith(
        mockHabitId,
        mockUserId,
        mockBabyId,
      );
    });

    it('should use current year and month as default', async () => {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      habitCheckInRepository.findByDateRange.mockResolvedValue([]);
      habitCheckInRepository.getStreakDays.mockResolvedValue(0);
      habitCheckInRepository.countCheckIns.mockResolvedValue(0);

      const result = await service.getHabitDetailStatistics(
        mockHabitId,
        mockUserId,
        mockBabyId,
      );

      expect(result.habit_id).toBe(mockHabitId);
      expect(habitCheckInRepository.findByDateRange).toHaveBeenCalledWith(
        mockUserId,
        mockBabyId,
        new Date(currentYear, currentMonth - 1, 1),
        expect.any(Date),
        mockHabitId,
      );
    });
  });

  describe('error handling', () => {
    it('should handle repository errors gracefully in getDailyStatistics', async () => {
      const testDate = new Date('2024-01-15');

      userHabitRepository.findActiveHabits.mockRejectedValue(
        new Error('Database error'),
      );

      const result = await service.getDailyStatistics(
        mockUserId,
        mockBabyId,
        testDate,
      );

      // Service should handle errors gracefully and return empty data
      expect(result.habits).toEqual([]);
      expect(result.completion_rate).toBe(0);
    });

    it('should handle repository errors gracefully in getHabitDetailStatistics', async () => {
      habitCheckInRepository.findByDateRange.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        service.getHabitDetailStatistics(mockHabitId, mockUserId, mockBabyId),
      ).rejects.toThrow('Database error');
    });
  });
});
