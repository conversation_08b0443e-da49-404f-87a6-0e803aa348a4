import { Injectable, HttpStatus } from '@nestjs/common';
import { UserHabitRepository } from '../repositories/user-habit.repository';
import { HabitTemplateRepository } from '../repositories/habit-template.repository';
import { BabyRepository } from '../../baby/baby.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import {
  IUserHabit,
  ICreateUserHabit,
  IUpdateUserHabit,
  UserHabitStatus,
} from '../interfaces/user-habit.interface';
import { CreateUserHabitDto } from '../dto/user-habit/create-user-habit.dto';
import { UpdateUserHabitDto } from '../dto/user-habit/update-user-habit.dto';
import { UserHabitResponseDto } from '../dto/user-habit/user-habit-response.dto';
import { UserHabitQueryDto } from '../dto/user-habit/user-habit-query.dto';

@Injectable()
export class UserHabitService {
  constructor(
    private readonly userHabitRepository: UserHabitRepository,
    private readonly habitTemplateRepository: HabitTemplateRepository,
    private readonly babyRepository: BabyRepository,
  ) {}

  /**
   * 获取用户的习惯列表
   */
  async findUserHabits(
    userId: string,
    query: UserHabitQueryDto,
  ): Promise<UserHabitResponseDto[]> {
    const { baby_id, template_id, status, limit, offset } = query;

    let habits: IUserHabit[];

    if (baby_id) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(baby_id, userId);
      habits = await this.userHabitRepository.findByUserAndBaby(
        userId,
        baby_id,
        status,
      );
    } else {
      habits = await this.userHabitRepository.findByUser(userId, status);
    }

    // 如果指定了模板ID，进行过滤
    if (template_id) {
      habits = habits.filter((habit) => habit.template_id === template_id);
    }

    // 应用分页
    if (limit !== undefined && offset !== undefined) {
      habits = habits.slice(offset, offset + limit);
    }

    return habits.map((habit) => this.mapToResponseDto(habit));
  }

  /**
   * 根据ID获取用户习惯
   */
  async findById(
    habitId: string,
    userId: string,
  ): Promise<UserHabitResponseDto> {
    const habit = await this.userHabitRepository.findById(habitId);

    if (!habit) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_NOT_FOUND,
        '用户习惯不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    // 验证用户权限
    if (habit.user_id !== userId) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_ACCESS_DENIED,
        '无权访问此习惯',
        HttpStatus.FORBIDDEN,
      );
    }

    return this.mapToResponseDto(habit);
  }

  /**
   * 创建用户习惯
   */
  async create(
    userId: string,
    createDto: CreateUserHabitDto,
  ): Promise<UserHabitResponseDto> {
    // 如果没有提供baby_id，使用用户的默认宝宝（第一个宝宝）
    let babyId = createDto.baby_id;
    if (!babyId) {
      const defaultBaby =
        await this.babyRepository.findFirstBabyByOwner(userId);
      if (!defaultBaby) {
        throw new BusinessException(
          ErrorCode.BABY_NOT_FOUND,
          '请先创建宝宝档案',
          HttpStatus.BAD_REQUEST,
        );
      }
      babyId = defaultBaby.id;
    }

    // 验证用户是否有权限访问该宝宝
    await this.validateBabyAccess(babyId, userId);

    // 如果基于模板创建，验证模板是否存在
    if (createDto.template_id) {
      const template = await this.habitTemplateRepository.findById(
        createDto.template_id,
      );
      if (!template) {
        throw new BusinessException(
          ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
          '习惯模板不存在',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // 检查同一宝宝下是否存在同名习惯
    const existingHabit = await this.userHabitRepository.findByName(
      userId,
      babyId,
      createDto.name,
    );

    if (existingHabit) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_NAME_EXISTS,
        '该宝宝已存在同名习惯',
        HttpStatus.CONFLICT,
      );
    }

    // 检查用户习惯数量限制（可选，根据业务需求）
    const habitCount = await this.userHabitRepository.countUserHabits(
      userId,
      babyId,
      UserHabitStatus.ACTIVE,
    );

    const HABIT_LIMIT = 50; // 可配置的习惯数量限制
    if (habitCount >= HABIT_LIMIT) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_LIMIT_EXCEEDED,
        `活跃习惯数量不能超过${HABIT_LIMIT}个`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const createData: ICreateUserHabit = {
      baby_id: babyId,
      template_id: createDto.template_id,
      name: createDto.name,
      icon: createDto.icon,
      theme_color: createDto.theme_color,
      description: createDto.description || '',
      frequency: createDto.frequency,
      preferred_time: createDto.preferred_time,
      reward_stars: createDto.reward_stars ?? 1,
      status: createDto.status ?? UserHabitStatus.ACTIVE,
    };

    const habit = await this.userHabitRepository.createUserHabit(
      userId,
      createData,
    );
    return this.mapToResponseDto(habit);
  }

  /**
   * 更新用户习惯
   */
  async update(
    habitId: string,
    userId: string,
    updateDto: UpdateUserHabitDto,
  ): Promise<UserHabitResponseDto> {
    // 验证习惯是否存在且用户有权限
    const existingHabit = await this.validateHabitAccess(habitId, userId);

    // 如果更新名称，检查是否重复
    if (updateDto.name && updateDto.name !== existingHabit.name) {
      const duplicateHabit = await this.userHabitRepository.findByName(
        userId,
        existingHabit.baby_id,
        updateDto.name,
      );

      if (duplicateHabit && duplicateHabit.id !== habitId) {
        throw new BusinessException(
          ErrorCode.USER_HABIT_NAME_EXISTS,
          '该宝宝已存在同名习惯',
          HttpStatus.CONFLICT,
        );
      }
    }

    const updateData: IUpdateUserHabit = {
      name: updateDto.name,
      icon: updateDto.icon,
      theme_color: updateDto.theme_color,
      description: updateDto.description,
      frequency: updateDto.frequency,
      preferred_time: updateDto.preferred_time,
      reward_stars: updateDto.reward_stars,
      status: updateDto.status,
    };

    const habit = await this.userHabitRepository.updateUserHabit(
      habitId,
      updateData,
    );
    return this.mapToResponseDto(habit);
  }

  /**
   * 删除用户习惯
   */
  async delete(habitId: string, userId: string): Promise<void> {
    // 验证习惯是否存在且用户有权限
    await this.validateHabitAccess(habitId, userId);

    await this.userHabitRepository.delete(habitId);
  }

  /**
   * 激活习惯
   */
  async activate(
    habitId: string,
    userId: string,
  ): Promise<UserHabitResponseDto> {
    // 验证习惯是否存在且用户有权限
    await this.validateHabitAccess(habitId, userId);

    const habit = await this.userHabitRepository.activateHabit(habitId, userId);
    return this.mapToResponseDto(habit);
  }

  /**
   * 归档习惯
   */
  async archive(
    habitId: string,
    userId: string,
  ): Promise<UserHabitResponseDto> {
    // 验证习惯是否存在且用户有权限
    await this.validateHabitAccess(habitId, userId);

    const habit = await this.userHabitRepository.archiveHabit(habitId, userId);
    return this.mapToResponseDto(habit);
  }

  /**
   * 获取今日习惯列表
   */
  async getTodayHabits(
    userId: string,
    babyId?: string,
  ): Promise<UserHabitResponseDto[]> {
    if (babyId) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(babyId, userId);
    }

    const habits = await this.userHabitRepository.findTodayHabits(
      userId,
      babyId,
    );
    return habits.map((habit) => this.mapToResponseDto(habit));
  }

  /**
   * 搜索用户习惯
   */
  async searchHabits(
    userId: string,
    searchTerm: string,
    babyId?: string,
    limit = 20,
    offset = 0,
  ): Promise<UserHabitResponseDto[]> {
    if (babyId) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(babyId, userId);
    }

    const habits = await this.userHabitRepository.searchUserHabits(
      userId,
      searchTerm,
      babyId,
      { page: Math.floor(offset / limit) + 1, limit },
    );

    return habits.map((habit) => this.mapToResponseDto(habit));
  }

  /**
   * 获取用户习惯统计
   */
  async getHabitStats(
    userId: string,
    babyId?: string,
  ): Promise<{
    total: number;
    active: number;
    archived: number;
    todayScheduled: number;
  }> {
    if (babyId) {
      // 验证用户是否有权限访问该宝宝
      await this.validateBabyAccess(babyId, userId);
    }

    const [total, active, archived, todayHabits] = await Promise.all([
      this.userHabitRepository.countUserHabits(userId, babyId),
      this.userHabitRepository.countUserHabits(
        userId,
        babyId,
        UserHabitStatus.ACTIVE,
      ),
      this.userHabitRepository.countUserHabits(
        userId,
        babyId,
        UserHabitStatus.ARCHIVED,
      ),
      this.userHabitRepository.findTodayHabits(userId, babyId),
    ]);

    return {
      total,
      active,
      archived,
      todayScheduled: todayHabits.length,
    };
  }

  /**
   * 批量创建习惯（从模板）
   */
  async createFromTemplates(
    userId: string,
    babyId: string,
    templateIds: string[],
  ): Promise<UserHabitResponseDto[]> {
    // 验证用户是否有权限访问该宝宝
    await this.validateBabyAccess(babyId, userId);

    if (!templateIds || templateIds.length === 0) {
      return [];
    }

    // 验证所有模板是否存在
    const templates = await Promise.all(
      templateIds.map((id) => this.habitTemplateRepository.findById(id)),
    );

    for (let i = 0; i < templates.length; i++) {
      if (!templates[i]) {
        throw new BusinessException(
          ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
          `习惯模板不存在: ${templateIds[i]}`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // 检查名称重复
    for (const template of templates) {
      const existing = await this.userHabitRepository.findByName(
        userId,
        babyId,
        template!.name,
      );
      if (existing) {
        throw new BusinessException(
          ErrorCode.USER_HABIT_NAME_EXISTS,
          `习惯名称已存在: ${template!.name}`,
          HttpStatus.CONFLICT,
        );
      }
    }

    const results: UserHabitResponseDto[] = [];

    for (const template of templates) {
      const createData: ICreateUserHabit = {
        baby_id: babyId,
        template_id: template!.id,
        name: template!.name,
        icon: template!.icon,
        theme_color: template!.theme_color,
        description: template!.description,
        frequency: [1, 2, 3, 4, 5, 6, 7], // 默认每天
        preferred_time: undefined,
        reward_stars: 1,
        status: UserHabitStatus.ACTIVE,
      };

      const habit = await this.userHabitRepository.createUserHabit(
        userId,
        createData,
      );
      results.push(this.mapToResponseDto(habit));
    }

    return results;
  }

  /**
   * 验证用户是否有权限访问宝宝
   */
  private async validateBabyAccess(
    babyId: string,
    userId: string,
  ): Promise<void> {
    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        '宝宝档案不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const hasAccess = await this.babyRepository.checkBabyAccess(babyId, userId);
    if (!hasAccess) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        '无权访问该宝宝档案',
        HttpStatus.FORBIDDEN,
      );
    }
  }

  /**
   * 验证用户是否有权限访问习惯
   */
  private async validateHabitAccess(
    habitId: string,
    userId: string,
  ): Promise<IUserHabit> {
    const habit = await this.userHabitRepository.findById(habitId);

    if (!habit) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_NOT_FOUND,
        '用户习惯不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    if (habit.user_id !== userId) {
      throw new BusinessException(
        ErrorCode.USER_HABIT_ACCESS_DENIED,
        '无权访问此习惯',
        HttpStatus.FORBIDDEN,
      );
    }

    return habit;
  }

  /**
   * 将实体映射为响应DTO
   */
  private mapToResponseDto(habit: IUserHabit): UserHabitResponseDto {
    const dto = new UserHabitResponseDto();
    dto.id = habit.id;
    dto.user_id = habit.user_id;
    dto.baby_id = habit.baby_id;
    dto.template_id = habit.template_id;
    dto.name = habit.name;
    dto.icon = habit.icon;
    dto.theme_color = habit.theme_color;
    dto.description = habit.description;
    dto.frequency = habit.frequency;
    dto.preferred_time = habit.preferred_time;
    dto.reward_stars = habit.reward_stars;
    dto.status = habit.status;
    dto.created_at = habit.created_at;
    dto.updated_at = habit.updated_at;

    return dto;
  }
}
