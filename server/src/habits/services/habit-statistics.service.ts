import { Injectable, Logger } from '@nestjs/common';
import { UserHabitRepository } from '../repositories/user-habit.repository';
import { HabitCheckInRepository } from '../repositories/habit-checkin.repository';
import {
  IDailyStatistics,
  IWeeklyStatistics,
  IMonthlyStatistics,
  IOverviewStatistics,
  IHabitDetailStatistics,
} from '../interfaces/habit-statistics.interface';
import { IUserHabit } from '../interfaces/user-habit.interface';
import { IHabitCheckIn } from '../interfaces/habit-check-in.interface';

@Injectable()
export class HabitStatisticsService {
  private readonly logger = new Logger(HabitStatisticsService.name);

  constructor(
    private readonly userHabitRepository: UserHabitRepository,
    private readonly habitCheckInRepository: HabitCheckInRepository,
  ) {}

  /**
   * 获取日统计数据
   */
  async getDailyStatistics(
    userId: string,
    babyId: string,
    date: Date = new Date(),
  ): Promise<IDailyStatistics> {
    try {
      this.logger.log(
        `Getting daily statistics for user ${userId}, baby ${babyId}, date ${date.toISOString()}`,
      );

      // 获取指定日期应该执行的习惯
      const todayHabits = await this.getTodayHabits(userId, babyId, date);

      // 获取当日的打卡记录
      const startDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);

      const checkIns =
        (await this.habitCheckInRepository.findByDateRange(
          userId,
          babyId,
          startDate,
          endDate,
        )) || [];

      // 创建打卡记录映射
      const checkInMap = new Map<string, IHabitCheckIn>();
      checkIns.forEach((checkIn) => {
        checkInMap.set(checkIn.habit_id, checkIn);
      });

      // 构建习惯完成状态
      const habits = todayHabits.map((habit) => {
        const checkIn = checkInMap.get(habit.id);
        return {
          habit_id: habit.id,
          habit_name: habit.name,
          habit_icon: habit.icon,
          theme_color: habit.theme_color,
          is_completed: !!checkIn,
          check_in_time: checkIn?.check_in_time,
        };
      });

      // 计算完成率
      const completedCount = habits.filter((h) => h.is_completed).length;
      const totalCount = habits.length;
      const completion_rate = totalCount > 0 ? completedCount / totalCount : 0;

      return {
        date,
        habits,
        completion_rate,
      };
    } catch (error) {
      this.logger.error('Failed to get daily statistics', error);
      throw error;
    }
  }

  /**
   * 获取周统计数据
   */
  async getWeeklyStatistics(
    userId: string,
    babyId: string,
    weekStart: Date,
  ): Promise<IWeeklyStatistics> {
    try {
      this.logger.log(
        `Getting weekly statistics for user ${userId}, baby ${babyId}, week start ${weekStart.toISOString()}`,
      );

      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekEnd.getDate() + 6);

      const daily_completion: Array<{
        date: Date;
        completion_rate: number;
        completed_count: number;
        total_count: number;
      }> = [];
      let totalCompletedCount = 0;
      let totalHabitCount = 0;

      // 获取一周内每天的统计
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(weekStart);
        currentDate.setDate(currentDate.getDate() + i);

        const dailyStats = await this.getDailyStatistics(
          userId,
          babyId,
          currentDate,
        );

        const completedCount = dailyStats.habits.filter(
          (h) => h.is_completed,
        ).length;
        const totalCount = dailyStats.habits.length;

        daily_completion.push({
          date: currentDate,
          completion_rate: dailyStats.completion_rate,
          completed_count: completedCount,
          total_count: totalCount,
        });

        totalCompletedCount += completedCount;
        totalHabitCount += totalCount;
      }

      // 计算整体完成率
      const overall_completion_rate =
        totalHabitCount > 0 ? totalCompletedCount / totalHabitCount : 0;

      return {
        week_start: weekStart,
        week_end: weekEnd,
        daily_completion,
        overall_completion_rate,
      };
    } catch (error) {
      this.logger.error('Failed to get weekly statistics', error);
      throw error;
    }
  }

  /**
   * 获取月统计数据
   */
  async getMonthlyStatistics(
    userId: string,
    babyId: string,
    year: number,
    month: number,
  ): Promise<IMonthlyStatistics> {
    try {
      this.logger.log(
        `Getting monthly statistics for user ${userId}, baby ${babyId}, year ${year}, month ${month}`,
      );

      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      const daysInMonth = endDate.getDate();

      const calendar_data: Array<{
        date: Date;
        completion_rate: number;
        completed_count: number;
        total_count: number;
      }> = [];
      let totalCompletedCount = 0;
      let totalHabitCount = 0;

      // 获取月内每天的统计
      for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(year, month - 1, day);

        // 只统计到今天为止的数据
        if (currentDate > new Date()) {
          calendar_data.push({
            date: currentDate,
            completion_rate: 0,
            completed_count: 0,
            total_count: 0,
          });
          continue;
        }

        const dailyStats = await this.getDailyStatistics(
          userId,
          babyId,
          currentDate,
        );

        const completedCount = dailyStats.habits.filter(
          (h) => h.is_completed,
        ).length;
        const totalCount = dailyStats.habits.length;

        calendar_data.push({
          date: currentDate,
          completion_rate: dailyStats.completion_rate,
          completed_count: completedCount,
          total_count: totalCount,
        });

        totalCompletedCount += completedCount;
        totalHabitCount += totalCount;
      }

      // 计算整体完成率
      const overall_completion_rate =
        totalHabitCount > 0 ? totalCompletedCount / totalHabitCount : 0;

      return {
        year,
        month,
        calendar_data,
        overall_completion_rate,
      };
    } catch (error) {
      this.logger.error('Failed to get monthly statistics', error);
      throw error;
    }
  }

  /**
   * 获取首页概览统计
   */
  async getOverviewStatistics(
    userId: string,
    babyId: string,
  ): Promise<IOverviewStatistics> {
    try {
      this.logger.log(
        `Getting overview statistics for user ${userId}, baby ${babyId}`,
      );

      const today = new Date();
      const dailyStats = await this.getDailyStatistics(userId, babyId, today);

      // 构建时间轴数据，包含首选时间信息
      const todayHabits = await this.getTodayHabits(userId, babyId, today);
      const checkIns = await this.getTodayCheckIns(userId, babyId, today);

      const checkInMap = new Map<string, IHabitCheckIn>();
      checkIns.forEach((checkIn) => {
        checkInMap.set(checkIn.habit_id, checkIn);
      });

      const today_timeline = todayHabits.map((habit) => {
        const checkIn = checkInMap.get(habit.id);
        return {
          habit_id: habit.id,
          habit_name: habit.name,
          habit_icon: habit.icon,
          theme_color: habit.theme_color,
          is_completed: !!checkIn,
          check_in_time: checkIn?.check_in_time,
          preferred_time: habit.preferred_time,
        };
      });

      // 按首选时间排序，没有首选时间的放在最后
      today_timeline.sort((a, b) => {
        if (!a.preferred_time && !b.preferred_time) return 0;
        if (!a.preferred_time) return 1;
        if (!b.preferred_time) return -1;
        return a.preferred_time.localeCompare(b.preferred_time);
      });

      return {
        today_completion_rate: dailyStats.completion_rate,
        today_completed_count: dailyStats.habits.filter((h) => h.is_completed)
          .length,
        today_total_count: dailyStats.habits.length,
        today_timeline,
      };
    } catch (error) {
      this.logger.error('Failed to get overview statistics', error);
      throw error;
    }
  }

  /**
   * 获取习惯详情统计
   */
  async getHabitDetailStatistics(
    habitId: string,
    userId: string,
    babyId: string,
    year?: number,
    month?: number,
  ): Promise<IHabitDetailStatistics> {
    try {
      this.logger.log(
        `Getting habit detail statistics for habit ${habitId}, user ${userId}, baby ${babyId}`,
      );

      // 使用当前年月作为默认值
      const now = new Date();
      const targetYear = year || now.getFullYear();
      const targetMonth = month || now.getMonth() + 1;

      // 获取当月数据
      const startDate = new Date(targetYear, targetMonth - 1, 1);
      const endDate = new Date(targetYear, targetMonth, 0);
      const daysInMonth = endDate.getDate();

      const current_month_data: Array<{
        date: Date;
        is_completed: boolean;
        check_in_time?: Date;
        notes?: string;
      }> = [];

      // 获取当月的打卡记录
      const checkIns = await this.habitCheckInRepository.findByDateRange(
        userId,
        babyId,
        startDate,
        endDate,
        habitId,
      );

      const checkInMap = new Map<string, IHabitCheckIn>();
      checkIns.forEach((checkIn) => {
        const dateKey = new Date(checkIn.check_in_date)
          .toISOString()
          .split('T')[0];
        checkInMap.set(dateKey, checkIn);
      });

      // 构建每日数据
      for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(targetYear, targetMonth - 1, day);
        const dateKey = currentDate.toISOString().split('T')[0];
        const checkIn = checkInMap.get(dateKey);

        current_month_data.push({
          date: currentDate,
          is_completed: !!checkIn,
          check_in_time: checkIn?.check_in_time,
          notes: checkIn?.notes,
        });
      }

      // 计算完成率
      const completedDays = current_month_data.filter(
        (d) => d.is_completed,
      ).length;
      const totalDays = current_month_data.length;
      const completion_rate = totalDays > 0 ? completedDays / totalDays : 0;

      // 计算连续天数
      const streak_days = await this.habitCheckInRepository.getStreakDays(
        habitId,
        userId,
        babyId,
      );

      // 计算总打卡次数
      const total_check_ins = await this.habitCheckInRepository.countCheckIns(
        habitId,
        userId,
        babyId,
      );

      return {
        habit_id: habitId,
        current_month_data,
        completion_rate,
        streak_days,
        total_check_ins,
      };
    } catch (error) {
      this.logger.error('Failed to get habit detail statistics', error);
      throw error;
    }
  }

  /**
   * 获取指定日期应该执行的习惯
   */
  private async getTodayHabits(
    userId: string,
    babyId: string,
    date: Date,
  ): Promise<IUserHabit[]> {
    try {
      // 获取用户的所有活跃习惯
      const allHabits =
        (await this.userHabitRepository.findActiveHabits(userId, babyId)) || [];

      // 获取指定日期是周几 (1-7, 周一到周日)
      const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();

      // 筛选出当天应该执行的习惯
      return allHabits.filter(
        (habit) => habit.frequency && habit.frequency.includes(dayOfWeek),
      );
    } catch (error) {
      this.logger.error('Failed to get today habits', error);
      return [];
    }
  }

  /**
   * 获取今日的打卡记录
   */
  private async getTodayCheckIns(
    userId: string,
    babyId: string,
    date: Date,
  ): Promise<IHabitCheckIn[]> {
    try {
      const startDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);

      return (
        (await this.habitCheckInRepository.findByDateRange(
          userId,
          babyId,
          startDate,
          endDate,
        )) || []
      );
    } catch (error) {
      this.logger.error('Failed to get today check-ins', error);
      return [];
    }
  }
}
