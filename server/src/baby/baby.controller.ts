import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import { BabyService } from './baby.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateBabyDto } from './dto/create-baby.dto';
import { UpdateBabyDto } from './dto/update-baby.dto';
import { BabyResponseDto } from './dto/baby-response.dto';
import { IUser } from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

@Controller('babies')
@UseGuards(JwtAuthGuard)
export class BabyController {
  constructor(private readonly babyService: BabyService) {}

  /**
   * 获取用户有权限访问的所有宝宝
   */
  @Get('/getAll')
  async getBabies(@Request() req: { user: IUser }): Promise<BabyResponseDto[]> {
    return this.babyService.getBabies(req.user.id);
  }

  /**
   * 创建宝宝档案
   */
  @Post('create')
  @HttpCode(HttpStatus.CREATED)
  async createBaby(
    @Request() req: { user: IUser },
    @Body() createBabyDto: CreateBabyDto,
  ): Promise<BabyResponseDto> {
    // 检查宝宝数量限制
    const hasReachedLimit = await this.babyService.checkBabyLimit(req.user.id);
    if (hasReachedLimit) {
      throw new BusinessException(
        ErrorCode.BABY_LIMIT_EXCEEDED,
        'Maximum number of babies (5) reached',
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.babyService.createBaby(req.user.id, createBabyDto);
  }

  /**
   * 根据ID获取宝宝信息
   */
  @Get(':id')
  async getBabyById(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: IUser },
  ): Promise<BabyResponseDto> {
    return this.babyService.getBabyById(id, req.user.id);
  }

  /**
   * 更新宝宝信息
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  async updateBaby(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: IUser },
    @Body() updateBabyDto: UpdateBabyDto,
  ): Promise<BabyResponseDto> {
    return this.babyService.updateBaby(id, req.user.id, updateBabyDto);
  }

  /**
   * 删除宝宝档案
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteBaby(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: IUser },
  ): Promise<void> {
    return this.babyService.deleteBaby(id, req.user.id);
  }

  /**
   * 获取用户的默认宝宝
   */
  @Get('default/current')
  async getDefaultBaby(
    @Request() req: { user: IUser },
  ): Promise<BabyResponseDto | null> {
    return this.babyService.getDefaultBaby(req.user.id);
  }

  /**
   * 设置默认宝宝
   */
  @Put(':id/default')
  @HttpCode(HttpStatus.OK)
  async setDefaultBaby(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user: IUser },
  ): Promise<{ success: boolean }> {
    await this.babyService.setDefaultBaby(req.user.id, id);
    return { success: true };
  }
}
