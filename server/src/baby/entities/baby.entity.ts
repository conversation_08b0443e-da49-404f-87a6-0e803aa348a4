import { Gender } from '../../common/enums';
import {
  IBaby,
  ICreateBabyDto,
  IUpdateBabyDto,
  IBabyResponse,
} from '../../common/interfaces';
import { BabyValidator } from '../../common/validators';

export class Baby implements IBaby {
  id: string;
  owner_id: string;
  nickname: string;
  birth_date: Date;
  gender: number; // 1: 男, 2: 女
  avatar_url?: string;
  created_at: Date;
  updated_at: Date;

  // 关系属性 (不存储在数据库中)
  is_default?: boolean; // 是否为用户的默认宝宝

  constructor(partial: Partial<Baby>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新宝宝实例
   */
  static create(data: ICreateBabyDto, ownerId: string): Baby {
    const validation = BabyValidator.validateBabyData({
      nickname: data.nickname,
      birth_date: data.birth_date,
      gender: data.gender,
      avatar_url: data.avatar_url,
    });

    if (!validation.isValid) {
      throw new Error(`Invalid baby data: ${validation.errors.join(', ')}`);
    }

    const now = new Date();
    return new Baby({
      id: '', // 将由数据库生成
      owner_id: ownerId,
      nickname: data.nickname,
      birth_date: data.birth_date,
      gender: data.gender,
      avatar_url: data.avatar_url,
      created_at: now,
      updated_at: now,
    });
  }

  /**
   * 更新宝宝信息
   */
  update(data: IUpdateBabyDto): void {
    // 验证更新数据
    const updateData = {
      nickname: data.nickname ?? this.nickname,
      birth_date: data.birth_date ?? this.birth_date,
      gender: data.gender ?? this.gender,
      avatar_url: data.avatar_url ?? this.avatar_url,
    };

    const validation = BabyValidator.validateBabyData(updateData);
    if (!validation.isValid) {
      throw new Error(`Invalid update data: ${validation.errors.join(', ')}`);
    }

    // 应用更新
    if (data.nickname !== undefined) this.nickname = data.nickname;
    if (data.birth_date !== undefined) this.birth_date = data.birth_date;
    if (data.gender !== undefined) this.gender = data.gender;
    if (data.avatar_url !== undefined) this.avatar_url = data.avatar_url;

    this.updated_at = new Date();
  }

  /**
   * 验证宝宝数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return BabyValidator.validateBabyData({
      nickname: this.nickname,
      birth_date: this.birth_date,
      gender: this.gender,
      avatar_url: this.avatar_url,
    });
  }

  /**
   * 计算宝宝年龄（月数）
   */
  getAgeInMonths(): number {
    const now = new Date();
    const birthDate = new Date(this.birth_date);

    let months = (now.getFullYear() - birthDate.getFullYear()) * 12;
    months += now.getMonth() - birthDate.getMonth();

    // 如果当前日期小于出生日期，减去一个月
    if (now.getDate() < birthDate.getDate()) {
      months--;
    }

    return Math.max(0, months);
  }

  /**
   * 计算宝宝年龄（天数）
   */
  getAgeInDays(): number {
    const now = new Date();
    const birthDate = new Date(this.birth_date);
    const diffTime = now.getTime() - birthDate.getTime();
    return Math.max(0, Math.floor(diffTime / (1000 * 60 * 60 * 24)));
  }

  /**
   * 获取年龄描述
   */
  getAgeDescription(): string {
    const months = this.getAgeInMonths();
    const days = this.getAgeInDays();

    if (months < 1) {
      return `${days}天`;
    } else if (months < 12) {
      return `${months}个月`;
    } else {
      const years = Math.floor(months / 12);
      const remainingMonths = months % 12;
      if (remainingMonths === 0) {
        return `${years}岁`;
      } else {
        return `${years}岁${remainingMonths}个月`;
      }
    }
  }

  /**
   * 获取性别描述
   */
  getGenderDescription(): string {
    return this.gender === Gender.MALE ? '男' : '女';
  }

  /**
   * 检查是否为宝宝的所有者
   */
  isOwnedBy(userId: string): boolean {
    return this.owner_id === userId;
  }

  /**
   * 设置默认状态
   */
  setDefaultStatus(isDefault: boolean): void {
    this.is_default = isDefault;
  }

  /**
   * 检查宝宝信息是否完整
   */
  isProfileComplete(): boolean {
    return !!(
      this.nickname &&
      this.birth_date &&
      this.gender &&
      this.avatar_url
    );
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): IBabyResponse {
    return {
      id: this.id,
      owner_id: this.owner_id,
      nickname: this.nickname,
      birth_date: this.birth_date,
      gender: this.gender,
      avatar_url: this.avatar_url,
      is_default: this.is_default,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }

  /**
   * 转换为详细响应格式（包含计算字段）
   */
  toDetailedResponse(): IBabyResponse & {
    age_in_months: number;
    age_in_days: number;
    age_description: string;
    gender_description: string;
  } {
    return {
      ...this.toResponse(),
      age_in_months: this.getAgeInMonths(),
      age_in_days: this.getAgeInDays(),
      age_description: this.getAgeDescription(),
      gender_description: this.getGenderDescription(),
    };
  }
}
