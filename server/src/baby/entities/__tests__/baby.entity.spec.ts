import { Baby } from '../baby.entity';
import { Gender } from '../../../common/enums';
import { ICreateBabyDto, IUpdateBabyDto } from '../../../common/interfaces';

describe('Baby Entity', () => {
  const validBabyData: ICreateBabyDto = {
    nickname: '小明',
    birth_date: new Date('2020-01-01'),
    gender: Gender.MALE,
    avatar_url: 'https://example.com/baby.jpg',
  };

  const ownerId = 'owner-123';

  describe('constructor', () => {
    it('should create baby instance with partial data', () => {
      const babyData = {
        id: 'baby-123',
        owner_id: ownerId,
        nickname: '小明',
        birth_date: new Date('2020-01-01'),
        gender: Gender.MALE,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const baby = new Baby(babyData);

      expect(baby.id).toBe(babyData.id);
      expect(baby.owner_id).toBe(babyData.owner_id);
      expect(baby.nickname).toBe(babyData.nickname);
      expect(baby.birth_date).toBe(babyData.birth_date);
      expect(baby.gender).toBe(babyData.gender);
    });
  });

  describe('create', () => {
    it('should create baby with valid data', () => {
      const baby = Baby.create(validBabyData, ownerId);

      expect(baby.owner_id).toBe(ownerId);
      expect(baby.nickname).toBe(validBabyData.nickname);
      expect(baby.birth_date).toBe(validBabyData.birth_date);
      expect(baby.gender).toBe(validBabyData.gender);
      expect(baby.avatar_url).toBe(validBabyData.avatar_url);
      expect(baby.created_at).toBeInstanceOf(Date);
      expect(baby.updated_at).toBeInstanceOf(Date);
    });

    it('should create baby with minimal data', () => {
      const minimalData: ICreateBabyDto = {
        nickname: '小明',
        birth_date: new Date('2020-01-01'),
        gender: Gender.MALE,
      };

      const baby = Baby.create(minimalData, ownerId);

      expect(baby.nickname).toBe(minimalData.nickname);
      expect(baby.birth_date).toBe(minimalData.birth_date);
      expect(baby.gender).toBe(minimalData.gender);
      expect(baby.avatar_url).toBeUndefined();
    });

    it('should throw error for invalid data', () => {
      const invalidData: ICreateBabyDto = {
        nickname: '', // Invalid empty nickname
        birth_date: new Date('2030-01-01'), // Future date
        gender: 0, // Invalid gender
        avatar_url: 'invalid-url', // Invalid URL
      };

      expect(() => Baby.create(invalidData, ownerId)).toThrow(
        'Invalid baby data',
      );
    });
  });

  describe('update', () => {
    let baby: Baby;

    beforeEach(() => {
      baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';
    });

    it('should update baby with valid data', () => {
      const updateData: IUpdateBabyDto = {
        nickname: '小红',
        birth_date: new Date('2021-01-01'),
        gender: Gender.FEMALE,
        avatar_url: 'https://example.com/new-baby.jpg',
      };

      const originalUpdatedAt = baby.updated_at;

      baby.update(updateData);

      expect(baby.nickname).toBe(updateData.nickname);
      expect(baby.birth_date).toBe(updateData.birth_date);
      expect(baby.gender).toBe(updateData.gender);
      expect(baby.avatar_url).toBe(updateData.avatar_url);
      expect(baby.updated_at).not.toBe(originalUpdatedAt);
    });

    it('should update only provided fields', () => {
      const originalNickname = baby.nickname;
      const originalGender = baby.gender;

      const updateData: IUpdateBabyDto = {
        birth_date: new Date('2021-01-01'),
        avatar_url: 'https://example.com/new-baby.jpg',
      };

      baby.update(updateData);

      expect(baby.nickname).toBe(originalNickname);
      expect(baby.gender).toBe(originalGender);
      expect(baby.birth_date).toBe(updateData.birth_date);
      expect(baby.avatar_url).toBe(updateData.avatar_url);
    });

    it('should throw error for invalid update data', () => {
      const invalidUpdateData: IUpdateBabyDto = {
        nickname: '', // Empty nickname
        birth_date: new Date('2030-01-01'), // Future date
        gender: 0, // Invalid gender
      };

      expect(() => baby.update(invalidUpdateData)).toThrow(
        'Invalid update data',
      );
    });
  });

  describe('validate', () => {
    it('should return valid for correct baby data', () => {
      const baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';

      const result = baby.validate();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect baby data', () => {
      const baby = new Baby({
        id: 'baby-123',
        owner_id: ownerId,
        nickname: '', // Invalid empty nickname
        birth_date: new Date('2030-01-01'), // Future date
        gender: 0, // Invalid gender
        avatar_url: 'invalid-url', // Invalid URL
        created_at: new Date(),
        updated_at: new Date(),
      });

      const result = baby.validate();

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('age calculations', () => {
    let baby: Baby;

    beforeEach(() => {
      // Create a baby born 1 year and 6 months ago
      const birthDate = new Date();
      birthDate.setFullYear(birthDate.getFullYear() - 1);
      birthDate.setMonth(birthDate.getMonth() - 6);

      baby = Baby.create(
        {
          ...validBabyData,
          birth_date: birthDate,
        },
        ownerId,
      );
      baby.id = 'baby-123';
    });

    describe('getAgeInMonths', () => {
      it('should calculate age in months correctly', () => {
        const months = baby.getAgeInMonths();
        expect(months).toBe(18); // 1 year 6 months = 18 months
      });

      it('should return 0 for newborn baby', () => {
        const newbornBaby = Baby.create(
          {
            ...validBabyData,
            birth_date: new Date(), // Today
          },
          ownerId,
        );

        expect(newbornBaby.getAgeInMonths()).toBe(0);
      });
    });

    describe('getAgeInDays', () => {
      it('should calculate age in days correctly', () => {
        const days = baby.getAgeInDays();
        expect(days).toBeGreaterThan(500); // Approximately 18 months in days
      });

      it('should return 0 for newborn baby', () => {
        const newbornBaby = Baby.create(
          {
            ...validBabyData,
            birth_date: new Date(), // Today
          },
          ownerId,
        );

        expect(newbornBaby.getAgeInDays()).toBe(0);
      });
    });

    describe('getAgeDescription', () => {
      it('should return correct age description for months', () => {
        const youngBaby = Baby.create(
          {
            ...validBabyData,
            birth_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000 * 6), // 6 months ago
          },
          ownerId,
        );

        const description = youngBaby.getAgeDescription();
        expect(description).toContain('个月');
      });

      it('should return correct age description for days', () => {
        const newBaby = Baby.create(
          {
            ...validBabyData,
            birth_date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
          },
          ownerId,
        );

        const description = newBaby.getAgeDescription();
        expect(description).toContain('天');
      });

      it('should return correct age description for years', () => {
        const description = baby.getAgeDescription();
        expect(description).toContain('岁');
      });
    });
  });

  describe('getGenderDescription', () => {
    it('should return correct gender description for male', () => {
      const maleBaby = Baby.create(
        {
          ...validBabyData,
          gender: Gender.MALE,
        },
        ownerId,
      );

      expect(maleBaby.getGenderDescription()).toBe('男');
    });

    it('should return correct gender description for female', () => {
      const femaleBaby = Baby.create(
        {
          ...validBabyData,
          gender: Gender.FEMALE,
        },
        ownerId,
      );

      expect(femaleBaby.getGenderDescription()).toBe('女');
    });
  });

  describe('isOwnedBy', () => {
    let baby: Baby;

    beforeEach(() => {
      baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';
    });

    it('should return true for correct owner', () => {
      expect(baby.isOwnedBy(ownerId)).toBe(true);
    });

    it('should return false for different user', () => {
      expect(baby.isOwnedBy('different-user-id')).toBe(false);
    });
  });

  describe('setDefaultStatus', () => {
    let baby: Baby;

    beforeEach(() => {
      baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';
    });

    it('should set default status to true', () => {
      baby.setDefaultStatus(true);
      expect(baby.is_default).toBe(true);
    });

    it('should set default status to false', () => {
      baby.setDefaultStatus(false);
      expect(baby.is_default).toBe(false);
    });
  });

  describe('isProfileComplete', () => {
    it('should return true for complete profile', () => {
      const baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';

      expect(baby.isProfileComplete()).toBe(true);
    });

    it('should return false for incomplete profile', () => {
      const incompleteData: ICreateBabyDto = {
        nickname: '小明',
        birth_date: new Date('2020-01-01'),
        gender: Gender.MALE,
        // Missing avatar_url
      };

      const baby = Baby.create(incompleteData, ownerId);
      baby.id = 'baby-123';

      expect(baby.isProfileComplete()).toBe(false);
    });
  });

  describe('toResponse', () => {
    it('should return baby data in response format', () => {
      const baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';
      baby.setDefaultStatus(true);

      const response = baby.toResponse();

      expect(response).toEqual({
        id: baby.id,
        owner_id: baby.owner_id,
        nickname: baby.nickname,
        birth_date: baby.birth_date,
        gender: baby.gender,
        avatar_url: baby.avatar_url,
        is_default: baby.is_default,
        created_at: baby.created_at,
        updated_at: baby.updated_at,
      });
    });
  });

  describe('toDetailedResponse', () => {
    it('should return baby data with calculated fields', () => {
      const baby = Baby.create(validBabyData, ownerId);
      baby.id = 'baby-123';

      const response = baby.toDetailedResponse();

      expect(response).toHaveProperty('id', baby.id);
      expect(response).toHaveProperty('nickname', baby.nickname);
      expect(response).toHaveProperty('age_in_months');
      expect(response).toHaveProperty('age_in_days');
      expect(response).toHaveProperty('age_description');
      expect(response).toHaveProperty('gender_description');
      expect(typeof response.age_in_months).toBe('number');
      expect(typeof response.age_in_days).toBe('number');
      expect(typeof response.age_description).toBe('string');
      expect(typeof response.gender_description).toBe('string');
    });
  });
});
