import { Test, TestingModule } from '@nestjs/testing';
import { BabyRepository } from '../baby.repository';
import { SupabaseService } from '../../database/supabase.service';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode, Gender } from '../../common/enums';

describe('BabyRepository', () => {
  let repository: BabyRepository;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockSupabaseQuery = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      query: jest.fn().mockReturnValue(mockSupabaseQuery),
      handleDatabaseError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BabyRepository,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    repository = module.get<BabyRepository>(BabyRepository);
    supabaseService = module.get(SupabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findByOwnerId', () => {
    it('should find babies by owner id', async () => {
      const mockBabies = [
        {
          id: '1',
          owner_id: 'user-1',
          nickname: 'Baby 1',
          birth_date: new Date('2023-01-01'),
          gender: Gender.MALE,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: '2',
          owner_id: 'user-1',
          nickname: 'Baby 2',
          birth_date: new Date('2023-06-01'),
          gender: Gender.FEMALE,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      jest.spyOn(repository, 'findMany').mockResolvedValue(mockBabies);

      const result = await repository.findByOwnerId('user-1');

      expect(result).toEqual(mockBabies);
      expect(repository.findMany).toHaveBeenCalledWith(
        { owner_id: 'user-1' },
        { sortBy: 'created_at', sortOrder: 'asc' },
      );
    });
  });

  describe('createBaby', () => {
    it('should create baby successfully', async () => {
      const babyData = {
        owner_id: 'user-1',
        nickname: 'Test Baby',
        birth_date: new Date('2023-01-01'),
        gender: Gender.MALE,
      };

      const mockCreatedBaby = {
        id: '123',
        ...babyData,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock count to return 0 (no existing babies)
      jest.spyOn(repository, 'count').mockResolvedValue(0);
      jest.spyOn(repository, 'create').mockResolvedValue(mockCreatedBaby);

      const result = await repository.createBaby(babyData);

      expect(result).toEqual(mockCreatedBaby);
      expect(repository.count).toHaveBeenCalledWith({ owner_id: 'user-1' });
    });

    it('should throw error when baby limit exceeded', async () => {
      const babyData = {
        owner_id: 'user-1',
        nickname: 'Test Baby',
        birth_date: new Date('2023-01-01'),
        gender: Gender.MALE,
      };

      // Mock count to return 5 (maximum reached)
      jest.spyOn(repository, 'count').mockResolvedValue(5);

      await expect(repository.createBaby(babyData)).rejects.toThrow(
        BusinessException,
      );
      await expect(repository.createBaby(babyData)).rejects.toThrow(
        'Maximum 5 babies allowed per user',
      );
    });

    it('should throw error for invalid gender', async () => {
      const babyData = {
        owner_id: 'user-1',
        nickname: 'Test Baby',
        birth_date: new Date('2023-01-01'),
        gender: 999, // Invalid gender
      };

      jest.spyOn(repository, 'count').mockResolvedValue(0);

      await expect(repository.createBaby(babyData)).rejects.toThrow(
        BusinessException,
      );
      await expect(repository.createBaby(babyData)).rejects.toThrow(
        'Invalid gender value',
      );
    });
  });

  describe('checkBabyAccess', () => {
    it('should return true when user has access', async () => {
      // Mock the complete query chain
      const mockResult = Promise.resolve({
        data: null,
        error: null,
        count: 1,
      });

      const mockCountQuery: any = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
      };

      // The second eq call should return the promise
      mockCountQuery.eq
        .mockReturnValueOnce(mockCountQuery)
        .mockReturnValueOnce(mockResult);
      supabaseService.query.mockReturnValue(mockCountQuery);

      const result = await repository.checkBabyAccess('baby-1', 'user-1');

      expect(result).toBe(true);
      expect(supabaseService.query).toHaveBeenCalledWith('family_members');
    });

    it('should return false when user has no access', async () => {
      // Mock the complete query chain
      const mockResult = Promise.resolve({
        data: null,
        error: null,
        count: 0,
      });

      const mockCountQuery: any = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
      };

      // The second eq call should return the promise
      mockCountQuery.eq
        .mockReturnValueOnce(mockCountQuery)
        .mockReturnValueOnce(mockResult);
      supabaseService.query.mockReturnValue(mockCountQuery);

      const result = await repository.checkBabyAccess('baby-1', 'user-1');

      expect(result).toBe(false);
    });
  });

  describe('checkDuplicateNicknameInFamily', () => {
    it('should return true when duplicate nickname exists in family', async () => {
      // Mock the query to return a baby with the same nickname
      const mockResult = Promise.resolve({
        data: [{ id: 'baby-1', nickname: 'Test Baby' }],
        error: null,
      });

      const mockQuery: any = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnValue(mockResult),
      };

      supabaseService.query.mockReturnValue(mockQuery);

      const result = await repository.checkDuplicateNicknameInFamily(
        'user-1',
        'Test Baby',
      );

      expect(result).toBe(true);
    });

    it('should return false when no duplicate nickname exists', async () => {
      // Mock the query to return empty array
      const mockResult = Promise.resolve({
        data: [],
        error: null,
      });

      const mockQuery: any = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnValue(mockResult),
      };

      supabaseService.query.mockReturnValue(mockQuery);

      const result = await repository.checkDuplicateNicknameInFamily(
        'user-1',
        'Unique Baby',
      );

      expect(result).toBe(false);
    });
  });

  describe('validateCreateData', () => {
    it('should validate required fields', () => {
      expect(() => {
        (repository as any).validateCreateData({});
      }).toThrow(BusinessException);
    });

    it('should validate nickname is not empty', () => {
      expect(() => {
        (repository as any).validateCreateData({
          owner_id: 'user-1',
          nickname: '',
          birth_date: new Date(),
          gender: Gender.MALE,
        });
      }).toThrow(BusinessException);
    });

    it('should validate birth date is not in future', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);

      expect(() => {
        (repository as any).validateCreateData({
          owner_id: 'user-1',
          nickname: 'Test Baby',
          birth_date: futureDate,
          gender: Gender.MALE,
        });
      }).toThrow(BusinessException);
    });

    it('should pass validation for valid data', () => {
      expect(() => {
        (repository as any).validateCreateData({
          owner_id: 'user-1',
          nickname: 'Test Baby',
          birth_date: new Date('2023-01-01'),
          gender: Gender.MALE,
        });
      }).not.toThrow();
    });
  });
});
