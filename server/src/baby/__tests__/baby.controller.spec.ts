import { Test, TestingModule } from '@nestjs/testing';
import { Baby<PERSON><PERSON>roller } from '../baby.controller';
import { BabyService } from '../baby.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CreateBabyDto } from '../dto/create-baby.dto';
import { UpdateBabyDto } from '../dto/update-baby.dto';
import { BabyResponseDto } from '../dto/baby-response.dto';
import { IUser } from '../../common/interfaces/entities';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode, FamilyRole } from '../../common/enums';
import { HttpStatus } from '@nestjs/common';

describe('BabyController', () => {
  let controller: BabyController;
  let babyService: jest.Mocked<BabyService>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: '***********',
    avatar_url: 'https://example.com/avatar.jpg',
    default_baby_id: 'baby-id-1',
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  const mockBabyResponse: BabyResponseDto = {
    id: 'baby-id-1',
    owner_id: 'user-id-1',
    nickname: 'Test Baby',
    birth_date: new Date('2023-01-01'),
    gender: 1,
    avatar_url: 'https://example.com/baby-avatar.jpg',
    is_default: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  const mockBabyResponse2: BabyResponseDto = {
    id: 'baby-id-2',
    owner_id: 'user-id-1',
    nickname: 'Test Baby 2',
    birth_date: new Date('2023-06-01'),
    gender: 2,
    avatar_url: 'https://example.com/baby2-avatar.jpg',
    is_default: false,
    created_at: new Date('2024-01-02'),
    updated_at: new Date('2024-01-02'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BabyController],
      providers: [
        {
          provide: BabyService,
          useValue: {
            getBabies: jest.fn(),
            createBaby: jest.fn(),
            getBabyById: jest.fn(),
            updateBaby: jest.fn(),
            deleteBaby: jest.fn(),
            getDefaultBaby: jest.fn(),
            setDefaultBaby: jest.fn(),
            checkBabyLimit: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<BabyController>(BabyController);
    babyService = module.get(BabyService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getBabies', () => {
    it('should return all accessible babies', async () => {
      // Arrange
      const req = { user: mockUser };
      const expectedBabies = [mockBabyResponse, mockBabyResponse2];
      babyService.getBabies.mockResolvedValue(expectedBabies);

      // Act
      const result = await controller.getBabies(req);

      // Assert
      expect(babyService.getBabies).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual(expectedBabies);
    });

    it('should return empty array when no babies exist', async () => {
      // Arrange
      const req = { user: mockUser };
      babyService.getBabies.mockResolvedValue([]);

      // Act
      const result = await controller.getBabies(req);

      // Assert
      expect(babyService.getBabies).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual([]);
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const error = new Error('Service error');
      babyService.getBabies.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getBabies(req)).rejects.toThrow(error);
      expect(babyService.getBabies).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('createBaby', () => {
    it('should create baby successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const createBabyDto: CreateBabyDto = {
        nickname: 'New Baby',
        birth_date: new Date('2023-01-01'),
        gender: 1,
        avatar_url: 'https://example.com/new-baby-avatar.jpg',
        is_default: true,
        role: FamilyRole.OTHER_CAREGIVER,
      };
      babyService.checkBabyLimit.mockResolvedValue(false);
      babyService.createBaby.mockResolvedValue(mockBabyResponse);

      // Act
      const result = await controller.createBaby(req, createBabyDto);

      // Assert
      expect(babyService.checkBabyLimit).toHaveBeenCalledWith(mockUser.id);
      expect(babyService.createBaby).toHaveBeenCalledWith(
        mockUser.id,
        createBabyDto,
      );
      expect(result).toEqual(mockBabyResponse);
    });

    it('should throw error when baby limit exceeded', async () => {
      // Arrange
      const req = { user: mockUser };
      const createBabyDto: CreateBabyDto = {
        nickname: 'New Baby',
        birth_date: new Date('2023-01-01'),
        gender: 1,
        role: FamilyRole.OTHER_CAREGIVER,
      };
      babyService.checkBabyLimit.mockResolvedValue(true);

      // Act & Assert
      await expect(controller.createBaby(req, createBabyDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_LIMIT_EXCEEDED,
          'Maximum number of babies (5) reached',
          HttpStatus.BAD_REQUEST,
        ),
      );
      expect(babyService.checkBabyLimit).toHaveBeenCalledWith(mockUser.id);
      expect(babyService.createBaby).not.toHaveBeenCalled();
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const createBabyDto: CreateBabyDto = {
        nickname: 'New Baby',
        birth_date: new Date('2023-01-01'),
        gender: 1,
        role: FamilyRole.OTHER_CAREGIVER,
      };
      babyService.checkBabyLimit.mockResolvedValue(false);
      const error = new Error('Service error');
      babyService.createBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createBaby(req, createBabyDto)).rejects.toThrow(
        error,
      );
      expect(babyService.createBaby).toHaveBeenCalledWith(
        mockUser.id,
        createBabyDto,
      );
    });
  });

  describe('getBabyById', () => {
    it('should return baby by id successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      babyService.getBabyById.mockResolvedValue(mockBabyResponse);

      // Act
      const result = await controller.getBabyById(babyId, req);

      // Assert
      expect(babyService.getBabyById).toHaveBeenCalledWith(babyId, mockUser.id);
      expect(result).toEqual(mockBabyResponse);
    });

    it('should handle baby not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'non-existent-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby profile not found',
        HttpStatus.NOT_FOUND,
      );
      babyService.getBabyById.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getBabyById(babyId, req)).rejects.toThrow(error);
      expect(babyService.getBabyById).toHaveBeenCalledWith(babyId, mockUser.id);
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'unauthorized-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'You do not have permission to access this baby profile',
        HttpStatus.FORBIDDEN,
      );
      babyService.getBabyById.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getBabyById(babyId, req)).rejects.toThrow(error);
      expect(babyService.getBabyById).toHaveBeenCalledWith(babyId, mockUser.id);
    });
  });

  describe('updateBaby', () => {
    it('should update baby successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      const updateBabyDto: UpdateBabyDto = {
        nickname: 'Updated Baby Name',
        avatar_url: 'https://example.com/updated-avatar.jpg',
      };
      const updatedBaby = {
        ...mockBabyResponse,
        nickname: 'Updated Baby Name',
      };
      babyService.updateBaby.mockResolvedValue(updatedBaby);

      // Act
      const result = await controller.updateBaby(babyId, req, updateBabyDto);

      // Assert
      expect(babyService.updateBaby).toHaveBeenCalledWith(
        babyId,
        mockUser.id,
        updateBabyDto,
      );
      expect(result).toEqual(updatedBaby);
    });

    it('should update default baby setting successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-2';
      const updateBabyDto: UpdateBabyDto = {
        is_default: true,
      };
      const updatedBaby = { ...mockBabyResponse2, is_default: true };
      babyService.updateBaby.mockResolvedValue(updatedBaby);

      // Act
      const result = await controller.updateBaby(babyId, req, updateBabyDto);

      // Assert
      expect(babyService.updateBaby).toHaveBeenCalledWith(
        babyId,
        mockUser.id,
        updateBabyDto,
      );
      expect(result).toEqual(updatedBaby);
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'unauthorized-baby-id';
      const updateBabyDto: UpdateBabyDto = {
        nickname: 'Updated Name',
      };
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'You do not have permission to update this baby profile',
        HttpStatus.FORBIDDEN,
      );
      babyService.updateBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateBaby(babyId, req, updateBabyDto),
      ).rejects.toThrow(error);
      expect(babyService.updateBaby).toHaveBeenCalledWith(
        babyId,
        mockUser.id,
        updateBabyDto,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      const updateBabyDto: UpdateBabyDto = {
        nickname: 'Updated Name',
      };
      const error = new Error('Service error');
      babyService.updateBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updateBaby(babyId, req, updateBabyDto),
      ).rejects.toThrow(error);
      expect(babyService.updateBaby).toHaveBeenCalledWith(
        babyId,
        mockUser.id,
        updateBabyDto,
      );
    });
  });

  describe('deleteBaby', () => {
    it('should delete baby successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      babyService.deleteBaby.mockResolvedValue();

      // Act
      const result = await controller.deleteBaby(babyId, req);

      // Assert
      expect(babyService.deleteBaby).toHaveBeenCalledWith(babyId, mockUser.id);
      expect(result).toBeUndefined();
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'unauthorized-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can delete the baby profile',
        HttpStatus.FORBIDDEN,
      );
      babyService.deleteBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteBaby(babyId, req)).rejects.toThrow(error);
      expect(babyService.deleteBaby).toHaveBeenCalledWith(babyId, mockUser.id);
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      const error = new Error('Service error');
      babyService.deleteBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteBaby(babyId, req)).rejects.toThrow(error);
      expect(babyService.deleteBaby).toHaveBeenCalledWith(babyId, mockUser.id);
    });
  });

  describe('getDefaultBaby', () => {
    it('should return default baby successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      babyService.getDefaultBaby.mockResolvedValue(mockBabyResponse);

      // Act
      const result = await controller.getDefaultBaby(req);

      // Assert
      expect(babyService.getDefaultBaby).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual(mockBabyResponse);
    });

    it('should return null when no default baby exists', async () => {
      // Arrange
      const req = { user: mockUser };
      babyService.getDefaultBaby.mockResolvedValue(null);

      // Act
      const result = await controller.getDefaultBaby(req);

      // Assert
      expect(babyService.getDefaultBaby).toHaveBeenCalledWith(mockUser.id);
      expect(result).toBeNull();
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const error = new Error('Service error');
      babyService.getDefaultBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getDefaultBaby(req)).rejects.toThrow(error);
      expect(babyService.getDefaultBaby).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('setDefaultBaby', () => {
    it('should set default baby successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-2';
      babyService.setDefaultBaby.mockResolvedValue();

      // Act
      const result = await controller.setDefaultBaby(babyId, req);

      // Assert
      expect(babyService.setDefaultBaby).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
      expect(result).toEqual({ success: true });
    });

    it('should handle baby not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'non-existent-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby profile not found',
        HttpStatus.NOT_FOUND,
      );
      babyService.setDefaultBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.setDefaultBaby(babyId, req)).rejects.toThrow(
        error,
      );
      expect(babyService.setDefaultBaby).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'unauthorized-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can set default baby',
        HttpStatus.FORBIDDEN,
      );
      babyService.setDefaultBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.setDefaultBaby(babyId, req)).rejects.toThrow(
        error,
      );
      expect(babyService.setDefaultBaby).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
    });

    it('should handle service error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-2';
      const error = new Error('Service error');
      babyService.setDefaultBaby.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.setDefaultBaby(babyId, req)).rejects.toThrow(
        error,
      );
      expect(babyService.setDefaultBaby).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
    });
  });
});
