import { Test, TestingModule } from '@nestjs/testing';
import { BabyService } from '../baby.service';
import { BabyRepository } from '../baby.repository';
import { UserRepository } from '../../user/user.repository';
import { FamilyRepository } from '../../family/family.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode, Gender, FamilyRole } from '../../common/enums';
import {
  IBaby,
  IUser,
  ICreateBabyDto,
  IUpdateBabyDto,
} from '../../common/interfaces/entities';

describe('BabyService', () => {
  let service: BabyService;
  let babyRepository: jest.Mocked<BabyRepository>;
  let userRepository: jest.Mocked<UserRepository>;
  let familyRepository: jest.Mocked<FamilyRepository>;

  const mockUser: IUser = {
    id: 'user-1',
    openid: 'openid-1',
    username: 'testuser',
    phone: '***********',
    avatar_url: 'https://example.com/avatar.jpg',
    default_baby_id: undefined,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  const mockBaby: IBaby = {
    id: 'baby-1',
    owner_id: 'user-1',
    nickname: '小宝',
    birth_date: new Date('2023-01-01'),
    gender: Gender.MALE,
    avatar_url: 'https://example.com/baby-avatar.jpg',
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  const mockCreateBabyDto: ICreateBabyDto & { role: number } = {
    nickname: '小宝',
    birth_date: new Date('2023-01-01'),
    gender: Gender.MALE,
    avatar_url: 'https://example.com/baby-avatar.jpg',
    is_default: true,
    role: FamilyRole.OTHER_CAREGIVER,
  };

  beforeEach(async () => {
    const mockBabyRepository = {
      createBaby: jest.fn(),
      findAccessibleBabies: jest.fn(),
      findById: jest.fn(),
      updateBaby: jest.fn(),
      deleteBaby: jest.fn(),
      checkBabyAccess: jest.fn(),
      checkBabyOwnership: jest.fn(),
      findByOwnerId: jest.fn(),
      findFirstBabyByOwner: jest.fn(),
      checkDuplicateNicknameInFamily: jest.fn(),
    };

    const mockUserRepository = {
      findById: jest.fn(),
      setDefaultBaby: jest.fn(),
    };

    const mockFamilyRepository = {
      createFamilyMember: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BabyService,
        { provide: BabyRepository, useValue: mockBabyRepository },
        { provide: UserRepository, useValue: mockUserRepository },
        { provide: FamilyRepository, useValue: mockFamilyRepository },
      ],
    }).compile();

    service = module.get<BabyService>(BabyService);
    babyRepository = module.get(BabyRepository);
    userRepository = module.get(UserRepository);
    familyRepository = module.get(FamilyRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createBaby', () => {
    it('should create a baby successfully', async () => {
      babyRepository.checkDuplicateNicknameInFamily.mockResolvedValue(false);
      babyRepository.createBaby.mockResolvedValue(mockBaby);
      familyRepository.createFamilyMember.mockResolvedValue({
        id: 'family-member-1',
        baby_id: 'baby-1',
        user_id: 'user-1',
        role: FamilyRole.OTHER_CAREGIVER,
        is_owner: true,
        created_at: new Date(),
      });
      userRepository.findById.mockResolvedValue(mockUser);
      babyRepository.findByOwnerId.mockResolvedValue([mockBaby]);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      const result = await service.createBaby('user-1', mockCreateBabyDto);

      expect(
        babyRepository.checkDuplicateNicknameInFamily,
      ).toHaveBeenCalledWith('user-1', '小宝');
      expect(babyRepository.createBaby).toHaveBeenCalledWith({
        nickname: mockCreateBabyDto.nickname,
        birth_date: mockCreateBabyDto.birth_date,
        gender: mockCreateBabyDto.gender,
        avatar_url: mockCreateBabyDto.avatar_url,
        is_default: mockCreateBabyDto.is_default,
        owner_id: 'user-1',
      });
      expect(familyRepository.createFamilyMember).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        user_id: 'user-1',
        role: FamilyRole.OTHER_CAREGIVER,
        is_owner: true,
      });
      expect(result).toEqual({
        ...mockBaby,
        is_default: false, // Will be set based on user's default_baby_id
      });
    });

    it('should throw error when duplicate nickname exists in family', async () => {
      babyRepository.checkDuplicateNicknameInFamily.mockResolvedValue(true);

      await expect(
        service.createBaby('user-1', mockCreateBabyDto),
      ).rejects.toThrow(BusinessException);

      expect(
        babyRepository.checkDuplicateNicknameInFamily,
      ).toHaveBeenCalledWith('user-1', '小宝');
      expect(babyRepository.createBaby).not.toHaveBeenCalled();
    });

    it('should set first baby as default automatically', async () => {
      babyRepository.checkDuplicateNicknameInFamily.mockResolvedValue(false);
      babyRepository.createBaby.mockResolvedValue(mockBaby);
      familyRepository.createFamilyMember.mockResolvedValue({
        id: 'family-member-1',
        baby_id: 'baby-1',
        user_id: 'user-1',
        role: FamilyRole.OTHER_CAREGIVER,
        is_owner: true,
        created_at: new Date(),
      });
      userRepository.findById.mockResolvedValue(mockUser);
      babyRepository.findByOwnerId.mockResolvedValue([mockBaby]); // First baby
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      await service.createBaby('user-1', {
        ...mockCreateBabyDto,
        is_default: false,
      });

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        'baby-1',
      );
    });

    it('should handle baby creation failure', async () => {
      babyRepository.createBaby.mockRejectedValue(
        new BusinessException(
          ErrorCode.BABY_LIMIT_EXCEEDED,
          'Maximum 5 babies allowed per user',
        ),
      );

      await expect(
        service.createBaby('user-1', mockCreateBabyDto),
      ).rejects.toThrow(BusinessException);
    });
  });

  describe('getBabies', () => {
    it('should return all accessible babies with default status', async () => {
      const babies = [
        mockBaby,
        { ...mockBaby, id: 'baby-2', nickname: '小二' },
      ];
      babyRepository.findAccessibleBabies.mockResolvedValue(babies);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      const result = await service.getBabies('user-1');

      expect(result).toHaveLength(2);
      expect(result[0].is_default).toBe(true);
      expect(result[1].is_default).toBe(false);
    });

    it('should return empty array when no babies found', async () => {
      babyRepository.findAccessibleBabies.mockResolvedValue([]);
      userRepository.findById.mockResolvedValue(mockUser);

      const result = await service.getBabies('user-1');

      expect(result).toEqual([]);
    });
  });

  describe('getBabyById', () => {
    it('should return baby by id with default status', async () => {
      babyRepository.checkBabyAccess.mockResolvedValue(true);
      babyRepository.findById.mockResolvedValue(mockBaby);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      const result = await service.getBabyById('baby-1', 'user-1');

      expect(result).toEqual({
        ...mockBaby,
        is_default: true,
      });
    });

    it('should throw error when user has no access', async () => {
      babyRepository.checkBabyAccess.mockResolvedValue(false);

      await expect(service.getBabyById('baby-1', 'user-1')).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'You do not have permission to access this baby profile',
        ),
      );
    });

    it('should throw error when baby not found', async () => {
      babyRepository.checkBabyAccess.mockResolvedValue(true);
      babyRepository.findById.mockResolvedValue(null);

      await expect(service.getBabyById('baby-1', 'user-1')).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_NOT_FOUND,
          'Baby profile not found',
        ),
      );
    });
  });

  describe('updateBaby', () => {
    const updateDto: IUpdateBabyDto = {
      nickname: '小宝贝',
      is_default: true,
    };

    it('should update baby successfully', async () => {
      const updatedBaby = { ...mockBaby, nickname: '小宝贝' };
      babyRepository.checkBabyAccess.mockResolvedValue(true);
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      babyRepository.updateBaby.mockResolvedValue(updatedBaby);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });
      babyRepository.findByOwnerId.mockResolvedValue([
        mockBaby,
        { ...mockBaby, id: 'baby-2' },
      ]);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      const result = await service.updateBaby('baby-1', 'user-1', updateDto);

      expect(babyRepository.updateBaby).toHaveBeenCalledWith(
        'baby-1',
        updateDto,
      );
      expect(result).toEqual({
        ...updatedBaby,
        is_default: true,
      });
    });

    it('should throw error when user has no access', async () => {
      babyRepository.checkBabyAccess.mockResolvedValue(false);

      await expect(
        service.updateBaby('baby-1', 'user-1', updateDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'You do not have permission to update this baby profile',
        ),
      );
    });

    it('should throw error when non-owner tries to set default', async () => {
      babyRepository.checkBabyAccess.mockResolvedValue(true);
      babyRepository.checkBabyOwnership.mockResolvedValue(false);

      await expect(
        service.updateBaby('baby-1', 'user-1', updateDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Only baby owner can change default baby setting',
        ),
      );
    });
  });

  describe('deleteBaby', () => {
    it('should delete baby successfully', async () => {
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-2',
      });
      babyRepository.deleteBaby.mockResolvedValue(true);

      await service.deleteBaby('baby-1', 'user-1');

      expect(babyRepository.deleteBaby).toHaveBeenCalledWith(
        'baby-1',
        'user-1',
      );
    });

    it('should select new default baby when deleting current default', async () => {
      const newDefaultBaby = { ...mockBaby, id: 'baby-2', nickname: '小二' };
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });
      babyRepository.deleteBaby.mockResolvedValue(true);
      babyRepository.findFirstBabyByOwner.mockResolvedValue(newDefaultBaby);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-2',
      });

      await service.deleteBaby('baby-1', 'user-1');

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        'baby-2',
      );
    });

    it('should clear default baby when deleting last baby', async () => {
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });
      babyRepository.deleteBaby.mockResolvedValue(true);
      babyRepository.findFirstBabyByOwner.mockResolvedValue(null);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: undefined,
      });

      await service.deleteBaby('baby-1', 'user-1');

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        null,
      );
    });

    it('should throw error when user is not owner', async () => {
      babyRepository.checkBabyOwnership.mockResolvedValue(false);

      await expect(service.deleteBaby('baby-1', 'user-1')).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Only baby owner can delete the baby profile',
        ),
      );
    });
  });

  describe('getDefaultBaby', () => {
    it('should return default baby', async () => {
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });
      babyRepository.findById.mockResolvedValue(mockBaby);

      const result = await service.getDefaultBaby('user-1');

      expect(result).toEqual({
        ...mockBaby,
        is_default: true,
      });
    });

    it('should return null when no default baby set', async () => {
      userRepository.findById.mockResolvedValue(mockUser);

      const result = await service.getDefaultBaby('user-1');

      expect(result).toBeNull();
    });

    it('should clear invalid default baby reference', async () => {
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });
      babyRepository.findById.mockResolvedValue(null);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: undefined,
      });

      const result = await service.getDefaultBaby('user-1');

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        null,
      );
      expect(result).toBeNull();
    });
  });

  describe('setDefaultBaby', () => {
    it('should set default baby successfully', async () => {
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      babyRepository.findById.mockResolvedValue(mockBaby);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      await service.setDefaultBaby('user-1', 'baby-1');

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        'baby-1',
      );
    });

    it('should throw error when user is not owner', async () => {
      babyRepository.checkBabyOwnership.mockResolvedValue(false);

      await expect(service.setDefaultBaby('user-1', 'baby-1')).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Only baby owner can set default baby',
        ),
      );
    });

    it('should throw error when baby not found', async () => {
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      babyRepository.findById.mockResolvedValue(null);

      await expect(service.setDefaultBaby('user-1', 'baby-1')).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_NOT_FOUND,
          'Baby profile not found',
        ),
      );
    });
  });

  describe('getBabyCount', () => {
    it('should return correct baby count', async () => {
      babyRepository.findByOwnerId.mockResolvedValue([
        mockBaby,
        { ...mockBaby, id: 'baby-2' },
      ]);

      const result = await service.getBabyCount('user-1');

      expect(result).toBe(2);
    });

    it('should return 0 when no babies', async () => {
      babyRepository.findByOwnerId.mockResolvedValue([]);

      const result = await service.getBabyCount('user-1');

      expect(result).toBe(0);
    });
  });

  describe('checkBabyLimit', () => {
    it('should return false when under limit', async () => {
      babyRepository.findByOwnerId.mockResolvedValue([mockBaby]);

      const result = await service.checkBabyLimit('user-1');

      expect(result).toBe(false);
    });

    it('should return true when at limit', async () => {
      const babies = Array(5)
        .fill(null)
        .map((_, i) => ({ ...mockBaby, id: `baby-${i + 1}` }));
      babyRepository.findByOwnerId.mockResolvedValue(babies);

      const result = await service.checkBabyLimit('user-1');

      expect(result).toBe(true);
    });
  });

  describe('default baby logic', () => {
    it('should handle setting new default baby', async () => {
      const babies = [mockBaby, { ...mockBaby, id: 'baby-2' }];
      babyRepository.createBaby.mockResolvedValue(mockBaby);
      familyRepository.createFamilyMember.mockResolvedValue({
        id: 'family-member-1',
        baby_id: 'baby-1',
        user_id: 'user-1',
        role: 3,
        is_owner: true,
        created_at: new Date(),
      });
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-2',
      });
      babyRepository.findByOwnerId.mockResolvedValue(babies);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });

      await service.createBaby('user-1', {
        ...mockCreateBabyDto,
        is_default: true,
      });

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        'baby-1',
      );
    });

    it('should handle unsetting default baby', async () => {
      const firstBaby = { ...mockBaby, id: 'baby-2', nickname: '小二' };
      babyRepository.checkBabyAccess.mockResolvedValue(true);
      babyRepository.checkBabyOwnership.mockResolvedValue(true);
      babyRepository.updateBaby.mockResolvedValue(mockBaby);
      userRepository.findById.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-1',
      });
      babyRepository.findByOwnerId.mockResolvedValue([mockBaby, firstBaby]);
      babyRepository.findFirstBabyByOwner.mockResolvedValue(firstBaby);
      userRepository.setDefaultBaby.mockResolvedValue({
        ...mockUser,
        default_baby_id: 'baby-2',
      });

      await service.updateBaby('baby-1', 'user-1', { is_default: false });

      expect(userRepository.setDefaultBaby).toHaveBeenCalledWith(
        'user-1',
        'baby-2',
      );
    });
  });
});
