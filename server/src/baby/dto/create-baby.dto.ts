import {
  IsString,
  IsNotEmpty,
  IsDateString,
  IsOptional,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Gender, FamilyRole } from '../../common/enums';

export class CreateBabyDto {
  @IsString()
  @IsNotEmpty()
  nickname: string;

  @IsDateString()
  birth_date: Date;

  @IsEnum(Gender)
  gender: Gender;

  @IsOptional()
  @IsString()
  avatar_url?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  is_default?: boolean;

  @IsEnum(FamilyRole)
  role: FamilyRole;
}
