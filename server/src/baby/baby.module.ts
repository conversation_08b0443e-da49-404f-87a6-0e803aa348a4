import { Module, forwardRef } from '@nestjs/common';
import { Baby<PERSON>ontroller } from './baby.controller';
import { BabyService } from './baby.service';
import { BabyRepository } from './baby.repository';
import { UserRepository } from '../user/user.repository';
import { FamilyModule } from '../family/family.module';

@Module({
  imports: [forwardRef(() => FamilyModule)],
  controllers: [BabyController],
  providers: [BabyService, BabyRepository, UserRepository],
  exports: [BabyService, BabyRepository],
})
export class BabyModule {}
