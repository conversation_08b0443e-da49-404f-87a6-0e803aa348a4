import { Injectable } from '@nestjs/common';
import { BabyRepository } from './baby.repository';
import { UserRepository } from '../user/user.repository';
import { FamilyRepository } from '../family/family.repository';
import {
  IBaby,
  ICreateBabyDto,
  IUpdateBabyDto,
  IBabyResponse,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

@Injectable()
export class BabyService {
  constructor(
    private readonly babyRepository: BabyRepository,
    private readonly userRepository: UserRepository,
    private readonly familyRepository: FamilyRepository,
  ) {}

  /**
   * 创建宝宝档案
   */
  async createBaby(
    userId: string,
    createBabyDto: ICreateBabyDto & { role: number },
  ): Promise<IBabyResponse> {
    // 检查家庭中是否存在相同小名的宝宝
    const hasDuplicateNickname =
      await this.babyRepository.checkDuplicateNicknameInFamily(
        userId,
        createBabyDto.nickname,
      );
    if (hasDuplicateNickname) {
      throw new BusinessException(
        ErrorCode.INVALID_BABY_DATA,
        'A baby with this nickname already exists in your family',
        400,
      );
    }

    // 从 DTO 中提取 role，其余字段用于创建宝宝档案
    const { role, ...babyData } = createBabyDto;

    // 创建宝宝档案
    const baby = await this.babyRepository.createBaby({
      ...babyData,
      owner_id: userId,
    });

    // 创建家庭成员关系（所有者）
    await this.familyRepository.createFamilyMember({
      baby_id: baby.id,
      user_id: userId,
      role: role, // 使用传递的角色
      is_owner: true,
    });

    // 处理默认宝宝逻辑
    await this.handleDefaultBabyLogic(
      userId,
      baby.id,
      createBabyDto.is_default,
    );

    // 返回带有默认状态的宝宝信息
    return this.getBabyWithDefaultStatus(baby, userId);
  }

  /**
   * 获取用户有权限访问的所有宝宝
   */
  async getBabies(userId: string): Promise<IBabyResponse[]> {
    const babies = await this.babyRepository.findAccessibleBabies(userId);
    const user = await this.userRepository.findById(userId);

    return babies.map((baby) => ({
      ...baby,
      is_default: user?.default_baby_id === baby.id,
    }));
  }

  /**
   * 根据ID获取宝宝信息
   */
  async getBabyById(babyId: string, userId: string): Promise<IBabyResponse> {
    // 检查访问权限
    const hasAccess = await this.babyRepository.checkBabyAccess(babyId, userId);
    if (!hasAccess) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'You do not have permission to access this baby profile',
      );
    }

    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby profile not found',
      );
    }

    return this.getBabyWithDefaultStatus(baby, userId);
  }

  /**
   * 更新宝宝信息
   */
  async updateBaby(
    babyId: string,
    userId: string,
    updateBabyDto: IUpdateBabyDto,
  ): Promise<IBabyResponse> {
    // 检查访问权限
    const hasAccess = await this.babyRepository.checkBabyAccess(babyId, userId);
    if (!hasAccess) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'You do not have permission to update this baby profile',
      );
    }

    // 检查是否是所有者（只有所有者可以设置默认宝宝）
    const isOwner = await this.babyRepository.checkBabyOwnership(
      babyId,
      userId,
    );
    if (updateBabyDto.is_default !== undefined && !isOwner) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can change default baby setting',
      );
    }

    // 更新宝宝信息
    const updatedBaby = await this.babyRepository.updateBaby(
      babyId,
      updateBabyDto,
    );

    // 处理默认宝宝逻辑（仅所有者可以设置）
    if (updateBabyDto.is_default !== undefined && isOwner) {
      await this.handleDefaultBabyLogic(
        userId,
        babyId,
        updateBabyDto.is_default,
      );
    }

    return this.getBabyWithDefaultStatus(updatedBaby, userId);
  }

  /**
   * 删除宝宝档案
   */
  async deleteBaby(babyId: string, userId: string): Promise<void> {
    // 检查是否是所有者
    const isOwner = await this.babyRepository.checkBabyOwnership(
      babyId,
      userId,
    );
    if (!isOwner) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can delete the baby profile',
      );
    }

    const user = await this.userRepository.findById(userId);
    const isDefaultBaby = user?.default_baby_id === babyId;

    // 删除宝宝档案
    await this.babyRepository.deleteBaby(babyId, userId);

    // 如果删除的是默认宝宝，自动选择新的默认宝宝
    if (isDefaultBaby) {
      await this.selectNewDefaultBaby(userId);
    }
  }

  /**
   * 获取用户的默认宝宝
   */
  async getDefaultBaby(userId: string): Promise<IBabyResponse | null> {
    const user = await this.userRepository.findById(userId);
    if (!user?.default_baby_id) {
      return null;
    }

    const baby = await this.babyRepository.findById(user.default_baby_id);
    if (!baby) {
      // 如果默认宝宝不存在，清除用户的默认宝宝设置
      await this.userRepository.setDefaultBaby(userId, null);
      return null;
    }

    return {
      ...baby,
      is_default: true,
    };
  }

  /**
   * 设置默认宝宝
   */
  async setDefaultBaby(userId: string, babyId: string): Promise<void> {
    // 检查是否是所有者
    const isOwner = await this.babyRepository.checkBabyOwnership(
      babyId,
      userId,
    );
    if (!isOwner) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can set default baby',
      );
    }

    // 检查宝宝是否存在
    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby profile not found',
      );
    }

    // 设置默认宝宝
    await this.userRepository.setDefaultBaby(userId, babyId);
  }

  /**
   * 处理默认宝宝逻辑
   */
  private async handleDefaultBabyLogic(
    userId: string,
    babyId: string,
    isDefault?: boolean,
  ): Promise<void> {
    const user = await this.userRepository.findById(userId);
    const userBabies = await this.babyRepository.findByOwnerId(userId);

    // 如果这是用户的第一个宝宝，自动设为默认
    if (userBabies.length === 1) {
      await this.userRepository.setDefaultBaby(userId, babyId);
      return;
    }

    // 如果明确设置为默认宝宝
    if (isDefault === true) {
      await this.userRepository.setDefaultBaby(userId, babyId);
      return;
    }

    // 如果明确设置为非默认，且当前是默认宝宝，则选择新的默认宝宝
    if (isDefault === false && user?.default_baby_id === babyId) {
      await this.selectNewDefaultBaby(userId);
    }
  }

  /**
   * 选择新的默认宝宝（选择最早创建的宝宝）
   */
  private async selectNewDefaultBaby(userId: string): Promise<void> {
    const firstBaby = await this.babyRepository.findFirstBabyByOwner(userId);

    if (firstBaby) {
      await this.userRepository.setDefaultBaby(userId, firstBaby.id);
    } else {
      await this.userRepository.setDefaultBaby(userId, null);
    }
  }

  /**
   * 获取带有默认状态的宝宝信息
   */
  private async getBabyWithDefaultStatus(
    baby: IBaby,
    userId: string,
  ): Promise<IBabyResponse> {
    const user = await this.userRepository.findById(userId);

    return {
      ...baby,
      is_default: user?.default_baby_id === baby.id,
    };
  }

  /**
   * 获取用户拥有的宝宝数量
   */
  async getBabyCount(userId: string): Promise<number> {
    const babies = await this.babyRepository.findByOwnerId(userId);
    return babies.length;
  }

  /**
   * 检查用户是否达到宝宝数量限制
   */
  async checkBabyLimit(userId: string): Promise<boolean> {
    const count = await this.getBabyCount(userId);
    return count >= 5;
  }
}
