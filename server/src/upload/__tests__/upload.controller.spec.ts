import { Test, TestingModule } from '@nestjs/testing';
import { Readable } from 'stream';
import { UploadController } from '../upload.controller';
import { UploadService } from '../upload.service';
import { UploadResponseDto } from '../dto/upload-response.dto';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';

describe('UploadController', () => {
  let controller: UploadController;
  let uploadService: jest.Mocked<UploadService>;

  beforeEach(async () => {
    const mockUploadService = {
      uploadImage: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UploadController],
      providers: [
        {
          provide: UploadService,
          useValue: mockUploadService,
        },
      ],
    }).compile();

    controller = module.get<UploadController>(UploadController);
    uploadService = module.get(UploadService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('uploadImage', () => {
    const mockFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: 'test.jpg',
      encoding: '7bit',
      mimetype: 'image/jpeg',
      size: 1024 * 1024,
      buffer: Buffer.from('fake image data'),
      destination: '',
      filename: '',
      path: '',
      stream: new Readable(),
    };

    const userId = 'user-123';
    const expectedUrl =
      'https://example.com/storage/images/user-123/123456_abc123.jpg';

    it('should upload image successfully', async () => {
      uploadService.uploadImage.mockResolvedValue(expectedUrl);

      const result = await controller.uploadImage(mockFile, userId);

      expect(result).toBeInstanceOf(UploadResponseDto);
      expect(result.image_url).toBe(expectedUrl);
      expect(uploadService.uploadImage).toHaveBeenCalledWith(mockFile, userId);
    });

    it('should throw error when no file is provided', async () => {
      await expect(controller.uploadImage(null as any, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'No file provided. Please upload an image file.',
        ),
      );

      expect(uploadService.uploadImage).not.toHaveBeenCalled();
    });

    it('should throw error when file is undefined', async () => {
      await expect(
        controller.uploadImage(undefined as any, userId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'No file provided. Please upload an image file.',
        ),
      );

      expect(uploadService.uploadImage).not.toHaveBeenCalled();
    });

    it('should propagate service errors', async () => {
      const serviceError = new BusinessException(
        ErrorCode.FILE_TOO_LARGE,
        'File size exceeds maximum limit of 5MB',
      );

      uploadService.uploadImage.mockRejectedValue(serviceError);

      await expect(controller.uploadImage(mockFile, userId)).rejects.toThrow(
        serviceError,
      );
      expect(uploadService.uploadImage).toHaveBeenCalledWith(mockFile, userId);
    });

    it('should handle different file types', async () => {
      const files = [
        { ...mockFile, originalname: 'test.png', mimetype: 'image/png' },
        { ...mockFile, originalname: 'test.webp', mimetype: 'image/webp' },
        { ...mockFile, originalname: 'test.jpeg', mimetype: 'image/jpeg' },
      ];

      for (const file of files) {
        const expectedUrl = `https://example.com/storage/images/user-123/test.${file.originalname.split('.')[1]}`;
        uploadService.uploadImage.mockResolvedValue(expectedUrl);

        const result = await controller.uploadImage(file, userId);

        expect(result).toBeInstanceOf(UploadResponseDto);
        expect(result.image_url).toBe(expectedUrl);
        expect(uploadService.uploadImage).toHaveBeenCalledWith(file, userId);
      }
    });

    it('should handle different user IDs', async () => {
      const userIds = ['user-123', 'user-456', 'user-789'];

      for (const uid of userIds) {
        const expectedUrl = `https://example.com/storage/images/${uid}/123456_abc123.jpg`;
        uploadService.uploadImage.mockResolvedValue(expectedUrl);

        const result = await controller.uploadImage(mockFile, uid);

        expect(result).toBeInstanceOf(UploadResponseDto);
        expect(result.image_url).toBe(expectedUrl);
        expect(uploadService.uploadImage).toHaveBeenCalledWith(mockFile, uid);
      }
    });
  });
});
