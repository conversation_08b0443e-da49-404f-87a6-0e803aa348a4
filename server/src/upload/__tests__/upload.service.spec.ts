import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Readable } from 'stream';
import { UploadService } from '../upload.service';
import { SupabaseService } from '../../database/supabase.service';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';

describe('UploadService', () => {
  let service: UploadService;
  let supabaseService: jest.Mocked<SupabaseService>;
  let configService: jest.Mocked<ConfigService>;

  const mockStorageClient = {
    from: jest.fn().mockReturnThis(),
    upload: jest.fn(),
    getPublicUrl: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      storage: mockStorageClient,
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UploadService,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<UploadService>(UploadService);
    supabaseService = module.get(SupabaseService);
    configService = module.get(ConfigService);

    // Reset mocks
    jest.clearAllMocks();
    configService.get.mockReturnValue('images');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadImage', () => {
    const mockFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: 'test.jpg',
      encoding: '7bit',
      mimetype: 'image/jpeg',
      size: 1024 * 1024, // 1MB
      buffer: Buffer.from('fake image data'),
      destination: '',
      filename: '',
      path: '',
      stream: new Readable(),
    };

    const userId = 'user-123';

    it('should upload image successfully', async () => {
      const expectedUrl =
        'https://example.com/storage/images/user-123/123456_abc123.jpg';

      mockStorageClient.upload.mockResolvedValue({
        data: { path: 'user-123/123456_abc123.jpg' },
        error: null,
      });

      mockStorageClient.getPublicUrl.mockReturnValue({
        data: { publicUrl: expectedUrl },
      });

      const result = await service.uploadImage(mockFile, userId);

      expect(result).toBe(expectedUrl);
      expect(mockStorageClient.from).toHaveBeenCalledWith('images');
      expect(mockStorageClient.upload).toHaveBeenCalledWith(
        expect.stringMatching(/^user-123\/\d+_[a-z0-9]+\.jpg$/),
        mockFile.buffer,
        {
          contentType: 'image/jpeg',
          upsert: false,
        },
      );
      expect(mockStorageClient.getPublicUrl).toHaveBeenCalled();
    });

    it('should throw error when file is not provided', async () => {
      await expect(service.uploadImage(null as any, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'No file provided',
        ),
      );
    });

    it('should throw error when file is too large', async () => {
      const largeFile = {
        ...mockFile,
        size: 6 * 1024 * 1024, // 6MB (exceeds 5MB limit)
      };

      await expect(service.uploadImage(largeFile, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.FILE_TOO_LARGE,
          'File size exceeds maximum limit of 5MB',
        ),
      );
    });

    it('should throw error for invalid MIME type', async () => {
      const invalidFile = {
        ...mockFile,
        mimetype: 'text/plain',
      };

      await expect(service.uploadImage(invalidFile, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'Invalid file format. Allowed formats: image/jpeg, image/jpg, image/png, image/webp',
        ),
      );
    });

    it('should throw error for invalid file extension', async () => {
      const invalidFile = {
        ...mockFile,
        originalname: 'test.txt',
      };

      await expect(service.uploadImage(invalidFile, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'Invalid file extension. Allowed extensions: jpg, jpeg, png, webp',
        ),
      );
    });

    it('should throw error when file has no extension', async () => {
      const noExtensionFile = {
        ...mockFile,
        originalname: 'test',
      };

      await expect(
        service.uploadImage(noExtensionFile, userId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'File must have an extension',
        ),
      );
    });

    it('should throw error when file buffer is empty', async () => {
      const emptyFile = {
        ...mockFile,
        buffer: Buffer.alloc(0),
      };

      await expect(service.uploadImage(emptyFile, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'File content is empty',
        ),
      );
    });

    it('should throw error when Supabase upload fails', async () => {
      mockStorageClient.upload.mockResolvedValue({
        data: null,
        error: { message: 'Upload failed' },
      });

      await expect(service.uploadImage(mockFile, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.UPLOAD_FAILED,
          'Failed to upload image: Upload failed',
        ),
      );
    });

    it('should throw error when public URL is not available', async () => {
      mockStorageClient.upload.mockResolvedValue({
        data: { path: 'user-123/123456_abc123.jpg' },
        error: null,
      });

      mockStorageClient.getPublicUrl.mockReturnValue({
        data: { publicUrl: null },
      });

      await expect(service.uploadImage(mockFile, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.UPLOAD_FAILED,
          'Failed to get public URL for uploaded image',
        ),
      );
    });

    it('should handle different image formats', async () => {
      const formats = [
        { ext: 'png', mime: 'image/png' },
        { ext: 'webp', mime: 'image/webp' },
        { ext: 'jpeg', mime: 'image/jpeg' },
      ];

      for (const format of formats) {
        const file = {
          ...mockFile,
          originalname: `test.${format.ext}`,
          mimetype: format.mime,
        };

        mockStorageClient.upload.mockResolvedValue({
          data: { path: `user-123/123456_abc123.${format.ext}` },
          error: null,
        });

        mockStorageClient.getPublicUrl.mockReturnValue({
          data: { publicUrl: `https://example.com/test.${format.ext}` },
        });

        const result = await service.uploadImage(file, userId);
        expect(result).toContain(format.ext);
      }
    });
  });

  describe('deleteImage', () => {
    const userId = 'user-123';
    const imageUrl =
      'https://example.com/storage/v1/object/public/images/user-123/123456_abc123.jpg';

    it('should delete image successfully', async () => {
      mockStorageClient.remove.mockResolvedValue({
        data: null,
        error: null,
      });

      const result = await service.deleteImage(imageUrl, userId);

      expect(result).toBe(true);
      expect(mockStorageClient.from).toHaveBeenCalledWith('images');
      expect(mockStorageClient.remove).toHaveBeenCalledWith([
        'user-123/123456_abc123.jpg',
      ]);
    });

    it("should throw error when trying to delete another user's image", async () => {
      const otherUserImageUrl =
        'https://example.com/storage/v1/object/public/images/other-user/123456_abc123.jpg';

      await expect(
        service.deleteImage(otherUserImageUrl, userId),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.UNAUTHORIZED,
          'You can only delete your own images',
        ),
      );
    });

    it('should return false when Supabase delete fails', async () => {
      mockStorageClient.remove.mockResolvedValue({
        data: null,
        error: { message: 'Delete failed' },
      });

      const result = await service.deleteImage(imageUrl, userId);

      expect(result).toBe(false);
    });

    it('should throw error for invalid URL format', async () => {
      const invalidUrl = 'https://example.com/invalid-url';

      await expect(service.deleteImage(invalidUrl, userId)).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FILE_FORMAT,
          'Invalid image URL format',
        ),
      );
    });

    it('should handle unexpected errors gracefully', async () => {
      mockStorageClient.remove.mockRejectedValue(new Error('Unexpected error'));

      const result = await service.deleteImage(imageUrl, userId);

      expect(result).toBe(false);
    });
  });

  describe('private methods', () => {
    it('should generate unique file names', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        buffer: Buffer.from('fake image data'),
        destination: '',
        filename: '',
        path: '',
        stream: new Readable(),
      };

      const userId = 'user-123';

      mockStorageClient.upload.mockResolvedValue({
        data: { path: 'user-123/123456_abc123.jpg' },
        error: null,
      });

      mockStorageClient.getPublicUrl.mockReturnValue({
        data: { publicUrl: 'https://example.com/test.jpg' },
      });

      // Call upload twice to ensure different file names
      await service.uploadImage(mockFile, userId);
      await service.uploadImage(mockFile, userId);

      const calls = mockStorageClient.upload.mock.calls;
      expect(calls).toHaveLength(2);
      expect(calls[0][0]).not.toBe(calls[1][0]); // Different file names
      expect(calls[0][0]).toMatch(/^user-123\/\d+_[a-z0-9]+\.jpg$/);
      expect(calls[1][0]).toMatch(/^user-123\/\d+_[a-z0-9]+\.jpg$/);
    });
  });
});
