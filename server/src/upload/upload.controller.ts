import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { UploadResponseDto } from './dto/upload-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

@Controller('upload')
@UseGuards(JwtAuthGuard)
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('image')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser('id') userId: string,
  ): Promise<UploadResponseDto> {
    if (!file) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        'No file provided. Please upload an image file.',
      );
    }

    const imageUrl = await this.uploadService.uploadImage(file, userId);
    return new UploadResponseDto(imageUrl);
  }
}
