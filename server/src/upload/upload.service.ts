import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../database/supabase.service';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private readonly maxFileSize = 5 * 1024 * 1024; // 5MB
  private readonly allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
  ];
  private readonly allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 上传图片到Supabase Storage
   * @param file 上传的文件
   * @param userId 用户ID，用于生成唯一的文件路径
   * @returns 图片的公共URL
   */
  async uploadImage(
    file: Express.Multer.File,
    userId: string,
  ): Promise<string> {
    try {
      // 1. 验证文件
      this.validateImageFile(file);

      // 2. 生成唯一文件名
      const fileName = this.generateFileName(userId, file.originalname);

      // 3. 获取存储桶名称
      const bucketName = this.configService.get<string>(
        'supabase.storageBucket',
        'images',
      );

      // 4. 上传到Supabase Storage
      const { data, error } = await this.supabaseService.storage
        .from(bucketName)
        .upload(fileName, file.buffer, {
          contentType: file.mimetype,
          upsert: false, // 不覆盖已存在的文件
        });

      if (error) {
        this.logger.error('Failed to upload file to Supabase Storage', {
          error: error.message,
          fileName,
          userId,
        });
        throw new BusinessException(
          ErrorCode.UPLOAD_FAILED,
          `Failed to upload image: ${error.message}`,
        );
      }

      // 5. 获取公共URL
      const { data: urlData } = this.supabaseService.storage
        .from(bucketName)
        .getPublicUrl(fileName);

      if (!urlData?.publicUrl) {
        this.logger.error('Failed to get public URL for uploaded file', {
          fileName,
          userId,
        });
        throw new BusinessException(
          ErrorCode.UPLOAD_FAILED,
          'Failed to get public URL for uploaded image',
        );
      }

      this.logger.log('Image uploaded successfully', {
        fileName,
        userId,
        publicUrl: urlData.publicUrl,
      });

      return urlData.publicUrl;
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }

      this.logger.error('Unexpected error during image upload', {
        error: error.message,
        userId,
        originalName: file?.originalname,
      });

      throw new BusinessException(
        ErrorCode.UPLOAD_FAILED,
        'An unexpected error occurred during image upload',
      );
    }
  }

  /**
   * 验证图片文件
   * @param file 上传的文件
   */
  private validateImageFile(file: Express.Multer.File): void {
    if (!file) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        'No file provided',
      );
    }

    // 验证文件大小
    if (file.size > this.maxFileSize) {
      throw new BusinessException(
        ErrorCode.FILE_TOO_LARGE,
        `File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`,
      );
    }

    // 验证MIME类型
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        `Invalid file format. Allowed formats: ${this.allowedMimeTypes.join(', ')}`,
      );
    }

    // 验证文件扩展名
    const fileExtension = this.getFileExtension(file.originalname);
    if (!this.allowedExtensions.includes(fileExtension.toLowerCase())) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        `Invalid file extension. Allowed extensions: ${this.allowedExtensions.join(', ')}`,
      );
    }

    // 验证文件内容不为空
    if (!file.buffer || file.buffer.length === 0) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        'File content is empty',
      );
    }
  }

  /**
   * 生成唯一文件名
   * @param userId 用户ID
   * @param originalName 原始文件名
   * @returns 唯一的文件名
   */
  private generateFileName(userId: string, originalName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = this.getFileExtension(originalName);

    return `${userId}/${timestamp}_${randomString}.${extension}`;
  }

  /**
   * 获取文件扩展名
   * @param filename 文件名
   * @returns 文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        'File must have an extension',
      );
    }
    return filename.substring(lastDotIndex + 1);
  }

  /**
   * 删除图片文件
   * @param imageUrl 图片URL
   * @param userId 用户ID（用于权限验证）
   * @returns 是否删除成功
   */
  async deleteImage(imageUrl: string, userId: string): Promise<boolean> {
    try {
      // 从URL中提取文件路径
      const fileName = this.extractFileNameFromUrl(imageUrl);

      // 验证文件是否属于该用户
      if (!fileName.startsWith(`${userId}/`)) {
        throw new BusinessException(
          ErrorCode.UNAUTHORIZED,
          'You can only delete your own images',
        );
      }

      const bucketName = this.configService.get<string>(
        'supabase.storageBucket',
        'images',
      );

      const { error } = await this.supabaseService.storage
        .from(bucketName)
        .remove([fileName]);

      if (error) {
        this.logger.error('Failed to delete file from Supabase Storage', {
          error: error.message,
          fileName,
          userId,
        });
        return false;
      }

      this.logger.log('Image deleted successfully', {
        fileName,
        userId,
      });

      return true;
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }

      this.logger.error('Unexpected error during image deletion', {
        error: error.message,
        imageUrl,
        userId,
      });

      return false;
    }
  }

  /**
   * 从URL中提取文件名
   * @param url 图片URL
   * @returns 文件名
   */
  private extractFileNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // Supabase Storage URL格式: /storage/v1/object/public/{bucket}/{fileName}
      const bucketIndex = pathParts.findIndex((part) => part === 'public');
      if (bucketIndex !== -1 && bucketIndex + 2 < pathParts.length) {
        return pathParts.slice(bucketIndex + 2).join('/');
      }
      throw new Error('Invalid URL format');
    } catch (error) {
      throw new BusinessException(
        ErrorCode.INVALID_FILE_FORMAT,
        'Invalid image URL format',
      );
    }
  }
}
