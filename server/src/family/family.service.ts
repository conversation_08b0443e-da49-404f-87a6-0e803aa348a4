import { Injectable } from '@nestjs/common';
import { FamilyRepository } from './family.repository';
import { BabyRepository } from '../baby/baby.repository';
import { UserRepository } from '../user/user.repository';
import {
  IInvitation,
  IFamilyMember,
  ICreateInvitationDto,
  IAcceptInvitationDto,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode, FamilyRole } from '../common/enums';

@Injectable()
export class FamilyService {
  constructor(
    private readonly familyRepository: FamilyRepository,
    private readonly babyRepository: BabyRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * 创建邀请码
   * 需求: 4.1, 4.6
   */
  async createInvitation(
    inviterId: string,
    createInvitationDto: ICreateInvitationDto,
  ): Promise<IInvitation> {
    const { baby_id, phone } = createInvitationDto;

    // 验证宝宝是否存在且用户有权限
    const baby = await this.babyRepository.findById(baby_id);
    if (!baby) {
      throw new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found');
    }

    // 检查用户是否有权限邀请（必须是宝宝的所有者或家庭成员）
    const familyMember = await this.familyRepository.findFamilyMember(
      baby_id,
      inviterId,
    );
    if (!familyMember && baby.owner_id !== inviterId) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'No permission to invite for this baby',
      );
    }

    // 创建邀请
    return await this.familyRepository.createInvitation({
      baby_id,
      inviter_id: inviterId,
      phone,
    });
  }

  /**
   * 接受邀请
   * 需求: 4.1, 4.2, 4.3, 4.4, 4.5
   */
  async acceptInvitation(
    userId: string,
    acceptInvitationDto: IAcceptInvitationDto,
  ): Promise<IFamilyMember> {
    const { invitation_code, role } = acceptInvitationDto;

    // 验证邀请码
    const validation = await this.familyRepository.validateInvitation(
      invitation_code,
      userId,
    );
    if (!validation.valid) {
      switch (validation.reason) {
        case 'Invitation not found':
          throw new BusinessException(
            ErrorCode.INVITATION_NOT_FOUND,
            'Invitation not found',
          );
        case 'Invitation already used':
          throw new BusinessException(
            ErrorCode.INVITATION_ALREADY_USED,
            'Invitation has already been used',
          );
        case 'Invitation expired':
          throw new BusinessException(
            ErrorCode.INVITATION_EXPIRED,
            'Invitation has expired',
          );
        case 'Already a family member':
          throw new BusinessException(
            ErrorCode.ALREADY_FAMILY_MEMBER,
            'User is already a family member for this baby',
          );
        default:
          throw new BusinessException(
            ErrorCode.INVITATION_NOT_FOUND,
            'Invalid invitation',
          );
      }
    }

    const invitation = validation.invitation!;

    // 验证角色
    if (!Object.values(FamilyRole).includes(role)) {
      throw new BusinessException(
        ErrorCode.INVALID_FAMILY_ROLE,
        'Invalid family role',
      );
    }

    // 创建家庭成员关系
    const familyMember = await this.familyRepository.createFamilyMember({
      baby_id: invitation.baby_id,
      user_id: userId,
      role,
      is_owner: false,
    });

    // 标记邀请为已使用
    await this.familyRepository.useInvitation(invitation_code, userId);

    return familyMember;
  }

  /**
   * 获取宝宝的家庭成员列表
   * 需求: 4.4
   */
  async getFamilyMembers(
    userId: string,
    babyId: string,
  ): Promise<IFamilyMember[]> {
    // 验证用户是否有权限查看该宝宝的家庭成员
    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found');
    }

    // 检查权限：必须是宝宝的所有者或家庭成员
    const familyMember = await this.familyRepository.findFamilyMember(
      babyId,
      userId,
    );
    if (!familyMember && baby.owner_id !== userId) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'No permission to view family members for this baby',
      );
    }

    return await this.familyRepository.findFamilyMembersByBaby(babyId);
  }

  /**
   * 移除家庭成员
   * 需求: 4.5
   */
  async removeFamilyMember(
    requesterId: string,
    babyId: string,
    memberUserId: string,
  ): Promise<boolean> {
    // 验证宝宝是否存在
    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found');
    }

    // 只有宝宝的所有者可以移除家庭成员
    if (baby.owner_id !== requesterId) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can remove family members',
      );
    }

    // 不能移除自己（所有者）
    if (requesterId === memberUserId) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Cannot remove yourself as baby owner',
      );
    }

    // 验证要移除的用户确实是家庭成员
    const familyMember = await this.familyRepository.findFamilyMember(
      babyId,
      memberUserId,
    );
    if (!familyMember) {
      throw new BusinessException(
        ErrorCode.INVITATION_NOT_FOUND,
        'User is not a family member for this baby',
      );
    }

    return await this.familyRepository.removeFamilyMember(babyId, memberUserId);
  }

  /**
   * 获取用户发出的邀请列表
   * 需求: 4.6
   */
  async getInvitationsByInviter(inviterId: string): Promise<IInvitation[]> {
    return await this.familyRepository.findInvitationsByInviter(inviterId);
  }

  /**
   * 获取用户的家庭成员关系
   * 需求: 4.4
   */
  async getUserFamilyMemberships(userId: string): Promise<IFamilyMember[]> {
    return await this.familyRepository.findFamilyMembersByUser(userId);
  }

  /**
   * 清理过期邀请（定时任务使用）
   * 需求: 4.6
   */
  async cleanupExpiredInvitations(): Promise<number> {
    return await this.familyRepository.cleanupExpiredInvitations();
  }

  /**
   * 验证邀请码（用于前端预览）
   * 需求: 4.1
   */
  async validateInvitationCode(
    invitationCode: string,
    userId: string,
  ): Promise<{
    valid: boolean;
    invitation?: IInvitation;
    reason?: string;
  }> {
    return await this.familyRepository.validateInvitation(
      invitationCode,
      userId,
    );
  }
}
