import { Module, forwardRef } from '@nestjs/common';
import { FamilyController } from './family.controller';
import { FamilyService } from './family.service';
import { FamilyRepository } from './family.repository';
import { BabyModule } from '../baby/baby.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [forwardRef(() => BabyModule), forwardRef(() => UserModule)],
  controllers: [FamilyController],
  providers: [FamilyService, FamilyRepository],
  exports: [FamilyService, FamilyRepository],
})
export class FamilyModule {}
