import { Test, TestingModule } from '@nestjs/testing';
import { FamilyService } from '../family.service';
import { FamilyRepository } from '../family.repository';
import { BabyRepository } from '../../baby/baby.repository';
import { UserRepository } from '../../user/user.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode, FamilyRole, Gender } from '../../common/enums';
import {
  IInvitation,
  IFamilyMember,
  IBaby,
  ICreateInvitationDto,
  IAcceptInvitationDto,
} from '../../common/interfaces/entities';

describe('FamilyService - Invitation and Acceptance Workflows', () => {
  let service: FamilyService;
  let familyRepository: jest.Mocked<FamilyRepository>;
  let babyRepository: jest.Mocked<BabyRepository>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockBaby: IBaby = {
    id: 'baby-1',
    owner_id: 'user-1',
    nickname: '小宝',
    birth_date: new Date('2023-01-01'),
    gender: Gender.MALE,
    avatar_url: 'https://example.com/avatar.jpg',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockInvitation: IInvitation = {
    id: 'invitation-1',
    baby_id: 'baby-1',
    inviter_id: 'user-1',
    invitation_code: 'ABC123',
    phone: '***********',
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    used_at: undefined,
    used_by: undefined,
    created_at: new Date(),
  };

  const mockFamilyMember: IFamilyMember = {
    id: 'member-1',
    baby_id: 'baby-1',
    user_id: 'user-2',
    role: FamilyRole.MOTHER,
    is_owner: false,
    created_at: new Date(),
  };

  beforeEach(async () => {
    const mockFamilyRepository = {
      createInvitation: jest.fn(),
      validateInvitation: jest.fn(),
      createFamilyMember: jest.fn(),
      useInvitation: jest.fn(),
      findFamilyMember: jest.fn(),
      findFamilyMembersByBaby: jest.fn(),
      findFamilyMembersByUser: jest.fn(),
      removeFamilyMember: jest.fn(),
      findInvitationsByInviter: jest.fn(),
      cleanupExpiredInvitations: jest.fn(),
    };

    const mockBabyRepository = {
      findById: jest.fn(),
    };

    const mockUserRepository = {
      findById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FamilyService,
        {
          provide: FamilyRepository,
          useValue: mockFamilyRepository,
        },
        {
          provide: BabyRepository,
          useValue: mockBabyRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<FamilyService>(FamilyService);
    familyRepository = module.get(FamilyRepository);
    babyRepository = module.get(BabyRepository);
    userRepository = module.get(UserRepository);
  });

  describe('Complete Invitation Workflow - 需求 4.1, 4.6', () => {
    it('should create invitation with phone number successfully', async () => {
      const createInvitationDto: ICreateInvitationDto = {
        baby_id: 'baby-1',
        phone: '***********',
      };

      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);
      familyRepository.createInvitation.mockResolvedValue(mockInvitation);

      const result = await service.createInvitation(
        'user-1',
        createInvitationDto,
      );

      expect(result).toEqual(mockInvitation);
      expect(result.invitation_code).toBeDefined();
      expect(result.expires_at).toBeInstanceOf(Date);
      expect(result.phone).toBe('***********');
      expect(familyRepository.createInvitation).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        inviter_id: 'user-1',
        phone: '***********',
      });
    });

    it('should create invitation without phone number successfully', async () => {
      const createInvitationDto: ICreateInvitationDto = {
        baby_id: 'baby-1',
      };

      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);
      familyRepository.createInvitation.mockResolvedValue({
        ...mockInvitation,
        phone: undefined,
      });

      const result = await service.createInvitation(
        'user-1',
        createInvitationDto,
      );

      expect(result.phone).toBeUndefined();
      expect(familyRepository.createInvitation).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        inviter_id: 'user-1',
        phone: undefined,
      });
    });

    it('should validate invitation expiry correctly', async () => {
      const expiredInvitation = {
        ...mockInvitation,
        expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      };

      familyRepository.validateInvitation.mockResolvedValue({
        valid: false,
        invitation: expiredInvitation,
        reason: 'Invitation expired',
      });

      const result = await service.validateInvitationCode('ABC123', 'user-2');

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Invitation expired');
    });
  });

  describe('Complete Acceptance Workflow - 需求 4.1, 4.2, 4.3, 4.4, 4.5', () => {
    it('should accept invitation and create family member with FATHER role', async () => {
      const acceptInvitationDto: IAcceptInvitationDto = {
        invitation_code: 'ABC123',
        role: FamilyRole.FATHER,
      };

      const expectedFamilyMember = {
        ...mockFamilyMember,
        role: FamilyRole.FATHER,
      };

      familyRepository.validateInvitation.mockResolvedValue({
        valid: true,
        invitation: mockInvitation,
      });
      familyRepository.createFamilyMember.mockResolvedValue(
        expectedFamilyMember,
      );
      familyRepository.useInvitation.mockResolvedValue({
        ...mockInvitation,
        used_at: new Date(),
        used_by: 'user-2',
      });

      const result = await service.acceptInvitation(
        'user-2',
        acceptInvitationDto,
      );

      expect(result.role).toBe(FamilyRole.FATHER);
      expect(result.is_owner).toBe(false);
      expect(familyRepository.createFamilyMember).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        user_id: 'user-2',
        role: FamilyRole.FATHER,
        is_owner: false,
      });
      expect(familyRepository.useInvitation).toHaveBeenCalledWith(
        'ABC123',
        'user-2',
      );
    });

    it('should accept invitation and create family member with MOTHER role', async () => {
      const acceptInvitationDto: IAcceptInvitationDto = {
        invitation_code: 'ABC123',
        role: FamilyRole.MOTHER,
      };

      familyRepository.validateInvitation.mockResolvedValue({
        valid: true,
        invitation: mockInvitation,
      });
      familyRepository.createFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.useInvitation.mockResolvedValue(mockInvitation);

      const result = await service.acceptInvitation(
        'user-2',
        acceptInvitationDto,
      );

      expect(result.role).toBe(FamilyRole.MOTHER);
      expect(familyRepository.createFamilyMember).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        user_id: 'user-2',
        role: FamilyRole.MOTHER,
        is_owner: false,
      });
    });

    it('should accept invitation and create family member with OTHER_CAREGIVER role', async () => {
      const acceptInvitationDto: IAcceptInvitationDto = {
        invitation_code: 'ABC123',
        role: FamilyRole.OTHER_CAREGIVER,
      };

      const expectedFamilyMember = {
        ...mockFamilyMember,
        role: FamilyRole.OTHER_CAREGIVER,
      };

      familyRepository.validateInvitation.mockResolvedValue({
        valid: true,
        invitation: mockInvitation,
      });
      familyRepository.createFamilyMember.mockResolvedValue(
        expectedFamilyMember,
      );
      familyRepository.useInvitation.mockResolvedValue(mockInvitation);

      const result = await service.acceptInvitation(
        'user-2',
        acceptInvitationDto,
      );

      expect(result.role).toBe(FamilyRole.OTHER_CAREGIVER);
      expect(familyRepository.createFamilyMember).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        user_id: 'user-2',
        role: FamilyRole.OTHER_CAREGIVER,
        is_owner: false,
      });
    });

    it('should handle invitation validation errors correctly', async () => {
      const acceptInvitationDto: IAcceptInvitationDto = {
        invitation_code: 'INVALID',
        role: FamilyRole.FATHER,
      };

      familyRepository.validateInvitation.mockResolvedValue({
        valid: false,
        reason: 'Invitation not found',
      });

      await expect(
        service.acceptInvitation('user-2', acceptInvitationDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVITATION_NOT_FOUND,
          'Invitation not found',
        ),
      );

      expect(familyRepository.createFamilyMember).not.toHaveBeenCalled();
      expect(familyRepository.useInvitation).not.toHaveBeenCalled();
    });
  });

  describe('Family Member Permission Verification - 需求 4.2, 4.3, 4.4, 4.5', () => {
    it('should allow family member to view baby information', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);
      familyRepository.findFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.findFamilyMembersByBaby.mockResolvedValue([
        mockFamilyMember,
      ]);

      const result = await service.getFamilyMembers('user-2', 'baby-1');

      expect(result).toEqual([mockFamilyMember]);
      expect(familyRepository.findFamilyMember).toHaveBeenCalledWith(
        'baby-1',
        'user-2',
      );
    });

    it('should allow baby owner to remove family members', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.removeFamilyMember.mockResolvedValue(true);

      const result = await service.removeFamilyMember(
        'user-1',
        'baby-1',
        'user-2',
      );

      expect(result).toBe(true);
      expect(familyRepository.removeFamilyMember).toHaveBeenCalledWith(
        'baby-1',
        'user-2',
      );
    });

    it('should prevent non-owner from removing family members', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);

      await expect(
        service.removeFamilyMember('user-2', 'baby-1', 'user-3'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Only baby owner can remove family members',
        ),
      );

      expect(familyRepository.removeFamilyMember).not.toHaveBeenCalled();
    });

    it('should prevent owner from removing themselves', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);

      await expect(
        service.removeFamilyMember('user-1', 'baby-1', 'user-1'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Cannot remove yourself as baby owner',
        ),
      );

      expect(familyRepository.removeFamilyMember).not.toHaveBeenCalled();
    });
  });

  describe('Invitation Management - 需求 4.6', () => {
    it('should get all invitations sent by user', async () => {
      const invitations = [
        mockInvitation,
        {
          ...mockInvitation,
          id: 'invitation-2',
          invitation_code: 'DEF456',
        },
      ];

      familyRepository.findInvitationsByInviter.mockResolvedValue(invitations);

      const result = await service.getInvitationsByInviter('user-1');

      expect(result).toEqual(invitations);
      expect(result).toHaveLength(2);
      expect(familyRepository.findInvitationsByInviter).toHaveBeenCalledWith(
        'user-1',
      );
    });

    it('should cleanup expired invitations', async () => {
      familyRepository.cleanupExpiredInvitations.mockResolvedValue(3);

      const result = await service.cleanupExpiredInvitations();

      expect(result).toBe(3);
      expect(familyRepository.cleanupExpiredInvitations).toHaveBeenCalled();
    });

    it('should validate invitation code and return detailed information', async () => {
      const validationResult = {
        valid: true,
        invitation: mockInvitation,
      };

      familyRepository.validateInvitation.mockResolvedValue(validationResult);

      const result = await service.validateInvitationCode('ABC123', 'user-2');

      expect(result.valid).toBe(true);
      expect(result.invitation).toEqual(mockInvitation);
      expect(result.reason).toBeUndefined();
    });

    it('should validate invitation code and return error reason', async () => {
      const validationResult = {
        valid: false,
        invitation: mockInvitation,
        reason: 'Invitation expired',
      };

      familyRepository.validateInvitation.mockResolvedValue(validationResult);

      const result = await service.validateInvitationCode('ABC123', 'user-2');

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Invitation expired');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle multiple family members for same baby', async () => {
      const familyMembers = [
        mockFamilyMember,
        {
          ...mockFamilyMember,
          id: 'member-2',
          user_id: 'user-3',
          role: FamilyRole.FATHER,
        },
        {
          ...mockFamilyMember,
          id: 'member-3',
          user_id: 'user-4',
          role: FamilyRole.OTHER_CAREGIVER,
        },
      ];

      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);
      familyRepository.findFamilyMembersByBaby.mockResolvedValue(familyMembers);

      const result = await service.getFamilyMembers('user-1', 'baby-1');

      expect(result).toHaveLength(3);
      expect(result.map((m) => m.role)).toContain(FamilyRole.MOTHER);
      expect(result.map((m) => m.role)).toContain(FamilyRole.FATHER);
      expect(result.map((m) => m.role)).toContain(FamilyRole.OTHER_CAREGIVER);
    });

    it('should handle user with multiple family memberships', async () => {
      const familyMemberships = [
        mockFamilyMember,
        {
          ...mockFamilyMember,
          id: 'member-2',
          baby_id: 'baby-2',
          role: FamilyRole.FATHER,
        },
      ];

      familyRepository.findFamilyMembersByUser.mockResolvedValue(
        familyMemberships,
      );

      const result = await service.getUserFamilyMemberships('user-2');

      expect(result).toHaveLength(2);
      expect(result.map((m) => m.baby_id)).toContain('baby-1');
      expect(result.map((m) => m.baby_id)).toContain('baby-2');
    });

    it('should handle invitation with missing baby reference', async () => {
      const createInvitationDto: ICreateInvitationDto = {
        baby_id: 'non-existent-baby',
        phone: '***********',
      };

      babyRepository.findById.mockResolvedValue(null);

      await expect(
        service.createInvitation('user-1', createInvitationDto),
      ).rejects.toThrow(
        new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found'),
      );

      expect(familyRepository.createInvitation).not.toHaveBeenCalled();
    });
  });
});
