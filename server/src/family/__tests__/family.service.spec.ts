import { Test, TestingModule } from '@nestjs/testing';
import { FamilyService } from '../family.service';
import { FamilyRepository } from '../family.repository';
import { BabyRepository } from '../../baby/baby.repository';
import { UserRepository } from '../../user/user.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode, FamilyRole, Gender } from '../../common/enums';
import {
  IInvitation,
  IFamilyMember,
  IBaby,
  ICreateInvitationDto,
  IAcceptInvitationDto,
} from '../../common/interfaces/entities';

describe('FamilyService', () => {
  let service: FamilyService;
  let familyRepository: jest.Mocked<FamilyRepository>;
  let babyRepository: jest.Mocked<BabyRepository>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockBaby: IBaby = {
    id: 'baby-1',
    owner_id: 'user-1',
    nickname: '小宝',
    birth_date: new Date('2023-01-01'),
    gender: Gender.MALE,
    avatar_url: 'https://example.com/avatar.jpg',
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockInvitation: IInvitation = {
    id: 'invitation-1',
    baby_id: 'baby-1',
    inviter_id: 'user-1',
    invitation_code: 'ABC123',
    phone: '***********',
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    used_at: undefined,
    used_by: undefined,
    created_at: new Date(),
  };

  const mockFamilyMember: IFamilyMember = {
    id: 'member-1',
    baby_id: 'baby-1',
    user_id: 'user-2',
    role: FamilyRole.MOTHER,
    is_owner: false,
    created_at: new Date(),
  };

  beforeEach(async () => {
    const mockFamilyRepository = {
      createInvitation: jest.fn(),
      validateInvitation: jest.fn(),
      createFamilyMember: jest.fn(),
      useInvitation: jest.fn(),
      findFamilyMember: jest.fn(),
      findFamilyMembersByBaby: jest.fn(),
      findFamilyMembersByUser: jest.fn(),
      removeFamilyMember: jest.fn(),
      findInvitationsByInviter: jest.fn(),
      cleanupExpiredInvitations: jest.fn(),
    };

    const mockBabyRepository = {
      findById: jest.fn(),
    };

    const mockUserRepository = {
      findById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FamilyService,
        {
          provide: FamilyRepository,
          useValue: mockFamilyRepository,
        },
        {
          provide: BabyRepository,
          useValue: mockBabyRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<FamilyService>(FamilyService);
    familyRepository = module.get(FamilyRepository);
    babyRepository = module.get(BabyRepository);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createInvitation', () => {
    const createInvitationDto: ICreateInvitationDto = {
      baby_id: 'baby-1',
      phone: '***********',
    };

    it('should create invitation successfully when user is baby owner', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);
      familyRepository.createInvitation.mockResolvedValue(mockInvitation);

      const result = await service.createInvitation(
        'user-1',
        createInvitationDto,
      );

      expect(result).toEqual(mockInvitation);
      expect(babyRepository.findById).toHaveBeenCalledWith('baby-1');
      expect(familyRepository.createInvitation).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        inviter_id: 'user-1',
        phone: '***********',
      });
    });

    it('should create invitation successfully when user is family member', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);
      familyRepository.findFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.createInvitation.mockResolvedValue(mockInvitation);

      const result = await service.createInvitation(
        'user-1',
        createInvitationDto,
      );

      expect(result).toEqual(mockInvitation);
      expect(familyRepository.findFamilyMember).toHaveBeenCalledWith(
        'baby-1',
        'user-1',
      );
    });

    it('should throw error when baby not found', async () => {
      babyRepository.findById.mockResolvedValue(null);

      await expect(
        service.createInvitation('user-1', createInvitationDto),
      ).rejects.toThrow(
        new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found'),
      );
    });

    it('should throw error when user has no permission', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);

      await expect(
        service.createInvitation('user-1', createInvitationDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'No permission to invite for this baby',
        ),
      );
    });
  });

  describe('acceptInvitation', () => {
    const acceptInvitationDto: IAcceptInvitationDto = {
      invitation_code: 'ABC123',
      role: FamilyRole.FATHER,
    };

    it('should accept invitation successfully', async () => {
      familyRepository.validateInvitation.mockResolvedValue({
        valid: true,
        invitation: mockInvitation,
      });
      familyRepository.createFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.useInvitation.mockResolvedValue(mockInvitation);

      const result = await service.acceptInvitation(
        'user-2',
        acceptInvitationDto,
      );

      expect(result).toEqual(mockFamilyMember);
      expect(familyRepository.validateInvitation).toHaveBeenCalledWith(
        'ABC123',
        'user-2',
      );
      expect(familyRepository.createFamilyMember).toHaveBeenCalledWith({
        baby_id: 'baby-1',
        user_id: 'user-2',
        role: FamilyRole.FATHER,
        is_owner: false,
      });
      expect(familyRepository.useInvitation).toHaveBeenCalledWith(
        'ABC123',
        'user-2',
      );
    });

    it('should throw error when invitation not found', async () => {
      familyRepository.validateInvitation.mockResolvedValue({
        valid: false,
        reason: 'Invitation not found',
      });

      await expect(
        service.acceptInvitation('user-2', acceptInvitationDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVITATION_NOT_FOUND,
          'Invitation not found',
        ),
      );
    });

    it('should throw error when invitation already used', async () => {
      familyRepository.validateInvitation.mockResolvedValue({
        valid: false,
        invitation: mockInvitation,
        reason: 'Invitation already used',
      });

      await expect(
        service.acceptInvitation('user-2', acceptInvitationDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVITATION_ALREADY_USED,
          'Invitation has already been used',
        ),
      );
    });

    it('should throw error when invitation expired', async () => {
      familyRepository.validateInvitation.mockResolvedValue({
        valid: false,
        invitation: mockInvitation,
        reason: 'Invitation expired',
      });

      await expect(
        service.acceptInvitation('user-2', acceptInvitationDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVITATION_EXPIRED,
          'Invitation has expired',
        ),
      );
    });

    it('should throw error when already family member', async () => {
      familyRepository.validateInvitation.mockResolvedValue({
        valid: false,
        invitation: mockInvitation,
        reason: 'Already a family member',
      });

      await expect(
        service.acceptInvitation('user-2', acceptInvitationDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.ALREADY_FAMILY_MEMBER,
          'User is already a family member for this baby',
        ),
      );
    });

    it('should throw error when invalid role', async () => {
      const invalidRoleDto = {
        ...acceptInvitationDto,
        role: 999 as FamilyRole,
      };
      familyRepository.validateInvitation.mockResolvedValue({
        valid: true,
        invitation: mockInvitation,
      });

      await expect(
        service.acceptInvitation('user-2', invalidRoleDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVALID_FAMILY_ROLE,
          'Invalid family role',
        ),
      );
    });
  });

  describe('getFamilyMembers', () => {
    it('should get family members successfully when user is baby owner', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);
      familyRepository.findFamilyMembersByBaby.mockResolvedValue([
        mockFamilyMember,
      ]);

      const result = await service.getFamilyMembers('user-1', 'baby-1');

      expect(result).toEqual([mockFamilyMember]);
      expect(babyRepository.findById).toHaveBeenCalledWith('baby-1');
      expect(familyRepository.findFamilyMembersByBaby).toHaveBeenCalledWith(
        'baby-1',
      );
    });

    it('should get family members successfully when user is family member', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);
      familyRepository.findFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.findFamilyMembersByBaby.mockResolvedValue([
        mockFamilyMember,
      ]);

      const result = await service.getFamilyMembers('user-1', 'baby-1');

      expect(result).toEqual([mockFamilyMember]);
      expect(familyRepository.findFamilyMember).toHaveBeenCalledWith(
        'baby-1',
        'user-1',
      );
    });

    it('should throw error when baby not found', async () => {
      babyRepository.findById.mockResolvedValue(null);

      await expect(
        service.getFamilyMembers('user-1', 'baby-1'),
      ).rejects.toThrow(
        new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found'),
      );
    });

    it('should throw error when user has no permission', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);

      await expect(
        service.getFamilyMembers('user-1', 'baby-1'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'No permission to view family members for this baby',
        ),
      );
    });
  });

  describe('removeFamilyMember', () => {
    it('should remove family member successfully', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(mockFamilyMember);
      familyRepository.removeFamilyMember.mockResolvedValue(true);

      const result = await service.removeFamilyMember(
        'user-1',
        'baby-1',
        'user-2',
      );

      expect(result).toBe(true);
      expect(babyRepository.findById).toHaveBeenCalledWith('baby-1');
      expect(familyRepository.findFamilyMember).toHaveBeenCalledWith(
        'baby-1',
        'user-2',
      );
      expect(familyRepository.removeFamilyMember).toHaveBeenCalledWith(
        'baby-1',
        'user-2',
      );
    });

    it('should throw error when baby not found', async () => {
      babyRepository.findById.mockResolvedValue(null);

      await expect(
        service.removeFamilyMember('user-1', 'baby-1', 'user-2'),
      ).rejects.toThrow(
        new BusinessException(ErrorCode.BABY_NOT_FOUND, 'Baby not found'),
      );
    });

    it('should throw error when user is not baby owner', async () => {
      const otherBaby = { ...mockBaby, owner_id: 'other-user' };
      babyRepository.findById.mockResolvedValue(otherBaby);

      await expect(
        service.removeFamilyMember('user-1', 'baby-1', 'user-2'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Only baby owner can remove family members',
        ),
      );
    });

    it('should throw error when trying to remove self', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);

      await expect(
        service.removeFamilyMember('user-1', 'baby-1', 'user-1'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Cannot remove yourself as baby owner',
        ),
      );
    });

    it('should throw error when user is not a family member', async () => {
      babyRepository.findById.mockResolvedValue(mockBaby);
      familyRepository.findFamilyMember.mockResolvedValue(null);

      await expect(
        service.removeFamilyMember('user-1', 'baby-1', 'user-2'),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.INVITATION_NOT_FOUND,
          'User is not a family member for this baby',
        ),
      );
    });
  });

  describe('getInvitationsByInviter', () => {
    it('should get invitations by inviter successfully', async () => {
      familyRepository.findInvitationsByInviter.mockResolvedValue([
        mockInvitation,
      ]);

      const result = await service.getInvitationsByInviter('user-1');

      expect(result).toEqual([mockInvitation]);
      expect(familyRepository.findInvitationsByInviter).toHaveBeenCalledWith(
        'user-1',
      );
    });
  });

  describe('getUserFamilyMemberships', () => {
    it('should get user family memberships successfully', async () => {
      familyRepository.findFamilyMembersByUser.mockResolvedValue([
        mockFamilyMember,
      ]);

      const result = await service.getUserFamilyMemberships('user-1');

      expect(result).toEqual([mockFamilyMember]);
      expect(familyRepository.findFamilyMembersByUser).toHaveBeenCalledWith(
        'user-1',
      );
    });
  });

  describe('cleanupExpiredInvitations', () => {
    it('should cleanup expired invitations successfully', async () => {
      familyRepository.cleanupExpiredInvitations.mockResolvedValue(5);

      const result = await service.cleanupExpiredInvitations();

      expect(result).toBe(5);
      expect(familyRepository.cleanupExpiredInvitations).toHaveBeenCalled();
    });
  });

  describe('validateInvitationCode', () => {
    it('should validate invitation code successfully', async () => {
      const validationResult = {
        valid: true,
        invitation: mockInvitation,
      };
      familyRepository.validateInvitation.mockResolvedValue(validationResult);

      const result = await service.validateInvitationCode('ABC123', 'user-2');

      expect(result).toEqual(validationResult);
      expect(familyRepository.validateInvitation).toHaveBeenCalledWith(
        'ABC123',
        'user-2',
      );
    });
  });
});
