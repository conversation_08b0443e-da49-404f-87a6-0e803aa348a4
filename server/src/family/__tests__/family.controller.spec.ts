import { Test, TestingModule } from '@nestjs/testing';
import { FamilyController } from '../family.controller';
import { FamilyService } from '../family.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CreateInvitationDto } from '../dto/create-invitation.dto';
import { AcceptInvitationDto } from '../dto/accept-invitation.dto';
import { InvitationResponseDto } from '../dto/invitation-response.dto';
import { FamilyMemberResponseDto } from '../dto/family-member-response.dto';
import { IUser } from '../../common/interfaces/entities';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import { HttpStatus } from '@nestjs/common';

describe('FamilyController', () => {
  let controller: FamilyController;
  let familyService: jest.Mocked<FamilyService>;

  const mockUser: IUser = {
    id: 'user-id-1',
    openid: 'test-openid',
    username: 'testuser',
    phone: '***********',
    avatar_url: 'https://example.com/avatar.jpg',
    default_baby_id: 'baby-id-1',
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
  };

  const mockInvitationResponse: InvitationResponseDto = {
    id: 'invitation-id-1',
    baby_id: 'baby-id-1',
    inviter_id: 'user-id-1',
    invitation_code: 'ABC123',
    phone: '***********',
    expires_at: new Date('2024-02-01'),
    created_at: new Date('2024-01-01'),
  };

  const mockFamilyMemberResponse: FamilyMemberResponseDto = {
    id: 'family-member-id-1',
    baby_id: 'baby-id-1',
    user_id: 'user-id-2',
    role: 1, // 爸爸
    is_owner: false,
    user: {
      id: 'user-id-2',
      openid: 'test-openid-2',
      username: 'familymember',
      phone: '***********',
      avatar_url: 'https://example.com/member-avatar.jpg',
      created_at: new Date('2024-01-01'),
      updated_at: new Date('2024-01-01'),
    },
    created_at: new Date('2024-01-01'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FamilyController],
      providers: [
        {
          provide: FamilyService,
          useValue: {
            createInvitation: jest.fn(),
            acceptInvitation: jest.fn(),
            getFamilyMembers: jest.fn(),
            removeFamilyMember: jest.fn(),
            getInvitationsByInviter: jest.fn(),
            getUserFamilyMemberships: jest.fn(),
            validateInvitationCode: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<FamilyController>(FamilyController);
    familyService = module.get(FamilyService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createInvitation', () => {
    it('should create invitation successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const createInvitationDto: CreateInvitationDto = {
        baby_id: 'baby-id-1',
        phone: '***********',
      };
      familyService.createInvitation.mockResolvedValue(mockInvitationResponse);

      // Act
      const result = await controller.createInvitation(
        req,
        createInvitationDto,
      );

      // Assert
      expect(familyService.createInvitation).toHaveBeenCalledWith(
        mockUser.id,
        createInvitationDto,
      );
      expect(result).toEqual(mockInvitationResponse);
    });

    it('should create invitation without phone', async () => {
      // Arrange
      const req = { user: mockUser };
      const createInvitationDto: CreateInvitationDto = {
        baby_id: 'baby-id-1',
      };
      const invitationWithoutPhone = {
        ...mockInvitationResponse,
        phone: undefined,
      };
      familyService.createInvitation.mockResolvedValue(invitationWithoutPhone);

      // Act
      const result = await controller.createInvitation(
        req,
        createInvitationDto,
      );

      // Assert
      expect(familyService.createInvitation).toHaveBeenCalledWith(
        mockUser.id,
        createInvitationDto,
      );
      expect(result).toEqual(invitationWithoutPhone);
    });

    it('should handle baby not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const createInvitationDto: CreateInvitationDto = {
        baby_id: 'non-existent-baby-id',
      };
      const error = new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby not found',
        HttpStatus.NOT_FOUND,
      );
      familyService.createInvitation.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.createInvitation(req, createInvitationDto),
      ).rejects.toThrow(error);
      expect(familyService.createInvitation).toHaveBeenCalledWith(
        mockUser.id,
        createInvitationDto,
      );
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const createInvitationDto: CreateInvitationDto = {
        baby_id: 'unauthorized-baby-id',
      };
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'No permission to invite for this baby',
        HttpStatus.FORBIDDEN,
      );
      familyService.createInvitation.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.createInvitation(req, createInvitationDto),
      ).rejects.toThrow(error);
      expect(familyService.createInvitation).toHaveBeenCalledWith(
        mockUser.id,
        createInvitationDto,
      );
    });
  });

  describe('acceptInvitation', () => {
    it('should accept invitation successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const acceptInvitationDto: AcceptInvitationDto = {
        invitation_code: 'ABC123',
        role: 1, // 爸爸
      };
      familyService.acceptInvitation.mockResolvedValue(
        mockFamilyMemberResponse,
      );

      // Act
      const result = await controller.acceptInvitation(
        req,
        acceptInvitationDto,
      );

      // Assert
      expect(familyService.acceptInvitation).toHaveBeenCalledWith(
        mockUser.id,
        acceptInvitationDto,
      );
      expect(result).toEqual(mockFamilyMemberResponse);
    });

    it('should handle invitation not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const acceptInvitationDto: AcceptInvitationDto = {
        invitation_code: 'INVALID123',
        role: 1,
      };
      const error = new BusinessException(
        ErrorCode.INVITATION_NOT_FOUND,
        'Invitation not found',
        HttpStatus.NOT_FOUND,
      );
      familyService.acceptInvitation.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.acceptInvitation(req, acceptInvitationDto),
      ).rejects.toThrow(error);
      expect(familyService.acceptInvitation).toHaveBeenCalledWith(
        mockUser.id,
        acceptInvitationDto,
      );
    });

    it('should handle invitation expired error', async () => {
      // Arrange
      const req = { user: mockUser };
      const acceptInvitationDto: AcceptInvitationDto = {
        invitation_code: 'EXPIRED123',
        role: 1,
      };
      const error = new BusinessException(
        ErrorCode.INVITATION_EXPIRED,
        'Invitation has expired',
        HttpStatus.BAD_REQUEST,
      );
      familyService.acceptInvitation.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.acceptInvitation(req, acceptInvitationDto),
      ).rejects.toThrow(error);
      expect(familyService.acceptInvitation).toHaveBeenCalledWith(
        mockUser.id,
        acceptInvitationDto,
      );
    });

    it('should handle already family member error', async () => {
      // Arrange
      const req = { user: mockUser };
      const acceptInvitationDto: AcceptInvitationDto = {
        invitation_code: 'ABC123',
        role: 1,
      };
      const error = new BusinessException(
        ErrorCode.ALREADY_FAMILY_MEMBER,
        'User is already a family member for this baby',
        HttpStatus.CONFLICT,
      );
      familyService.acceptInvitation.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.acceptInvitation(req, acceptInvitationDto),
      ).rejects.toThrow(error);
      expect(familyService.acceptInvitation).toHaveBeenCalledWith(
        mockUser.id,
        acceptInvitationDto,
      );
    });
  });

  describe('getFamilyMembers', () => {
    it('should return family members successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      const familyMembers = [mockFamilyMemberResponse];
      familyService.getFamilyMembers.mockResolvedValue(familyMembers);

      // Act
      const result = await controller.getFamilyMembers(babyId, req);

      // Assert
      expect(familyService.getFamilyMembers).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
      expect(result).toEqual(familyMembers);
    });

    it('should return empty array when no family members exist', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      familyService.getFamilyMembers.mockResolvedValue([]);

      // Act
      const result = await controller.getFamilyMembers(babyId, req);

      // Assert
      expect(familyService.getFamilyMembers).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
      expect(result).toEqual([]);
    });

    it('should handle baby not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'non-existent-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby not found',
        HttpStatus.NOT_FOUND,
      );
      familyService.getFamilyMembers.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFamilyMembers(babyId, req)).rejects.toThrow(
        error,
      );
      expect(familyService.getFamilyMembers).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'unauthorized-baby-id';
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'No permission to view family members for this baby',
        HttpStatus.FORBIDDEN,
      );
      familyService.getFamilyMembers.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFamilyMembers(babyId, req)).rejects.toThrow(
        error,
      );
      expect(familyService.getFamilyMembers).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
      );
    });
  });

  describe('removeFamilyMember', () => {
    it('should remove family member successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      const memberUserId = 'user-id-2';
      familyService.removeFamilyMember.mockResolvedValue(true);

      // Act
      const result = await controller.removeFamilyMember(
        babyId,
        memberUserId,
        req,
      );

      // Assert
      expect(familyService.removeFamilyMember).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
        memberUserId,
      );
      expect(result).toBeUndefined();
    });

    it('should handle baby not found error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'non-existent-baby-id';
      const memberUserId = 'user-id-2';
      const error = new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby not found',
        HttpStatus.NOT_FOUND,
      );
      familyService.removeFamilyMember.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.removeFamilyMember(babyId, memberUserId, req),
      ).rejects.toThrow(error);
      expect(familyService.removeFamilyMember).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
        memberUserId,
      );
    });

    it('should handle access denied error', async () => {
      // Arrange
      const req = { user: mockUser };
      const babyId = 'baby-id-1';
      const memberUserId = 'user-id-2';
      const error = new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can remove family members',
        HttpStatus.FORBIDDEN,
      );
      familyService.removeFamilyMember.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.removeFamilyMember(babyId, memberUserId, req),
      ).rejects.toThrow(error);
      expect(familyService.removeFamilyMember).toHaveBeenCalledWith(
        mockUser.id,
        babyId,
        memberUserId,
      );
    });
  });

  describe('getInvitationsByInviter', () => {
    it('should return invitations by inviter successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const invitations = [mockInvitationResponse];
      familyService.getInvitationsByInviter.mockResolvedValue(invitations);

      // Act
      const result = await controller.getInvitationsByInviter(req);

      // Assert
      expect(familyService.getInvitationsByInviter).toHaveBeenCalledWith(
        mockUser.id,
      );
      expect(result).toEqual(invitations);
    });

    it('should return empty array when no invitations exist', async () => {
      // Arrange
      const req = { user: mockUser };
      familyService.getInvitationsByInviter.mockResolvedValue([]);

      // Act
      const result = await controller.getInvitationsByInviter(req);

      // Assert
      expect(familyService.getInvitationsByInviter).toHaveBeenCalledWith(
        mockUser.id,
      );
      expect(result).toEqual([]);
    });
  });

  describe('getUserFamilyMemberships', () => {
    it('should return user family memberships successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const memberships = [mockFamilyMemberResponse];
      familyService.getUserFamilyMemberships.mockResolvedValue(memberships);

      // Act
      const result = await controller.getUserFamilyMemberships(req);

      // Assert
      expect(familyService.getUserFamilyMemberships).toHaveBeenCalledWith(
        mockUser.id,
      );
      expect(result).toEqual(memberships);
    });

    it('should return empty array when no memberships exist', async () => {
      // Arrange
      const req = { user: mockUser };
      familyService.getUserFamilyMemberships.mockResolvedValue([]);

      // Act
      const result = await controller.getUserFamilyMemberships(req);

      // Assert
      expect(familyService.getUserFamilyMemberships).toHaveBeenCalledWith(
        mockUser.id,
      );
      expect(result).toEqual([]);
    });
  });

  describe('validateInvitationCode', () => {
    it('should validate invitation code successfully', async () => {
      // Arrange
      const req = { user: mockUser };
      const invitationCode = 'ABC123';
      const validationResult = {
        valid: true,
        invitation: mockInvitationResponse,
      };
      familyService.validateInvitationCode.mockResolvedValue(validationResult);

      // Act
      const result = await controller.validateInvitationCode(
        invitationCode,
        req,
      );

      // Assert
      expect(familyService.validateInvitationCode).toHaveBeenCalledWith(
        invitationCode,
        mockUser.id,
      );
      expect(result).toEqual(validationResult);
    });

    it('should return invalid result for invalid invitation code', async () => {
      // Arrange
      const req = { user: mockUser };
      const invitationCode = 'INVALID123';
      const validationResult = {
        valid: false,
        reason: 'Invitation not found',
      };
      familyService.validateInvitationCode.mockResolvedValue(validationResult);

      // Act
      const result = await controller.validateInvitationCode(
        invitationCode,
        req,
      );

      // Assert
      expect(familyService.validateInvitationCode).toHaveBeenCalledWith(
        invitationCode,
        mockUser.id,
      );
      expect(result).toEqual(validationResult);
    });

    it('should return invalid result for expired invitation', async () => {
      // Arrange
      const req = { user: mockUser };
      const invitationCode = 'EXPIRED123';
      const validationResult = {
        valid: false,
        reason: 'Invitation expired',
      };
      familyService.validateInvitationCode.mockResolvedValue(validationResult);

      // Act
      const result = await controller.validateInvitationCode(
        invitationCode,
        req,
      );

      // Assert
      expect(familyService.validateInvitationCode).toHaveBeenCalledWith(
        invitationCode,
        mockUser.id,
      );
      expect(result).toEqual(validationResult);
    });
  });
});
