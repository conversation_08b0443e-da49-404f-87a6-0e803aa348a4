import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import { FamilyService } from './family.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateInvitationDto } from './dto/create-invitation.dto';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { InvitationResponseDto } from './dto/invitation-response.dto';
import { FamilyMemberResponseDto } from './dto/family-member-response.dto';
import { IUser } from '../common/interfaces/entities';

@Controller('family')
@UseGuards(JwtAuthGuard)
export class FamilyController {
  constructor(private readonly familyService: FamilyService) {}

  /**
   * 创建邀请码
   */
  @Post('invite')
  @HttpCode(HttpStatus.CREATED)
  async createInvitation(
    @Request() req: { user: IUser },
    @Body() createInvitationDto: CreateInvitationDto,
  ): Promise<InvitationResponseDto> {
    return this.familyService.createInvitation(
      req.user.id,
      createInvitationDto,
    );
  }

  /**
   * 接受邀请
   */
  @Post('accept-invitation')
  @HttpCode(HttpStatus.CREATED)
  async acceptInvitation(
    @Request() req: { user: IUser },
    @Body() acceptInvitationDto: AcceptInvitationDto,
  ): Promise<FamilyMemberResponseDto> {
    return this.familyService.acceptInvitation(
      req.user.id,
      acceptInvitationDto,
    );
  }

  /**
   * 获取宝宝的家庭成员列表
   */
  @Get('members/:babyId')
  async getFamilyMembers(
    @Param('babyId', ParseUUIDPipe) babyId: string,
    @Request() req: { user: IUser },
  ): Promise<FamilyMemberResponseDto[]> {
    return this.familyService.getFamilyMembers(req.user.id, babyId);
  }

  /**
   * 移除家庭成员
   */
  @Delete('members/:babyId/:memberUserId')
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeFamilyMember(
    @Param('babyId', ParseUUIDPipe) babyId: string,
    @Param('memberUserId', ParseUUIDPipe) memberUserId: string,
    @Request() req: { user: IUser },
  ): Promise<void> {
    await this.familyService.removeFamilyMember(
      req.user.id,
      babyId,
      memberUserId,
    );
  }

  /**
   * 获取用户发出的邀请列表
   */
  @Get('invitations')
  async getInvitationsByInviter(
    @Request() req: { user: IUser },
  ): Promise<InvitationResponseDto[]> {
    return this.familyService.getInvitationsByInviter(req.user.id);
  }

  /**
   * 获取用户的家庭成员关系
   */
  @Get('memberships')
  async getUserFamilyMemberships(
    @Request() req: { user: IUser },
  ): Promise<FamilyMemberResponseDto[]> {
    return this.familyService.getUserFamilyMemberships(req.user.id);
  }

  /**
   * 验证邀请码（用于前端预览）
   */
  @Get('validate-invitation')
  async validateInvitationCode(
    @Query('code') invitationCode: string,
    @Request() req: { user: IUser },
  ): Promise<{
    valid: boolean;
    invitation?: InvitationResponseDto;
    reason?: string;
  }> {
    return this.familyService.validateInvitationCode(
      invitationCode,
      req.user.id,
    );
  }
}
