import { FamilyMember } from '../family-member.entity';
import { FamilyRole } from '../../../common/enums';

describe('FamilyMember Entity', () => {
  const validMemberData = {
    baby_id: '123e4567-e89b-12d3-a456-426614174000',
    user_id: '123e4567-e89b-12d3-a456-426614174001',
    role: FamilyRole.FATHER,
  };

  describe('constructor', () => {
    it('should create family member instance with partial data', () => {
      const memberData = {
        id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-426614174000',
        user_id: '123e4567-e89b-12d3-a456-426614174001',
        role: FamilyRole.FATHER,
        is_owner: true,
        created_at: new Date(),
      };

      const member = new FamilyMember(memberData);

      expect(member.id).toBe(memberData.id);
      expect(member.baby_id).toBe(memberData.baby_id);
      expect(member.user_id).toBe(memberData.user_id);
      expect(member.role).toBe(memberData.role);
      expect(member.is_owner).toBe(memberData.is_owner);
    });
  });

  describe('create', () => {
    it('should create family member with valid data', () => {
      const member = FamilyMember.create(validMemberData);

      expect(member.baby_id).toBe(validMemberData.baby_id);
      expect(member.user_id).toBe(validMemberData.user_id);
      expect(member.role).toBe(validMemberData.role);
      expect(member.is_owner).toBe(false); // Default value
      expect(member.created_at).toBeInstanceOf(Date);
    });

    it('should create family member as owner', () => {
      const ownerData = {
        ...validMemberData,
        is_owner: true,
      };

      const member = FamilyMember.create(ownerData);

      expect(member.is_owner).toBe(true);
    });

    it('should throw error for invalid data', () => {
      const invalidData = {
        baby_id: 'invalid-uuid',
        user_id: 'invalid-uuid',
        role: 0, // Invalid role
      };

      expect(() => FamilyMember.create(invalidData)).toThrow(
        'Invalid family member data',
      );
    });
  });

  describe('validate', () => {
    it('should return valid for correct family member data', () => {
      const member = FamilyMember.create(validMemberData);
      member.id = '123e4567-e89b-12d3-a456-************';

      const result = member.validate();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return invalid for incorrect family member data', () => {
      const member = new FamilyMember({
        id: '123e4567-e89b-12d3-a456-************',
        baby_id: 'invalid-uuid',
        user_id: 'invalid-uuid',
        role: 0, // Invalid role
        is_owner: false,
        created_at: new Date(),
      });

      const result = member.validate();

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('getRoleDescription', () => {
    it('should return correct description for father', () => {
      const member = FamilyMember.create({
        ...validMemberData,
        role: FamilyRole.FATHER,
      });

      expect(member.getRoleDescription()).toBe('爸爸');
    });

    it('should return correct description for mother', () => {
      const member = FamilyMember.create({
        ...validMemberData,
        role: FamilyRole.MOTHER,
      });

      expect(member.getRoleDescription()).toBe('妈妈');
    });

    it('should return correct description for other caregiver', () => {
      const member = FamilyMember.create({
        ...validMemberData,
        role: FamilyRole.OTHER_CAREGIVER,
      });

      expect(member.getRoleDescription()).toBe('其他照顾者');
    });

    it('should return unknown for invalid role', () => {
      const member = new FamilyMember({
        ...validMemberData,
        role: 999, // Invalid role
        created_at: new Date(),
      });

      expect(member.getRoleDescription()).toBe('未知角色');
    });
  });

  describe('ownership and permissions', () => {
    let owner: FamilyMember;
    let member: FamilyMember;

    beforeEach(() => {
      owner = FamilyMember.create({
        ...validMemberData,
        is_owner: true,
      });

      member = FamilyMember.create(validMemberData);
    });

    describe('isBabyOwner', () => {
      it('should return true for owner', () => {
        expect(owner.isBabyOwner()).toBe(true);
      });

      it('should return false for non-owner', () => {
        expect(member.isBabyOwner()).toBe(false);
      });
    });

    describe('hasManagementPermission', () => {
      it('should return true for owner', () => {
        expect(owner.hasManagementPermission()).toBe(true);
      });

      it('should return false for non-owner', () => {
        expect(member.hasManagementPermission()).toBe(false);
      });
    });

    describe('canInviteMembers', () => {
      it('should return true for owner', () => {
        expect(owner.canInviteMembers()).toBe(true);
      });

      it('should return false for non-owner', () => {
        expect(member.canInviteMembers()).toBe(false);
      });
    });

    describe('canRemoveMembers', () => {
      it('should return true for owner', () => {
        expect(owner.canRemoveMembers()).toBe(true);
      });

      it('should return false for non-owner', () => {
        expect(member.canRemoveMembers()).toBe(false);
      });
    });

    describe('setAsOwner', () => {
      it('should set member as owner', () => {
        expect(member.is_owner).toBe(false);

        member.setAsOwner();

        expect(member.is_owner).toBe(true);
      });
    });
  });

  describe('toResponse', () => {
    it('should return family member data in response format', () => {
      const member = FamilyMember.create(validMemberData);
      member.id = '123e4567-e89b-12d3-a456-************';

      const response = member.toResponse();

      expect(response).toEqual({
        id: member.id,
        baby_id: member.baby_id,
        user_id: member.user_id,
        role: member.role,
        is_owner: member.is_owner,
        created_at: member.created_at,
      });
    });
  });

  describe('toDetailedResponse', () => {
    it('should return family member data with role description and permissions', () => {
      const owner = FamilyMember.create({
        ...validMemberData,
        is_owner: true,
      });
      owner.id = '123e4567-e89b-12d3-a456-************';

      const response = owner.toDetailedResponse();

      expect(response).toHaveProperty('id', owner.id);
      expect(response).toHaveProperty('role_description', '爸爸');
      expect(response).toHaveProperty('permissions');
      expect(response.permissions).toEqual({
        can_invite_members: true,
        can_remove_members: true,
        has_management_permission: true,
      });
    });

    it('should return correct permissions for non-owner', () => {
      const member = FamilyMember.create(validMemberData);
      member.id = '123e4567-e89b-12d3-a456-426614174003';

      const response = member.toDetailedResponse();

      expect(response.permissions).toEqual({
        can_invite_members: false,
        can_remove_members: false,
        has_management_permission: false,
      });
    });
  });
});
