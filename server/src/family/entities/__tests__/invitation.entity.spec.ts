import { Invitation } from '../invitation.entity';
import { ICreateInvitationDto } from '../../../common/interfaces';

describe('Invitation Entity', () => {
  const validInvitationData: ICreateInvitationDto = {
    baby_id: '123e4567-e89b-12d3-a456-************',
    phone: '13812345678',
  };

  const inviterId = '123e4567-e89b-12d3-a456-426614174001';

  describe('constructor', () => {
    it('should create invitation instance with partial data', () => {
      const invitationData = {
        id: '123e4567-e89b-12d3-a456-************',
        baby_id: '123e4567-e89b-12d3-a456-************',
        inviter_id: inviterId,
        invitation_code: 'ABC1234567',
        phone: '13812345678',
        expires_at: new Date(),
        created_at: new Date(),
      };

      const invitation = new Invitation(invitationData);

      expect(invitation.id).toBe(invitationData.id);
      expect(invitation.baby_id).toBe(invitationData.baby_id);
      expect(invitation.inviter_id).toBe(invitationData.inviter_id);
      expect(invitation.invitation_code).toBe(invitationData.invitation_code);
      expect(invitation.phone).toBe(invitationData.phone);
    });
  });

  describe('create', () => {
    it('should create invitation with valid data', () => {
      const invitation = Invitation.create(validInvitationData, inviterId);

      expect(invitation.baby_id).toBe(validInvitationData.baby_id);
      expect(invitation.inviter_id).toBe(inviterId);
      expect(invitation.phone).toBe(validInvitationData.phone);
      expect(invitation.invitation_code).toHaveLength(10);
      expect(invitation.expires_at).toBeInstanceOf(Date);
      expect(invitation.created_at).toBeInstanceOf(Date);

      // Should expire in 7 days
      const expectedExpiry = new Date();
      expectedExpiry.setDate(expectedExpiry.getDate() + 7);
      const timeDiff = Math.abs(
        invitation.expires_at.getTime() - expectedExpiry.getTime(),
      );
      expect(timeDiff).toBeLessThan(1000); // Within 1 second
    });

    it('should create invitation without phone', () => {
      const dataWithoutPhone: ICreateInvitationDto = {
        baby_id: '123e4567-e89b-12d3-a456-************',
      };

      const invitation = Invitation.create(dataWithoutPhone, inviterId);

      expect(invitation.baby_id).toBe(dataWithoutPhone.baby_id);
      expect(invitation.phone).toBeUndefined();
    });

    it('should throw error for invalid data', () => {
      const invalidData: ICreateInvitationDto = {
        baby_id: 'invalid-uuid',
        phone: '123', // Invalid phone
      };

      expect(() => Invitation.create(invalidData, 'invalid-uuid')).toThrow(
        'Invalid invitation data',
      );
    });
  });

  describe('validate', () => {
    it('should return valid for correct invitation data', () => {
      const invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';

      const result = invitation.validate();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('status checks', () => {
    let invitation: Invitation;

    beforeEach(() => {
      invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';
    });

    describe('isExpired', () => {
      it('should return false for valid invitation', () => {
        expect(invitation.isExpired()).toBe(false);
      });

      it('should return true for expired invitation', () => {
        invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday
        expect(invitation.isExpired()).toBe(true);
      });
    });

    describe('isUsed', () => {
      it('should return false for unused invitation', () => {
        expect(invitation.isUsed()).toBe(false);
      });

      it('should return true for used invitation', () => {
        invitation.used_at = new Date();
        expect(invitation.isUsed()).toBe(true);
      });
    });

    describe('isValid', () => {
      it('should return true for valid invitation', () => {
        expect(invitation.isValid()).toBe(true);
      });

      it('should return false for expired invitation', () => {
        invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday
        expect(invitation.isValid()).toBe(false);
      });

      it('should return false for used invitation', () => {
        invitation.used_at = new Date();
        expect(invitation.isValid()).toBe(false);
      });
    });

    describe('getStatusDescription', () => {
      it('should return "有效" for valid invitation', () => {
        expect(invitation.getStatusDescription()).toBe('有效');
      });

      it('should return "已过期" for expired invitation', () => {
        invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday
        expect(invitation.getStatusDescription()).toBe('已过期');
      });

      it('should return "已使用" for used invitation', () => {
        invitation.used_at = new Date();
        expect(invitation.getStatusDescription()).toBe('已使用');
      });

      it('should prioritize "已使用" over "已过期"', () => {
        invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday
        invitation.used_at = new Date();
        expect(invitation.getStatusDescription()).toBe('已使用');
      });
    });

    describe('getRemainingHours', () => {
      it('should return positive hours for valid invitation', () => {
        const hours = invitation.getRemainingHours();
        expect(hours).toBeGreaterThan(0);
        expect(hours).toBeLessThanOrEqual(168); // 7 days = 168 hours
      });

      it('should return 0 for expired invitation', () => {
        invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday
        expect(invitation.getRemainingHours()).toBe(0);
      });

      it('should return 0 for used invitation', () => {
        invitation.used_at = new Date();
        expect(invitation.getRemainingHours()).toBe(0);
      });
    });
  });

  describe('use', () => {
    let invitation: Invitation;
    const userId = '123e4567-e89b-12d3-a456-426614174003';

    beforeEach(() => {
      invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';
    });

    it('should mark invitation as used', () => {
      expect(invitation.isUsed()).toBe(false);

      invitation.use(userId);

      expect(invitation.isUsed()).toBe(true);
      expect(invitation.used_at).toBeInstanceOf(Date);
      expect(invitation.used_by).toBe(userId);
    });

    it('should throw error when trying to use already used invitation', () => {
      invitation.use(userId);

      expect(() => invitation.use('another-user')).toThrow(
        'Invitation has already been used',
      );
    });

    it('should throw error when trying to use expired invitation', () => {
      invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday

      expect(() => invitation.use(userId)).toThrow('Invitation has expired');
    });
  });

  describe('canBeUsedBy', () => {
    let invitation: Invitation;
    const userId = '123e4567-e89b-12d3-a456-426614174003';

    beforeEach(() => {
      invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';
    });

    it('should allow valid user to use invitation', () => {
      const result = invitation.canBeUsedBy(userId);

      expect(result.canUse).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('should not allow inviter to use their own invitation', () => {
      const result = invitation.canBeUsedBy(inviterId);

      expect(result.canUse).toBe(false);
      expect(result.reason).toBe('不能接受自己发出的邀请');
    });

    it('should not allow using already used invitation', () => {
      invitation.use(userId);

      const result = invitation.canBeUsedBy('another-user');

      expect(result.canUse).toBe(false);
      expect(result.reason).toBe('邀请已被使用');
    });

    it('should not allow using expired invitation', () => {
      invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday

      const result = invitation.canBeUsedBy(userId);

      expect(result.canUse).toBe(false);
      expect(result.reason).toBe('邀请已过期');
    });
  });

  describe('toResponse', () => {
    it('should return invitation data in response format', () => {
      const invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';

      const response = invitation.toResponse();

      expect(response).toEqual({
        id: invitation.id,
        baby_id: invitation.baby_id,
        inviter_id: invitation.inviter_id,
        invitation_code: invitation.invitation_code,
        phone: invitation.phone,
        expires_at: invitation.expires_at,
        used_at: invitation.used_at,
        used_by: invitation.used_by,
        created_at: invitation.created_at,
      });
    });
  });

  describe('toDetailedResponse', () => {
    it('should return invitation data with status information', () => {
      const invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';

      const response = invitation.toDetailedResponse();

      expect(response).toHaveProperty('id', invitation.id);
      expect(response).toHaveProperty('status', '有效');
      expect(response).toHaveProperty('is_expired', false);
      expect(response).toHaveProperty('is_used', false);
      expect(response).toHaveProperty('is_valid', true);
      expect(response).toHaveProperty('remaining_hours');
      expect(typeof response.remaining_hours).toBe('number');
      expect(response.remaining_hours).toBeGreaterThan(0);
    });

    it('should return correct status for expired invitation', () => {
      const invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';
      invitation.expires_at = new Date(Date.now() - 86400000); // Yesterday

      const response = invitation.toDetailedResponse();

      expect(response.status).toBe('已过期');
      expect(response.is_expired).toBe(true);
      expect(response.is_valid).toBe(false);
      expect(response.remaining_hours).toBe(0);
    });

    it('should return correct status for used invitation', () => {
      const invitation = Invitation.create(validInvitationData, inviterId);
      invitation.id = '123e4567-e89b-12d3-a456-************';
      invitation.use('123e4567-e89b-12d3-a456-426614174003');

      const response = invitation.toDetailedResponse();

      expect(response.status).toBe('已使用');
      expect(response.is_used).toBe(true);
      expect(response.is_valid).toBe(false);
      expect(response.remaining_hours).toBe(0);
    });
  });
});
