import { FamilyRole } from '../../common/enums';
import { IFamilyMember, IFamilyMemberResponse } from '../../common/interfaces';
import { FamilyMemberValidator } from '../../common/validators';

export class FamilyMember implements IFamilyMember {
  id: string;
  baby_id: string;
  user_id: string;
  role: number; // 1: 爸爸, 2: 妈妈, 3: 其他照顾者
  is_owner: boolean; // 是否为宝宝档案的创建者
  created_at: Date;

  constructor(partial: Partial<FamilyMember>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新家庭成员实例
   */
  static create(data: {
    baby_id: string;
    user_id: string;
    role: number;
    is_owner?: boolean;
  }): FamilyMember {
    const validation = FamilyMemberValidator.validateFamilyMemberData({
      baby_id: data.baby_id,
      user_id: data.user_id,
      role: data.role,
    });

    if (!validation.isValid) {
      throw new Error(
        `Invalid family member data: ${validation.errors.join(', ')}`,
      );
    }

    return new FamilyMember({
      id: '', // 将由数据库生成
      baby_id: data.baby_id,
      user_id: data.user_id,
      role: data.role,
      is_owner: data.is_owner || false,
      created_at: new Date(),
    });
  }

  /**
   * 验证家庭成员数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return FamilyMemberValidator.validateFamilyMemberData({
      baby_id: this.baby_id,
      user_id: this.user_id,
      role: this.role,
    });
  }

  /**
   * 获取角色描述
   */
  getRoleDescription(): string {
    switch (this.role) {
      case FamilyRole.FATHER:
        return '爸爸';
      case FamilyRole.MOTHER:
        return '妈妈';
      case FamilyRole.OTHER_CAREGIVER:
        return '其他照顾者';
      default:
        return '未知角色';
    }
  }

  /**
   * 检查是否为宝宝的所有者
   */
  isBabyOwner(): boolean {
    return this.is_owner;
  }

  /**
   * 检查是否有管理权限（所有者有完全权限）
   */
  hasManagementPermission(): boolean {
    return this.is_owner;
  }

  /**
   * 检查是否可以邀请其他成员（只有所有者可以邀请）
   */
  canInviteMembers(): boolean {
    return this.is_owner;
  }

  /**
   * 检查是否可以移除其他成员（只有所有者可以移除）
   */
  canRemoveMembers(): boolean {
    return this.is_owner;
  }

  /**
   * 设置为所有者
   */
  setAsOwner(): void {
    this.is_owner = true;
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): IFamilyMemberResponse {
    return {
      id: this.id,
      baby_id: this.baby_id,
      user_id: this.user_id,
      role: this.role,
      is_owner: this.is_owner,
      created_at: this.created_at,
    };
  }

  /**
   * 转换为详细响应格式（包含角色描述）
   */
  toDetailedResponse(): IFamilyMemberResponse & {
    role_description: string;
    permissions: {
      can_invite_members: boolean;
      can_remove_members: boolean;
      has_management_permission: boolean;
    };
  } {
    return {
      ...this.toResponse(),
      role_description: this.getRoleDescription(),
      permissions: {
        can_invite_members: this.canInviteMembers(),
        can_remove_members: this.canRemoveMembers(),
        has_management_permission: this.hasManagementPermission(),
      },
    };
  }
}
