import {
  IInvitation,
  IInvitationResponse,
  ICreateInvitationDto,
} from '../../common/interfaces';
import { InvitationValidator } from '../../common/validators';

export class Invitation implements IInvitation {
  id: string;
  baby_id: string;
  inviter_id: string;
  invitation_code: string;
  phone?: string;
  expires_at: Date;
  used_at?: Date;
  used_by?: string;
  created_at: Date;

  constructor(partial: Partial<Invitation>) {
    Object.assign(this, partial);
  }

  /**
   * 创建新邀请实例
   */
  static create(data: ICreateInvitationDto, inviterId: string): Invitation {
    const invitationCode = InvitationValidator.generateInvitationCode();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7天后过期

    const invitationData = {
      baby_id: data.baby_id,
      inviter_id: inviterId,
      invitation_code: invitationCode,
      phone: data.phone,
      expires_at: expiresAt,
    };

    const validation =
      InvitationValidator.validateInvitationData(invitationData);
    if (!validation.isValid) {
      throw new Error(
        `Invalid invitation data: ${validation.errors.join(', ')}`,
      );
    }

    return new Invitation({
      id: '', // 将由数据库生成
      baby_id: data.baby_id,
      inviter_id: inviterId,
      invitation_code: invitationCode,
      phone: data.phone,
      expires_at: expiresAt,
      created_at: new Date(),
    });
  }

  /**
   * 验证邀请数据完整性
   */
  validate(): { isValid: boolean; errors: string[] } {
    return InvitationValidator.validateInvitationData({
      baby_id: this.baby_id,
      inviter_id: this.inviter_id,
      invitation_code: this.invitation_code,
      phone: this.phone,
      expires_at: this.expires_at,
    });
  }

  /**
   * 检查邀请是否已过期
   */
  isExpired(): boolean {
    return new Date() > this.expires_at;
  }

  /**
   * 检查邀请是否已被使用
   */
  isUsed(): boolean {
    return !!this.used_at;
  }

  /**
   * 检查邀请是否有效（未过期且未使用）
   */
  isValid(): boolean {
    return !this.isExpired() && !this.isUsed();
  }

  /**
   * 获取邀请状态描述
   */
  getStatusDescription(): string {
    if (this.isUsed()) {
      return '已使用';
    } else if (this.isExpired()) {
      return '已过期';
    } else {
      return '有效';
    }
  }

  /**
   * 获取剩余有效时间（小时）
   */
  getRemainingHours(): number {
    if (this.isExpired() || this.isUsed()) {
      return 0;
    }

    const now = new Date();
    const diffMs = this.expires_at.getTime() - now.getTime();
    return Math.max(0, Math.floor(diffMs / (1000 * 60 * 60)));
  }

  /**
   * 使用邀请
   */
  use(userId: string): void {
    if (this.isUsed()) {
      throw new Error('Invitation has already been used');
    }

    if (this.isExpired()) {
      throw new Error('Invitation has expired');
    }

    this.used_at = new Date();
    this.used_by = userId;
  }

  /**
   * 检查邀请是否可以被特定用户使用
   */
  canBeUsedBy(userId: string): { canUse: boolean; reason?: string } {
    if (this.isUsed()) {
      return { canUse: false, reason: '邀请已被使用' };
    }

    if (this.isExpired()) {
      return { canUse: false, reason: '邀请已过期' };
    }

    if (userId === this.inviter_id) {
      return { canUse: false, reason: '不能接受自己发出的邀请' };
    }

    return { canUse: true };
  }

  /**
   * 转换为响应DTO格式
   */
  toResponse(): IInvitationResponse {
    return {
      id: this.id,
      baby_id: this.baby_id,
      inviter_id: this.inviter_id,
      invitation_code: this.invitation_code,
      phone: this.phone,
      expires_at: this.expires_at,
      used_at: this.used_at,
      used_by: this.used_by,
      created_at: this.created_at,
    };
  }

  /**
   * 转换为详细响应格式（包含状态信息）
   */
  toDetailedResponse(): IInvitationResponse & {
    status: string;
    is_expired: boolean;
    is_used: boolean;
    is_valid: boolean;
    remaining_hours: number;
  } {
    return {
      ...this.toResponse(),
      status: this.getStatusDescription(),
      is_expired: this.isExpired(),
      is_used: this.isUsed(),
      is_valid: this.isValid(),
      remaining_hours: this.getRemainingHours(),
    };
  }
}
