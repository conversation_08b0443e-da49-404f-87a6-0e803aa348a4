import { Injectable } from '@nestjs/common';
import { SupabaseService } from '../database/supabase.service';
import { DatabaseUtils } from '../database/database.utils';
import {
  IFamilyMember,
  IInvitation,
  ICreateInvitationDto,
  IAcceptInvitationDto,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode, FamilyRole } from '../common/enums';

@Injectable()
export class FamilyRepository {
  private readonly logger = DatabaseUtils['logger'];

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * 创建家庭成员关系
   */
  async createFamilyMember(memberData: {
    baby_id: string;
    user_id: string;
    role: number;
    is_owner?: boolean;
  }): Promise<IFamilyMember> {
    try {
      // 验证角色值
      if (!Object.values(FamilyRole).includes(memberData.role)) {
        throw new BusinessException(
          ErrorCode.INVALID_FAMILY_ROLE,
          'Invalid family role',
        );
      }

      // 检查是否已经是家庭成员
      const existingMember = await this.findFamilyMember(
        memberData.baby_id,
        memberData.user_id,
      );
      if (existingMember) {
        throw new BusinessException(
          ErrorCode.ALREADY_FAMILY_MEMBER,
          'User is already a family member for this baby',
        );
      }

      DatabaseUtils.logDatabaseOperation(
        'CREATE_FAMILY_MEMBER',
        'family_members',
        memberData,
      );

      const result = await this.supabaseService
        .query('family_members')
        .insert(memberData)
        .select()
        .single();

      this.supabaseService.handleDatabaseError(
        result.error,
        'create family member',
      );

      return result.data as IFamilyMember;
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'create family member', 'FamilyMember');
    }
  }

  /**
   * 查找家庭成员关系
   */
  async findFamilyMember(
    babyId: string,
    userId: string,
  ): Promise<IFamilyMember | null> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_FAMILY_MEMBER',
        'family_members',
        { babyId, userId },
      );

      const result = await this.supabaseService
        .query('family_members')
        .select('*')
        .eq('baby_id', babyId)
        .eq('user_id', userId)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find family member',
        );
      }

      return result.data as IFamilyMember | null;
    } catch (error) {
      DatabaseUtils.handleError(error, 'find family member', 'FamilyMember');
    }
  }

  /**
   * 获取宝宝的所有家庭成员
   */
  async findFamilyMembersByBaby(babyId: string): Promise<IFamilyMember[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_FAMILY_MEMBERS_BY_BABY',
        'family_members',
        { babyId },
      );

      const result = await this.supabaseService
        .query('family_members')
        .select(
          `
          *,
          user:users(
            id,
            username,
            phone,
            avatar_url
          )
        `,
        )
        .eq('baby_id', babyId)
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find family members by baby',
      );

      return (result.data as IFamilyMember[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find family members by baby',
        'FamilyMember',
      );
    }
  }

  /**
   * 获取用户的所有家庭成员关系
   */
  async findFamilyMembersByUser(userId: string): Promise<IFamilyMember[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_FAMILY_MEMBERS_BY_USER',
        'family_members',
        { userId },
      );

      const result = await this.supabaseService
        .query('family_members')
        .select(
          `
          *,
          baby:babies(
            id,
            nickname,
            birth_date,
            gender,
            avatar_url
          )
        `,
        )
        .eq('user_id', userId)
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find family members by user',
      );

      return (result.data as IFamilyMember[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find family members by user',
        'FamilyMember',
      );
    }
  }

  /**
   * 删除家庭成员关系
   */
  async removeFamilyMember(babyId: string, userId: string): Promise<boolean> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'REMOVE_FAMILY_MEMBER',
        'family_members',
        { babyId, userId },
      );

      const result = await this.supabaseService
        .query('family_members')
        .delete()
        .eq('baby_id', babyId)
        .eq('user_id', userId);

      this.supabaseService.handleDatabaseError(
        result.error,
        'remove family member',
      );

      return true;
    } catch (error) {
      DatabaseUtils.handleError(error, 'remove family member', 'FamilyMember');
    }
  }

  /**
   * 创建邀请
   */
  async createInvitation(invitationData: {
    baby_id: string;
    inviter_id: string;
    phone?: string;
  }): Promise<IInvitation> {
    try {
      // 生成邀请码和过期时间
      const invitationCode = DatabaseUtils.generateInvitationCode();
      const expiresAt = DatabaseUtils.calculateInvitationExpiry(7);

      const data = {
        ...invitationData,
        invitation_code: invitationCode,
        expires_at: expiresAt.toISOString(),
      };

      DatabaseUtils.logDatabaseOperation(
        'CREATE_INVITATION',
        'invitations',
        data,
      );

      const result = await this.supabaseService
        .query('invitations')
        .insert(data)
        .select()
        .single();

      this.supabaseService.handleDatabaseError(
        result.error,
        'create invitation',
      );

      return result.data as IInvitation;
    } catch (error) {
      DatabaseUtils.handleError(error, 'create invitation', 'Invitation');
    }
  }

  /**
   * 根据邀请码查找邀请
   */
  async findInvitationByCode(
    invitationCode: string,
  ): Promise<IInvitation | null> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_INVITATION_BY_CODE',
        'invitations',
        { invitationCode },
      );

      const result = await this.supabaseService
        .query('invitations')
        .select(
          `
          *,
          baby:babies(
            id,
            nickname,
            birth_date,
            gender,
            avatar_url
          ),
          inviter:users!invitations_inviter_id_fkey(
            id,
            username,
            phone,
            avatar_url
          )
        `,
        )
        .eq('invitation_code', invitationCode)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find invitation by code',
        );
      }

      return result.data as IInvitation | null;
    } catch (error) {
      DatabaseUtils.handleError(error, 'find invitation by code', 'Invitation');
    }
  }

  /**
   * 使用邀请码
   */
  async useInvitation(
    invitationCode: string,
    userId: string,
  ): Promise<IInvitation> {
    try {
      const data = {
        used_at: new Date().toISOString(),
        used_by: userId,
      };

      DatabaseUtils.logDatabaseOperation('USE_INVITATION', 'invitations', {
        invitationCode,
        userId,
      });

      const result = await this.supabaseService
        .query('invitations')
        .update(data)
        .eq('invitation_code', invitationCode)
        .select()
        .single();

      this.supabaseService.handleDatabaseError(result.error, 'use invitation');

      return result.data as IInvitation;
    } catch (error) {
      DatabaseUtils.handleError(error, 'use invitation', 'Invitation');
    }
  }

  /**
   * 获取用户发出的邀请列表
   */
  async findInvitationsByInviter(inviterId: string): Promise<IInvitation[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_INVITATIONS_BY_INVITER',
        'invitations',
        { inviterId },
      );

      const result = await this.supabaseService
        .query('invitations')
        .select(
          `
          *,
          baby:babies(
            id,
            nickname,
            birth_date,
            gender,
            avatar_url
          )
        `,
        )
        .eq('inviter_id', inviterId)
        .order('created_at', { ascending: false });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find invitations by inviter',
      );

      return (result.data as IInvitation[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find invitations by inviter',
        'Invitation',
      );
    }
  }

  /**
   * 清理过期邀请
   */
  async cleanupExpiredInvitations(): Promise<number> {
    try {
      const now = new Date().toISOString();

      DatabaseUtils.logDatabaseOperation(
        'CLEANUP_EXPIRED_INVITATIONS',
        'invitations',
        { before: now },
      );

      const result = await this.supabaseService
        .query('invitations')
        .delete()
        .lt('expires_at', now)
        .is('used_at', null);

      this.supabaseService.handleDatabaseError(
        result.error,
        'cleanup expired invitations',
      );

      // 注意：Supabase 的 delete 操作不返回删除的行数，这里返回0作为占位
      return 0;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'cleanup expired invitations',
        'Invitation',
      );
    }
  }

  /**
   * 验证邀请是否可用
   */
  async validateInvitation(
    invitationCode: string,
    userId: string,
  ): Promise<{
    valid: boolean;
    invitation?: IInvitation;
    reason?: string;
  }> {
    try {
      const invitation = await this.findInvitationByCode(invitationCode);

      if (!invitation) {
        return {
          valid: false,
          reason: 'Invitation not found',
        };
      }

      // 检查是否已使用
      if (invitation.used_at) {
        return {
          valid: false,
          invitation,
          reason: 'Invitation already used',
        };
      }

      // 检查是否过期
      if (DatabaseUtils.isInvitationExpired(invitation.expires_at)) {
        return {
          valid: false,
          invitation,
          reason: 'Invitation expired',
        };
      }

      // 检查是否已经是家庭成员
      const existingMember = await this.findFamilyMember(
        invitation.baby_id,
        userId,
      );
      if (existingMember) {
        return {
          valid: false,
          invitation,
          reason: 'Already a family member',
        };
      }

      return {
        valid: true,
        invitation,
      };
    } catch (error) {
      DatabaseUtils.handleError(error, 'validate invitation', 'Invitation');
    }
  }
}
