import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { appConfig, jwtConfig, supabaseConfig, wechatConfig } from './config';

describe('Application Startup', () => {
  let app: INestApplication;
  let configService: ConfigService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [supabaseConfig, appConfig, jwtConfig, wechatConfig],
          envFilePath: ['.env.test', '.env'],
          cache: true,
          expandVariables: true,
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    configService = app.get<ConfigService>(ConfigService);
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('Configuration Loading', () => {
    it('should load app configuration', () => {
      expect(configService.get('app.port')).toBeDefined();
      expect(configService.get('app.nodeEnv')).toBeDefined();
      expect(typeof configService.get('app.isDevelopment')).toBe('boolean');
      expect(typeof configService.get('app.isProduction')).toBe('boolean');
      expect(typeof configService.get('app.isTest')).toBe('boolean');
    });

    it('should load JWT configuration', () => {
      expect(configService.get('jwt.secret')).toBeDefined();
      expect(configService.get('jwt.expiresIn')).toBeDefined();
      expect(configService.get('jwt.adminSecret')).toBeDefined();
      expect(configService.get('jwt.adminExpiresIn')).toBeDefined();
    });

    it('should load Supabase configuration', () => {
      expect(configService.get('supabase.url')).toBeDefined();
      expect(configService.get('supabase.key')).toBeDefined();
      expect(configService.get('supabase.serviceKey')).toBeDefined();
      expect(configService.get('supabase.storageBucket')).toBeDefined();
    });

    it('should load WeChat configuration', () => {
      expect(configService.get('wechat.appId')).toBeDefined();
      expect(configService.get('wechat.appSecret')).toBeDefined();
      expect(configService.get('wechat.apiUrl')).toBeDefined();
    });
  });

  describe('Application Initialization', () => {
    it('should initialize application successfully', async () => {
      // 配置全局管道、过滤器和拦截器
      app.useGlobalPipes(
        new ValidationPipe({
          whitelist: true,
          forbidNonWhitelisted: true,
          transform: true,
        }),
      );

      app.useGlobalFilters(new HttpExceptionFilter());
      app.useGlobalInterceptors(new TransformInterceptor());

      // 设置全局前缀
      app.setGlobalPrefix('api', {
        exclude: ['/'],
      });

      // 启用 CORS
      app.enableCors({
        origin: true,
        credentials: true,
      });

      await app.init();
      expect(app).toBeDefined();
    });

    it('should have correct global prefix configuration', async () => {
      app.setGlobalPrefix('api', {
        exclude: ['/'],
      });

      await app.init();

      // 验证应用已正确初始化
      expect(app).toBeDefined();
    });

    it('should configure CORS correctly', async () => {
      app.enableCors({
        origin: true,
        credentials: true,
      });

      await app.init();
      expect(app).toBeDefined();
    });
  });

  describe('Environment Variables', () => {
    it('should handle missing environment variables gracefully', () => {
      // 测试默认值
      const port = configService.get('app.port', 3000);
      const nodeEnv = configService.get('app.nodeEnv', 'development');
      const jwtSecret = configService.get('jwt.secret', 'default-jwt-secret');

      expect(typeof port).toBe('number');
      expect(typeof nodeEnv).toBe('string');
      expect(typeof jwtSecret).toBe('string');
    });

    it('should validate required configuration', () => {
      // 验证关键配置项存在
      expect(configService.get('app')).toBeDefined();
      expect(configService.get('jwt')).toBeDefined();
      expect(configService.get('supabase')).toBeDefined();
      expect(configService.get('wechat')).toBeDefined();
    });
  });

  describe('Module Dependencies', () => {
    it('should resolve all module dependencies', async () => {
      await app.init();

      // 验证关键服务可以被解析
      const configSvc = app.get(ConfigService);
      expect(configSvc).toBeDefined();
    });

    it('should have global configuration available', async () => {
      await app.init();

      const config = app.get(ConfigService);
      expect(config.get('app')).toBeDefined();
      expect(config.get('jwt')).toBeDefined();
      expect(config.get('supabase')).toBeDefined();
      expect(config.get('wechat')).toBeDefined();
    });
  });
});
