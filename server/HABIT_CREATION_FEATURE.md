# 习惯创建功能更新

## 功能描述
实现了创建新习惯时，如果没有传递 `baby_id` 参数，则自动使用登录用户的默认宝宝（第一个宝宝）的功能。

## 修改内容

### 1. DTO 层修改
- **文件**: `src/habits/dto/user-habit/create-user-habit.dto.ts`
- **修改**: 将 `baby_id` 字段从必填改为可选
  ```typescript
  // 修改前
  @IsUUID()
  @IsNotEmpty()
  baby_id: string;

  // 修改后
  @IsUUID()
  @IsOptional()
  baby_id?: string;
  ```

### 2. 服务层修改
- **文件**: `src/habits/services/user-habit.service.ts`
- **修改**: 在 `create` 方法中添加默认宝宝逻辑
  ```typescript
  // 如果没有提供baby_id，使用用户的默认宝宝（第一个宝宝）
  let babyId = createDto.baby_id;
  if (!babyId) {
    const defaultBaby = await this.babyRepository.findFirstBabyByOwner(userId);
    if (!defaultBaby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        '请先创建宝宝档案',
        HttpStatus.BAD_REQUEST
      );
    }
    babyId = defaultBaby.id;
  }
  ```

### 3. 测试更新
- **文件**: `src/habits/dto/__tests__/user-habit.dto.spec.ts`
- **新增**: 添加了测试用例验证 `baby_id` 为可选字段

## 业务逻辑
1. 如果用户在创建习惯时提供了 `baby_id`，则使用提供的值
2. 如果用户没有提供 `baby_id`，系统会自动查找用户的第一个宝宝（按创建时间排序）
3. 如果用户还没有创建任何宝宝，则返回错误提示"请先创建宝宝档案"
4. 后续的权限验证和业务逻辑保持不变

## API 使用示例

### 带 baby_id 的请求（原有方式）
```json
POST /habits/my-habits
{
  "baby_id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "睡前刷牙",
  "icon": "brush-teeth",
  "theme_color": "#FF6B6B",
  "description": "睡前刷干净牙齿",
  "frequency": [1, 2, 3, 4, 5, 6, 7],
  "preferred_time": "20:30",
  "reward_stars": 3
}
```

### 不带 baby_id 的请求（新功能）
```json
POST /habits/my-habits
{
  "name": "睡前刷牙",
  "icon": "brush-teeth",
  "theme_color": "#FF6B6B",
  "description": "睡前刷干净牙齿",
  "frequency": [1, 2, 3, 4, 5, 6, 7],
  "preferred_time": "20:30",
  "reward_stars": 3
}
```

## 错误处理
- 如果用户没有宝宝档案，返回 400 错误："请先创建宝宝档案"
- 其他验证错误保持原有逻辑不变

## 兼容性
- 完全向后兼容，原有的带 `baby_id` 的请求继续正常工作
- 新功能为可选增强，不影响现有功能