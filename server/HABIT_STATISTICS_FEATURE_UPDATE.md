# 习惯统计功能更新

## 功能描述
更新了习惯统计控制器，使所有接口的 `baby_id` 参数都变为可选，当没有提供 `baby_id` 时，自动使用登录用户的默认宝宝（第一个宝宝）。

## 修改内容

### 1. 控制器层修改
- **文件**: `src/habits/controllers/client/habit-statistics.controller.ts`
- **主要修改**:
  - 添加了 `BabyRepository` 依赖注入
  - 添加了 `resolveBabyId` 私有方法来处理默认宝宝逻辑
  - 更新了所有统计接口方法，移除了 `baby_id` 必填验证
  - 所有方法现在都使用 `resolveBabyId` 来获取有效的 `baby_id`

### 2. 新增私有方法
```typescript
private async resolveBabyId(userId: string, providedBabyId?: string): Promise<string> {
  if (providedBabyId) {
    return providedBabyId;
  }

  const defaultBaby = await this.babyRepository.findFirstBabyByOwner(userId);
  if (!defaultBaby) {
    throw new BusinessException(
      ErrorCode.BABY_NOT_FOUND,
      '请先创建宝宝档案',
      HttpStatus.BAD_REQUEST
    );
  }

  return defaultBaby.id;
}
```

### 3. 测试更新
- **文件**: `src/habits/controllers/client/__tests__/habit-statistics.controller.spec.ts`
- **修改内容**:
  - 添加了 `BabyRepository` 的 mock
  - 更新了测试用例，移除了 `baby_id` 必填的测试
  - 添加了默认宝宝功能的测试用例
  - 添加了用户没有宝宝时的错误处理测试

## 受影响的接口

### 1. 日统计接口
- **路径**: `GET /habits/statistics/daily`
- **变化**: `baby_id` 查询参数变为可选

### 2. 周统计接口
- **路径**: `GET /habits/statistics/weekly`
- **变化**: `baby_id` 查询参数变为可选

### 3. 月统计接口
- **路径**: `GET /habits/statistics/monthly`
- **变化**: `baby_id` 查询参数变为可选

### 4. 概览统计接口
- **路径**: `GET /habits/statistics/overview`
- **变化**: `baby_id` 查询参数变为可选

### 5. 习惯详情统计接口
- **路径**: `GET /habits/statistics/habit/:id`
- **变化**: `baby_id` 查询参数变为可选

### 6. 通用统计接口
- **路径**: `GET /habits/statistics`
- **变化**: `baby_id` 查询参数变为可选

## 业务逻辑
1. 如果用户在请求统计数据时提供了 `baby_id`，则使用提供的值
2. 如果用户没有提供 `baby_id`，系统会自动查找用户的第一个宝宝（按创建时间排序）
3. 如果用户还没有创建任何宝宝，则返回错误提示"请先创建宝宝档案"
4. 后续的统计计算逻辑保持不变

## API 使用示例

### 带 baby_id 的请求（原有方式）
```bash
GET /habits/statistics/daily?baby_id=123e4567-e89b-12d3-a456-426614174000&date=2024-01-01
```

### 不带 baby_id 的请求（新功能）
```bash
GET /habits/statistics/daily?date=2024-01-01
```

### 概览统计示例
```bash
# 原有方式
GET /habits/statistics/overview?baby_id=123e4567-e89b-12d3-a456-426614174000

# 新方式（使用默认宝宝）
GET /habits/statistics/overview
```

## 错误处理
- 如果用户没有宝宝档案，返回 400 错误："请先创建宝宝档案"
- 其他验证错误保持原有逻辑不变

## 兼容性
- 完全向后兼容，原有的带 `baby_id` 的请求继续正常工作
- 新功能为可选增强，不影响现有功能
- DTO 层的 `StatisticsQueryDto` 中 `baby_id` 本来就是可选的，无需修改

## 依赖关系
- 控制器现在依赖 `BabyRepository` 来获取用户的默认宝宝
- `BabyModule` 已经在 `HabitsModule` 中导入，无需额外配置

## 测试覆盖
- 所有原有测试用例继续通过
- 新增了默认宝宝功能的测试覆盖
- 新增了错误场景的测试覆盖
- 测试覆盖率保持在高水平