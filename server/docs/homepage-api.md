# 首页习惯统计 API

## 概述

首页习惯统计 API 提供用户今日习惯完成情况的概览数据，包括今日打卡率和时间轴信息。

## 端点

### GET /homepage/habits-overview

获取首页习惯统计概览，包含今日打卡率和时间轴数据。

#### 请求参数

**Query Parameters:**
- `baby_id` (string, required): 宝宝ID

#### 请求头

```
Authorization: Bearer <JWT_TOKEN>
```

#### 响应格式

```json
{
  "success": true,
  "data": {
    "today_completion_rate": 0.8,
    "today_completed_count": 4,
    "today_total_count": 5,
    "today_timeline": [
      {
        "habit_id": "uuid",
        "habit_name": "睡前刷牙",
        "habit_icon": "tooth",
        "theme_color": "#FF6B6B",
        "is_completed": true,
        "check_in_time": "2024-01-15T20:30:00.000Z",
        "preferred_time": "20:30"
      },
      {
        "habit_id": "uuid",
        "habit_name": "饭前洗手",
        "habit_icon": "hand",
        "theme_color": "#4ECDC4",
        "is_completed": false,
        "check_in_time": null,
        "preferred_time": "12:00"
      }
    ]
  }
}
```

#### 响应字段说明

- `today_completion_rate`: 今日完成率 (0-1)
- `today_completed_count`: 今日已完成习惯数量
- `today_total_count`: 今日总习惯数量
- `today_timeline`: 今日习惯时间轴数组
  - `habit_id`: 习惯ID
  - `habit_name`: 习惯名称
  - `habit_icon`: 习惯图标
  - `theme_color`: 主题颜色
  - `is_completed`: 是否已完成
  - `check_in_time`: 打卡时间（如果已完成）
  - `preferred_time`: 首选打卡时间

#### 错误响应

```json
{
  "success": false,
  "error": {
    "code": 40101,
    "message": "未授权访问",
    "timestamp": "2024-01-15T12:00:00.000Z"
  }
}
```

#### 使用示例

```bash
curl -X GET "http://localhost:3000/homepage/habits-overview?baby_id=uuid" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 注意事项

1. 需要有效的 JWT 认证令牌
2. 用户只能查看自己有权限访问的宝宝的习惯数据
3. 时间轴按首选时间排序，没有首选时间的习惯排在最后
4. 只显示今日应该执行的习惯（根据习惯的频率设置）