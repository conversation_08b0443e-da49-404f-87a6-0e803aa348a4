# 习惯分类层级结构迁移指南

## 概述

本指南描述了如何将现有的习惯分类系统迁移到支持层级结构的新系统。新系统支持两级分类：一级分类（父分类）和二级分类（子分类）。

## 迁移内容

### 数据库结构变更

1. **新增字段**：
   - `parent_id`: UUID类型，指向父分类的ID（一级分类为NULL）
   - `level`: INTEGER类型，表示分类层级（1=一级分类，2=二级分类）

2. **新增约束**：
   - `check_parent_level`: 确保层级结构一致性
   - `check_level_range`: 限制层级只能是1或2

3. **新增索引**：
   - `idx_habit_categories_parent`: 优化父子关系查询
   - `idx_habit_categories_level`: 优化层级查询
   - 更新 `idx_habit_categories_active`: 包含层级信息

### 数据迁移

1. **现有分类处理**：
   - 所有现有分类自动设置为一级分类（level=1, parent_id=NULL）

2. **新增默认分类结构**：
   ```
   生活自理 (一级)
   ├── 个人卫生 (二级)
   └── 生活技能 (二级)
   
   健康习惯 (一级)
   学习认知 (一级)
   社交与情绪 (一级)
   整理与责任 (一级)
   ```

3. **习惯模板**：
   - 为每个分类创建相应的习惯模板
   - 包含图标、主题色、描述等完整信息

## 迁移文件

### 1. 结构迁移文件
- **文件**: `migrations/012_add_hierarchy_to_habit_categories.sql`
- **作用**: 添加层级字段、约束和索引

### 2. 数据迁移文件
- **文件**: `migrations/013_insert_hierarchical_habit_data.sql`
- **作用**: 插入默认的层级分类和习惯模板数据

### 3. 迁移脚本
- **主脚本**: `src/scripts/run-hierarchy-migration.ts`
- **迁移逻辑**: `src/scripts/migrate-habit-categories.ts`
- **验证脚本**: `src/scripts/validate-hierarchy-migration.ts`

## 执行迁移

### 方法一：使用npm脚本（推荐）

```bash
# 执行完整迁移
yarn migrate:hierarchy

# 验证迁移结果
yarn migrate:hierarchy:validate

# 紧急回滚（谨慎使用）
yarn migrate:hierarchy:rollback
```

### 方法二：直接运行脚本

```bash
# 执行迁移
ts-node src/scripts/run-hierarchy-migration.ts migrate

# 验证结果
ts-node src/scripts/run-hierarchy-migration.ts validate

# 回滚迁移
ts-node src/scripts/run-hierarchy-migration.ts rollback
```

### 方法三：手动执行SQL文件

```bash
# 1. 执行结构迁移
psql -d your_database -f migrations/012_add_hierarchy_to_habit_categories.sql

# 2. 执行数据迁移
psql -d your_database -f migrations/013_insert_hierarchical_habit_data.sql
```

## 验证迁移

迁移完成后，系统会自动进行以下验证：

1. **表结构验证**：确认新字段和约束已正确添加
2. **层级结构验证**：检查分类层级关系的正确性
3. **数据完整性验证**：确保没有孤立的分类或模板
4. **约束验证**：确认所有约束条件正常工作
5. **索引验证**：确认新索引已正确创建

## 测试层级功能

```bash
# 运行层级功能测试
yarn test:hierarchy
```

测试内容包括：
- 获取分类树结构
- 获取一级分类列表
- 获取指定分类的子分类
- 创建和删除层级分类

## 回滚说明

⚠️ **警告**: 回滚操作会删除所有层级相关的数据和结构，请谨慎使用！

回滚操作会：
1. 删除层级相关的约束条件
2. 删除新增的索引
3. 删除 `parent_id` 和 `level` 字段
4. 恢复原始的索引结构

## 常见问题

### Q: 迁移失败怎么办？
A: 
1. 检查错误日志，确定失败原因
2. 如果是数据问题，手动修复后重新运行
3. 如果是结构问题，可能需要回滚后重新迁移

### Q: 现有的用户习惯会受影响吗？
A: 不会。迁移只影响习惯分类和模板，用户创建的习惯不受影响。

### Q: 如何验证迁移是否成功？
A: 运行验证脚本：`yarn migrate:hierarchy:validate`

### Q: 可以自定义默认分类吗？
A: 可以。修改 `migrations/013_insert_hierarchical_habit_data.sql` 文件中的数据。

## 注意事项

1. **备份数据**: 迁移前建议备份数据库
2. **测试环境**: 先在测试环境验证迁移流程
3. **停机时间**: 迁移过程中可能需要短暂停机
4. **监控日志**: 密切关注迁移过程中的日志输出
5. **验证结果**: 迁移完成后务必进行全面验证

## 相关文档

- [习惯模块设计文档](../specs/habits-module/design.md)
- [习惯模块需求文档](../specs/habits-module/requirements.md)
- [数据库迁移说明](../migrations/README.md)