# 习惯分类层级结构

## 概述

习惯分类现在支持两级层级结构：
- **一级分类**：主要分类，如"生活自理"、"健康习惯"等
- **二级分类**：子分类，如"个人卫生"、"生活技能"等

## 数据结构

### 数据库表结构

```sql
CREATE TABLE habit_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(255) NOT NULL,
  parent_id UUID REFERENCES habit_categories(id) ON DELETE CASCADE,
  level INTEGER NOT NULL DEFAULT 1 CHECK (level IN (1, 2)),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 确保层级结构的一致性
  CONSTRAINT check_parent_level CHECK (
    (level = 1 AND parent_id IS NULL) OR 
    (level = 2 AND parent_id IS NOT NULL)
  )
);
```

### 默认分类结构

```
生活自理 (一级)
├── 个人卫生 (二级)
│   ├── 睡前刷牙
│   └── 饭前洗手
└── 生活技能 (二级)
    ├── 独立吃饭
    ├── 自己穿鞋
    └── 上厕所

健康习惯 (一级)
├── 规律作息
└── 户外活动

学习认知 (一级)
├── 亲子阅读
├── 绘画
└── 拼图/积木

社交与情绪 (一级)
└── 分享玩具

整理与责任 (一级)
└── 整理玩具
```

## API 接口

### 管理员接口

#### 获取分类树形结构
```http
GET /admin/habit-categories/tree?is_active=true
```

#### 获取一级分类
```http
GET /admin/habit-categories/top-level?is_active=true
```

#### 获取子分类
```http
GET /admin/habit-categories/{parentId}/children
```

#### 创建分类
```http
POST /admin/habit-categories
Content-Type: application/json

{
  "name": "新分类",
  "icon": "🆕",
  "parent_id": "parent-uuid", // 可选，二级分类时必须
  "level": 1, // 1 或 2
  "sort_order": 1,
  "is_active": true
}
```

### 客户端接口

#### 获取习惯库（层级结构）
```http
GET /habits/library
```

#### 获取分类树
```http
GET /habits/library/categories/tree?is_active=true
```

#### 获取指定层级的分类
```http
GET /habits/library/categories?level=1&is_active=true
```

## 数据迁移

### 执行迁移

1. **运行数据库迁移脚本**：
   ```bash
   yarn migrate:habits
   ```

2. **验证迁移结果**：
   ```bash
   yarn test:hierarchy
   ```

### 迁移步骤

1. 添加 `parent_id` 和 `level` 字段
2. 添加数据库约束
3. 将现有分类设置为一级分类
4. 插入新的层级分类结构
5. 更新习惯模板关联
6. 验证数据完整性

### 回滚计划

如果需要回滚，可以：
1. 删除新添加的二级分类
2. 移除 `parent_id` 和 `level` 字段
3. 恢复原有的分类结构

## 业务规则

### 层级约束

1. **一级分类**：
   - `level = 1`
   - `parent_id = NULL`
   - 可以有子分类

2. **二级分类**：
   - `level = 2`
   - `parent_id` 必须指向一个一级分类
   - 不能有子分类

### 删除规则

1. **删除一级分类**：
   - 如果有子分类，必须先删除所有子分类
   - 如果有关联的习惯模板，不能删除

2. **删除二级分类**：
   - 如果有关联的习惯模板，不能删除

### 命名规则

- 同一层级下的分类名称不能重复
- 不同层级的分类名称可以相同

## 错误处理

### 新增错误代码

```typescript
export enum ErrorCode {
  // 习惯分类层级错误
  HABIT_CATEGORY_HAS_CHILDREN = 71107,   // 分类下存在子分类，无法删除
  HABIT_CATEGORY_LEVEL_INVALID = 71108,  // 分类层级无效
  HABIT_CATEGORY_PARENT_INVALID = 71109, // 父分类无效（不存在或层级错误）
}
```

### 常见错误场景

1. **创建二级分类时未指定父分类**
   ```json
   {
     "success": false,
     "error": {
       "code": 71108,
       "message": "二级分类必须指定父分类"
     }
   }
   ```

2. **尝试删除有子分类的一级分类**
   ```json
   {
     "success": false,
     "error": {
       "code": 71107,
       "message": "分类下存在子分类，无法删除"
     }
   }
   ```

## 前端集成

### 显示层级结构

```typescript
// 获取分类树
const categoryTree = await api.get('/habits/library/categories/tree');

// 渲染层级结构
function renderCategoryTree(categories: CategoryTree[]) {
  return categories.map(category => (
    <div key={category.id}>
      <h3>{category.name}</h3>
      {category.children?.map(child => (
        <div key={child.id} className="ml-4">
          <h4>{child.name}</h4>
          {/* 渲染该分类下的习惯模板 */}
        </div>
      ))}
    </div>
  ));
}
```

### 面包屑导航

```typescript
function Breadcrumb({ category }: { category: Category }) {
  const breadcrumbs = [];
  
  if (category.parent_id) {
    // 获取父分类信息
    const parent = await getParentCategory(category.parent_id);
    breadcrumbs.push(parent.name);
  }
  
  breadcrumbs.push(category.name);
  
  return (
    <nav>
      {breadcrumbs.map((name, index) => (
        <span key={index}>
          {index > 0 && ' > '}
          {name}
        </span>
      ))}
    </nav>
  );
}
```

## 测试

### 单元测试

```bash
# 运行习惯分类相关测试
yarn test src/habits/services/habit-category.service.spec.ts
yarn test src/habits/repositories/habit-category.repository.spec.ts
```

### 集成测试

```bash
# 运行层级功能测试
yarn test:hierarchy
```

### 手动测试场景

1. 创建一级分类
2. 在一级分类下创建二级分类
3. 尝试创建三级分类（应该失败）
4. 删除有子分类的一级分类（应该失败）
5. 删除二级分类
6. 删除一级分类
7. 获取分类树形结构
8. 按层级筛选分类

## 性能考虑

### 索引优化

```sql
-- 层级查询索引
CREATE INDEX idx_habit_categories_parent ON habit_categories(parent_id, level, sort_order);
CREATE INDEX idx_habit_categories_level ON habit_categories(level, is_active, sort_order);
```

### 缓存策略

- 分类树结构相对稳定，可以缓存较长时间
- 使用 Redis 缓存分类树，TTL 设置为 1 小时
- 分类更新时清除相关缓存

## 监控和日志

### 关键指标

- 分类创建/更新/删除操作数量
- 层级查询性能
- 缓存命中率

### 日志记录

- 分类层级结构变更
- 删除操作的依赖检查结果
- 数据迁移过程

## 未来扩展

### 可能的改进

1. **支持更多层级**：扩展到三级或四级分类
2. **分类排序优化**：支持拖拽排序
3. **分类图标管理**：支持自定义图标上传
4. **分类统计**：显示每个分类下的习惯数量和使用情况
5. **分类推荐**：基于用户行为推荐相关分类

### 兼容性考虑

- 保持向后兼容的 API 接口
- 渐进式迁移策略
- 数据回滚方案