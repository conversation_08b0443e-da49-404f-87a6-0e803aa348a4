# 习惯模块层级分类迁移指南

## 概述

习惯模块已经更新为支持两级层级分类结构。所有相关的SQL文件已经更新，包含了层级字段和默认数据。

## 层级结构

```
生活自理 (一级)
├── 个人卫生 (二级) - 睡前刷牙, 饭前洗手
└── 生活技能 (二级) - 独立吃饭, 自己穿鞋, 上厕所

健康习惯 (一级) - 规律作息, 户外活动
学习认知 (一级) - 亲子阅读, 绘画, 拼图/积木
社交与情绪 (一级) - 分享玩具
整理与责任 (一级) - 整理玩具
```

## 执行步骤

### 1. 检查当前状态

```bash
yarn setup:hierarchy
```

### 2. 执行基础迁移

根据检查结果，执行相应的SQL文件：

#### 在Supabase Dashboard中执行：
1. 打开Supabase Dashboard → SQL Editor
2. 依次执行以下文件内容：
   - `migrations/007_create_habit_categories_table.sql` (创建分类表)
   - `migrations/008_create_habit_templates_table.sql` (创建模板表)
   - `migrations/011_insert_default_habit_data.sql` (插入默认数据)

#### 或使用psql命令：
```bash
psql -d your_database -f migrations/007_create_habit_categories_table.sql
psql -d your_database -f migrations/008_create_habit_templates_table.sql
psql -d your_database -f migrations/011_insert_default_habit_data.sql
```

### 3. 验证迁移结果

```bash
yarn migrate:hierarchy:validate
```

## 数据库结构变更

### habit_categories表新增字段：
- `parent_id` (UUID): 父分类ID，NULL表示一级分类
- `level` (INTEGER): 分类层级，1=一级分类，2=二级分类

### 约束条件：
- `check_parent_level`: 确保层级结构一致性
- `check_level_range`: 限制层级只能是1或2

### 新增索引：
- `idx_habit_categories_parent`: 优化父子关系查询
- `idx_habit_categories_level`: 优化层级查询

## 可用命令

- `yarn setup:hierarchy` - 检查迁移准备状态
- `yarn migrate:hierarchy:validate` - 验证迁移结果
- `yarn test:hierarchy` - 测试层级功能

## 注意事项

1. **数据安全**: 迁移前建议备份数据库
2. **执行顺序**: 必须按照文件编号顺序执行SQL文件
3. **验证结果**: 执行完成后务必运行验证命令
4. **图标更新**: 新版本使用emoji图标，更加直观

## 故障排除

如果遇到问题：

1. **表不存在**: 确保按顺序执行了所有SQL文件
2. **约束错误**: 检查数据是否符合层级结构要求
3. **连接问题**: 确认Supabase配置正确

运行 `yarn setup:hierarchy` 获取详细的状态检查和指导。