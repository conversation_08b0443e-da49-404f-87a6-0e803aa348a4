# 习惯库树形结构API

## 接口概述

新的习惯库树形结构API提供了一个统一的接口来获取完整的习惯库数据，包括分类层级和对应的习惯模板。

## 接口详情

### GET /habits/library/tree

获取完整习惯库树形结构（分类+模板）

#### 请求参数

无需参数

#### 响应格式

```json
{
  "success": true,
  "data": [
    {
      "id": "category-uuid",
      "name": "生活自理",
      "icon": "life-icon-url",
      "level": 1,
      "sort_order": 1,
      "children": [
        {
          "id": "subcategory-uuid",
          "name": "基础自理",
          "icon": "basic-icon-url",
          "level": 2,
          "sort_order": 1,
          "templates": [
            {
              "id": "template-uuid",
              "name": "睡前刷牙",
              "icon": "brush-teeth-icon-url",
              "theme_color": "#4CAF50",
              "description": "睡前刷干净牙齿，让小牙齿一夜都在休息哦"
            },
            {
              "id": "template-uuid-2",
              "name": "饭前洗手",
              "icon": "wash-hands-icon-url",
              "theme_color": "#2196F3",
              "description": "小手洗干净，病菌不来烦"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 数据结构说明

- **一级分类** (`HabitCategoryTreeDto`)
  - `id`: 分类唯一标识
  - `name`: 分类名称
  - `icon`: 分类图标URL
  - `level`: 分类层级（固定为1）
  - `sort_order`: 排序顺序
  - `children`: 二级分类列表

- **二级分类** (`HabitSubCategoryTreeDto`)
  - `id`: 子分类唯一标识
  - `name`: 子分类名称
  - `icon`: 子分类图标URL
  - `level`: 分类层级（固定为2）
  - `sort_order`: 排序顺序
  - `templates`: 该分类下的习惯模板列表

- **习惯模板** (`HabitTemplateInfoDto`)
  - `id`: 模板唯一标识
  - `name`: 模板名称
  - `icon`: 模板图标URL
  - `theme_color`: 主题色（HEX格式）
  - `description`: 模板描述

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取习惯库树形数据
async function getHabitLibraryTree() {
  try {
    const response = await fetch('/habits/library/tree');
    const result = await response.json();
    
    if (result.success) {
      console.log('习惯库数据:', result.data);
      
      // 遍历一级分类
      result.data.forEach(category => {
        console.log(`一级分类: ${category.name}`);
        
        // 遍历二级分类
        category.children.forEach(subCategory => {
          console.log(`  二级分类: ${subCategory.name}`);
          
          // 遍历习惯模板
          subCategory.templates.forEach(template => {
            console.log(`    模板: ${template.name} - ${template.description}`);
          });
        });
      });
    }
  } catch (error) {
    console.error('获取习惯库数据失败:', error);
  }
}
```

### React 组件示例

```tsx
import React, { useEffect, useState } from 'react';

interface HabitLibraryTreeProps {}

const HabitLibraryTree: React.FC<HabitLibraryTreeProps> = () => {
  const [libraryData, setLibraryData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHabitLibraryTree();
  }, []);

  const fetchHabitLibraryTree = async () => {
    try {
      const response = await fetch('/habits/library/tree');
      const result = await response.json();
      
      if (result.success) {
        setLibraryData(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch habit library:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>加载中...</div>;
  }

  return (
    <div className="habit-library">
      {libraryData.map(category => (
        <div key={category.id} className="category">
          <h2>{category.name}</h2>
          {category.children.map(subCategory => (
            <div key={subCategory.id} className="subcategory">
              <h3>{subCategory.name}</h3>
              <div className="templates">
                {subCategory.templates.map(template => (
                  <div 
                    key={template.id} 
                    className="template"
                    style={{ borderColor: template.theme_color }}
                  >
                    <img src={template.icon} alt={template.name} />
                    <h4>{template.name}</h4>
                    <p>{template.description}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default HabitLibraryTree;
```

## 优势

1. **减少API调用**: 一次请求获取所有数据，减少网络开销
2. **数据一致性**: 确保分类和模板数据的一致性
3. **简化客户端逻辑**: 不需要多次请求来组装数据
4. **提高性能**: 减少网络延迟，提升用户体验
5. **易于缓存**: 完整的数据结构便于客户端缓存

## 注意事项

1. 只返回激活状态的分类和模板
2. 数据按照 `sort_order` 字段排序
3. 如果一级分类没有子分类但有模板，会创建虚拟的二级分类来包含这些模板
4. 响应数据可能较大，建议在客户端进行适当的缓存处理