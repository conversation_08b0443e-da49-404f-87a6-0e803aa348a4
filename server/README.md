# 小脚步应用 - 用户管理模块

基于 NestJS 和 Supabase 构建的用户管理系统，支持微信小程序认证、宝宝档案管理和家庭成员邀请功能。

## 项目结构

```
src/
├── common/                     # 通用模块
│   ├── enums/                 # 枚举定义
│   ├── interfaces/            # 接口定义
│   ├── exceptions/            # 自定义异常
│   ├── filters/               # 全局过滤器
│   ├── interceptors/          # 全局拦截器
│   └── decorators/            # 自定义装饰器
├── config/                    # 配置文件
├── database/                  # 数据库服务
├── user/                      # 用户模块
├── baby/                      # 宝宝模块
├── family/                    # 家庭模块
├── admin/                     # 管理员模块
├── auth/                      # 认证模块
├── upload/                    # 文件上传模块
└── habits/                    # 习惯模块
```

## 环境配置

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 配置环境变量：
- `SUPABASE_URL`: Supabase 项目 URL
- `SUPABASE_ANON_KEY`: Supabase 匿名密钥
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase 服务角色密钥
- `JWT_SECRET`: JWT 密钥
- `ADMIN_JWT_SECRET`: 管理员 JWT 密钥

## 安装依赖

```bash
yarn install
```

## 数据库迁移

运行数据库迁移脚本：
```bash
yarn migrate
```

## 运行应用

```bash
# 开发模式
yarn start:dev

# 生产模式
yarn start:prod
```

## 测试

```bash
# 单元测试
yarn test

# 端到端测试
yarn test:e2e

# 测试覆盖率
yarn test:cov
```

## 技术栈

- **后端框架**: NestJS (TypeScript)
- **数据库**: Supabase (PostgreSQL)
- **认证**: 微信小程序 + JWT
- **文件存储**: Supabase Storage
- **包管理**: yarn

## 功能模块

### 用户管理
- 微信小程序 openid 注册登录
- 个人资料管理（头像、手机号、用户名）
- JWT 认证和授权

### 宝宝档案
- 宝宝信息管理（小名、出生年月、性别、头像）
- 每用户最多 5 个宝宝档案
- 默认宝宝设置

### 家庭成员
- 邀请码生成和管理
- 家庭成员邀请和接受
- 权限管理

### 管理员系统
- 独立的管理员认证体系
- 基于角色的访问控制

### 文件上传
- 图片上传和存储
- 文件格式和大小验证

### 习惯模块
- 习惯分类和模板管理
- 用户习惯创建和跟踪
- 打卡记录和统计分析
- 首页习惯概览（今日打卡率和时间轴）

## API 文档

启动应用后，API 文档将在以下地址可用：
- 开发环境: http://localhost:3000/api

## 许可证

MIT