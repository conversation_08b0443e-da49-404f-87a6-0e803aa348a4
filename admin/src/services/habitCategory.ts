import request from '@/utils/request';
import type { HabitCategoryResponseDto } from '@/pages/habits/types/HabitCategoryResponseDto';

export interface CreateHabitCategoryDto {
  name: string;
  icon: string;
  parent_id?: string;
  sort_order?: number;
}

export interface UpdateHabitCategoryDto extends Partial<CreateHabitCategoryDto> {}

export interface HabitCategoryQueryParams {
  current?: number;
  pageSize?: number;
  name?: string;
  parent_id?: string;
  is_active?: boolean;
  level?: number;
}

// 获取分类列表（树形结构）
export async function getHabitCategories(params?: HabitCategoryQueryParams) {
  return request('/admin/habit-categories/tree', {
    method: 'GET',
    params,
  });
}

// 获取分类树形结构（用于TreeSelect组件）
export async function getHabitCategoryTree(params?: HabitCategoryQueryParams) {
  return request('/admin/habit-categories/tree', {
    method: 'GET',
    params,
  });
}

// 获取分类详情
export async function getHabitCategory(id: string) {
  return request(`/admin/habit-categories/${id}`, {
    method: 'GET',
  });
}

// 创建分类
export async function createHabitCategory(data: CreateHabitCategoryDto) {
  return request('/admin/habit-categories', {
    method: 'POST',
    data,
  });
}

// 更新分类
export async function updateHabitCategory(id: string, data: UpdateHabitCategoryDto) {
  return request(`/admin/habit-categories/${id}`, {
    method: 'PUT',
    data,
  });
}

// 删除分类
export async function deleteHabitCategory(id: string) {
  return request(`/admin/habit-categories/${id}`, {
    method: 'DELETE',
  });
}

// 批量删除分类
export async function batchDeleteHabitCategories(ids: string[]) {
  return request('/admin/habit-categories/batch', {
    method: 'DELETE',
    data: { ids },
  });
}

// 切换分类状态
export async function toggleHabitCategoryStatus(id: string, is_active: boolean) {
  return request(`/admin/habit-categories/${id}/status`, {
    method: 'PATCH',
    data: { is_active },
  });
}

// 更新分类排序
export async function updateHabitCategorySort(data: { id: string; sort_order: number }[]) {
  return request('/admin/habit-categories/sort', {
    method: 'PATCH',
    data: { categories: data },
  });
}