import request from '@/utils/request';

export interface CreateHabitTemplateDto {
  name: string;
  icon: string;
  category_id: string;
  theme_color: string;
  description: string;
}

export interface UpdateHabitTemplateDto extends Partial<CreateHabitTemplateDto> {}

export interface HabitTemplateQueryParams {
  current?: number;
  pageSize?: number;
  name?: string;
  category_id?: string;
  is_active?: boolean;
}

// 获取模版列表
export async function getHabitTemplates(params?: HabitTemplateQueryParams) {
  return request('/admin/habit-templates', {
    method: 'GET',
    params,
  });
}

// 获取模版详情
export async function getHabitTemplate(id: string) {
  return request(`/admin/habit-templates/${id}`, {
    method: 'GET',
  });
}

// 创建模版
export async function createHabitTemplate(data: CreateHabitTemplateDto) {
  return request('/admin/habit-templates', {
    method: 'POST',
    data,
  });
}

// 更新模版
export async function updateHabitTemplate(id: string, data: UpdateHabitTemplateDto) {
  return request(`/admin/habit-templates/${id}`, {
    method: 'PUT',
    data,
  });
}

// 删除模版
export async function deleteHabitTemplate(id: string) {
  return request(`/admin/habit-templates/${id}`, {
    method: 'DELETE',
  });
}

// 批量删除模版
export async function batchDeleteHabitTemplates(ids: string[]) {
  return request('/admin/habit-templates/batch', {
    method: 'DELETE',
    data: { ids },
  });
}

// 切换模版状态
export async function toggleHabitTemplateStatus(id: string, is_active: boolean) {
  return request(`/admin/habit-templates/${id}/status`, {
    method: 'PATCH',
    data: { is_active },
  });
}
