import request from '@/utils/request';
import type {
  MilestoneTemplate,
  CreateMilestoneTemplateDto,
  UpdateMilestoneTemplateDto,
  MilestoneQueryParams,
  MilestoneListResponse,
} from '@/types/milestone';

// 获取里程碑模板列表
export async function getMilestoneTemplates(params?: MilestoneQueryParams) {
  return request<MilestoneListResponse>('/admin/milestones/templates', {
    method: 'GET',
    params,
  }).then((res)=>{
    return {
      data: res.milestones,
      total: res.total
    }
  })
}

// 获取里程碑模板详情
export async function getMilestoneTemplate(id: string) {
  return request<{ data: MilestoneTemplate; success: boolean }>(`/admin/milestones/templates/${id}`, {
    method: 'GET',
  });
}

// 创建里程碑模板
export async function createMilestoneTemplate(data: CreateMilestoneTemplateDto) {
  return request<{ data: MilestoneTemplate; success: boolean }>('/admin/milestones/templates', {
    method: 'POST',
    data,
  });
}

// 更新里程碑模板
export async function updateMilestoneTemplate(id: string, data: Partial<CreateMilestoneTemplateDto>) {
  return request<{ data: MilestoneTemplate; success: boolean }>(`/admin/milestones/templates/${id}`, {
    method: 'PUT',
    data,
  });
}

// 删除里程碑模板
export async function deleteMilestoneTemplate(id: string) {
  return request<{ success: boolean }>(`/admin/milestones/templates/${id}`, {
    method: 'DELETE',
  });
}

// 批量删除里程碑模板
export async function batchDeleteMilestoneTemplates(ids: string[]) {
  return request<{ success: boolean }>('/admin/milestones/templates/batch', {
    method: 'DELETE',
    data: { ids },
  });
}

// 切换里程碑模板状态
export async function toggleMilestoneTemplateStatus(id: string, isActive: boolean) {
  return request<{ data: MilestoneTemplate; success: boolean }>(`/admin/milestones/templates/${id}/status`, {
    method: 'PATCH',
    data: { isActive },
  });
}