import axios from "axios"
import Cookies from "js-cookie"

const request = axios.create({
  timeout: 10 * 1000,
  baseURL: "/api",
  // headers: {
  //   "X-Requested-With": "XMLHttpRequest",
  //   "Content-Type": "application/x-www-form-urlencoded",
  // },
});

request.interceptors.request.use(
  (config) => {
    const token =  Cookies.get("token");

    console.log('token', config);
    return {
      ...config,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    };
  },
  (error) => {
    console.log('error', error);
    return Promise.reject(error);
  }
);

request.interceptors.response.use(
  (response) => {
    console.log('response113444', response);
    return response.data;
  },
  (error) => {
    console.log('error', error);
    return Promise.resolve({
      code: error.ERR_BAD_REQUEST,
    });
  }
);

export default request