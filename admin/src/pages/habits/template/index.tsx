import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import {
  Button,
  message,
  Popconfirm,
  Switch,
  Space,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import React, { useRef, useState } from 'react';
import type { HabitTemplateResponseDto } from '../types/HabitTemplateResponseDto';
import {
  getHabitTemplates,
  deleteHabitTemplate,
  batchDeleteHabitTemplates,
  toggleHabitTemplateStatus,
} from '@/services/habitTemplate';
import CreateTemplateForm from './components/CreateTemplateForm';
import UpdateTemplateForm from './components/UpdateTemplateForm';

const { Text } = Typography;

const HabitTemplate: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<HabitTemplateResponseDto[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  // 删除模版
  const { run: deleteRun, loading: deleteLoading } = useRequest<any, [string]>((id) => deleteHabitTemplate(id), {
    manual: true,
    onSuccess: () => {
      messageApi.success('删除成功');
      actionRef.current?.reload();
    },
    onError: (error) => {
      messageApi.error(error.message || '删除失败，请重试');
    },
  });

  // 批量删除模版
  const { run: batchDeleteRun, loading: batchDeleteLoading } = useRequest<any, [string[]]>((ids) => batchDeleteHabitTemplates(ids), {
    manual: true,
    onSuccess: () => {
      messageApi.success('批量删除成功');
      setSelectedRows([]);
      actionRef.current?.reload();
    },
    onError: (error) => {
      messageApi.error(error.message || '批量删除失败，请重试');
    },
  });

  // 切换状态
  const { run: toggleStatusRun } = useRequest<any, [string, boolean]>((id, is_active) => toggleHabitTemplateStatus(id, is_active), {
    manual: true,
    onSuccess: () => {
      messageApi.success('状态更新成功');
      actionRef.current?.reload();
    },
    onError: (error) => {
      messageApi.error(error.message || '状态更新失败，请重试');
    },
  });

  const columns: ProColumns<HabitTemplateResponseDto>[] = [
    {
      title: '类别',
      dataIndex: ['category', 'name'],
      width: 200,
    },
    {
      title: '模版名称',
      dataIndex: 'name',
      width: 200,
    },
    {
        title: '图标',
        dataIndex: 'icon',
        width: 80,
    },
    {
        title: '主题色',
        dataIndex: 'theme_color',
        width: 80,
        render: (_, record) => (
            <div style={{ width: '24px', height: '24px', backgroundColor: record.theme_color, borderRadius: '4px' }} />
        )
    },
    {
        title: '描述',
        dataIndex: 'description',
        width: 300,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
      render: (_, record) => (
        <Switch
          checked={record.is_active}
          onChange={(checked) => toggleStatusRun(record.id, checked)}
          size="small"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 160,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 160,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 180,
      render: (_, record) => [
        <UpdateTemplateForm
          key="edit"
          trigger={
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
            >
              编辑
            </Button>
          }
          values={record}
          onSuccess={() => actionRef.current?.reload()}
        />,
        <Popconfirm
          key="delete"
          title="确定要删除这个模版吗？"
          onConfirm={() => deleteRun(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            size="small"
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      {contextHolder}
      <ProTable<HabitTemplateResponseDto>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <CreateTemplateForm
            key="create"
            trigger={
              <Button type="primary" icon={<PlusOutlined />}>
                新建模版
              </Button>
            }
            onSuccess={() => actionRef.current?.reload()}
          />,
        ]}
        request={async (params) => {
          const response = await getHabitTemplates({
            current: params.current,
            pageSize: params.pageSize,
            name: params.name,
            is_active: params.is_active === 'true' ? true : params.is_active === 'false' ? false : undefined,
          });

          return {
            data: response.data || [],
            success: response.success,
            total: response.total || 0,
          };
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        options={{
          reload: true,
          density: true,
          fullScreen: true,
        }}
      />

      {selectedRowsState?.length > 0 && (
        <div
          style={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            background: '#fff',
            borderTop: '1px solid #f0f0f0',
            padding: '16px 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            zIndex: 1000,
          }}
        >
          <div>
            已选择 <Text strong>{selectedRowsState.length}</Text> 项
          </div>
          <Space>
            <Button onClick={() => setSelectedRows([])}>
              取消选择
            </Button>
            <Popconfirm
              title={`确定要删除选中的 ${selectedRowsState.length} 个模版吗？`}
              description="删除后不可恢复，请谨慎操作"
              onConfirm={() => batchDeleteRun(selectedRowsState.map(row => row.id))}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                loading={batchDeleteLoading || deleteLoading}
                icon={<DeleteOutlined />}
              >
                批量删除
              </Button>
            </Popconfirm>
          </Space>
        </div>
      )}
    </PageContainer>
  );
};

export default HabitTemplate;
