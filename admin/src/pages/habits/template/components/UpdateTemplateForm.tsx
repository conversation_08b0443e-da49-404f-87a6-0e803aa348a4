import React, { useState, useEffect } from 'react';
import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message, Button, Space, Popover, ColorPicker, TreeSelect, Form } from 'antd';
import { useRequest } from '@umijs/max';
import { updateHabitTemplate } from '@/services/habitTemplate';
import { getHabitCategoryTree } from '@/services/habitCategory';
import type { UpdateHabitTemplateDto } from '@/services/habitTemplate';
import type { HabitTemplateResponseDto } from '@/pages/habits/types/HabitTemplateResponseDto';

// 常用图标选项
const ICON_OPTIONS = [
  '🏃', '💪', '📚', '🎯', '⏰', '🧘', '🍎', '💧', '🛌', '🚿',
  '🦷', '🧴', '👕', '🧹', '📱', '💻', '🎵', '🎨', '✍️', '📝',
  '🌱', '🌟', '⭐', '🔥', '💎', '🏆', '🎉', '❤️', '💚', '💙',
];

interface UpdateTemplateFormProps {
  trigger: React.ReactElement;
  values: HabitTemplateResponseDto;
  onSuccess?: () => void;
}

const UpdateTemplateForm: React.FC<UpdateTemplateFormProps> = ({
  trigger,
  values,
  onSuccess,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string>(values.icon);
  const [themeColor, setThemeColor] = useState<string>(values.theme_color);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    setSelectedIcon(values.icon);
    setThemeColor(values.theme_color);
  }, [values.icon, values.theme_color]);

  // 获取分类树形结构用于分类选择
  const { data: categoriesData, loading: categoriesLoading } = useRequest(
    () => getHabitCategoryTree({ is_active: true }),
    {
      formatResult: (res) => res.data || [],
    }
  );

  // 更新模版
  const { run: updateRun, loading: updateLoading } = useRequest(updateHabitTemplate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('更新成功');
      onSuccess?.();
      return true;
    },
    onError: (error) => {
      messageApi.error(error.message || '更新失败，请重试');
    },
  });

  // 构建树形分类选项
  const buildTreeData = (categories: any[]): any[] => {
    return categories.map(cat => ({
      title: `${cat.icon} ${cat.name}`,
      value: cat.id,
      key: cat.id,
      children: cat.children && cat.children.length > 0 ? buildTreeData(cat.children) : undefined,
    }));
  };

  const treeData = categoriesData ? buildTreeData(categoriesData) : [];

  // 图标选择器
  const IconSelector = () => (
    <Popover
      content={
        <div style={{ width: 240 }}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(8, 1fr)', gap: 8 }}>
            {ICON_OPTIONS.map((icon) => (
              <Button
                key={icon}
                size="small"
                type={selectedIcon === icon ? 'primary' : 'default'}
                onClick={() => setSelectedIcon(icon)}
                style={{ 
                  width: 24, 
                  height: 24, 
                  padding: 0,
                  fontSize: '14px',
                }}
              >
                {icon}
              </Button>
            ))}
          </div>
        </div>
      }
      title="选择图标"
      trigger="click"
    >
      <Button style={{ width: '100%', textAlign: 'left' }}>
        <Space>
          <span style={{ fontSize: '16px' }}>{selectedIcon}</span>
          <span>点击选择图标</span>
        </Space>
      </Button>
    </Popover>
  );

  return (
    <>
      {contextHolder}
      <ModalForm<UpdateHabitTemplateDto>
        title="编辑模版"
        trigger={trigger}
        width={500}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        modalProps={{
          destroyOnClose: true,
        }}
        initialValues={{
          name: values.name,
          category_id: values.category_id,
          description: values.description,
        }}
        onFinish={async (formValues) => {
          await updateRun(values.id, {
            ...formValues,
            icon: selectedIcon,
            theme_color: themeColor,
          });
          return true;
        }}
        loading={updateLoading}
      >
        <ProFormText
          name="name"
          label="模版名称"
          placeholder="请输入模版名称"
          rules={[
            { required: true, message: '请输入模版名称' },
            { max: 50, message: '模版名称不能超过50个字符' },
          ]}
        />

        <Form.Item
          name="category_id"
          label="所属分类"
          rules={[{ required: true, message: '请选择所属分类' }]}
        >
          <TreeSelect
            style={{ width: '100%' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            treeData={treeData}
            placeholder="请选择所属分类"
            treeDefaultExpandAll
            loading={categoriesLoading}
            showSearch
            treeNodeFilterProp="title"
            allowClear
          />
        </Form.Item>
        
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: '#ff4d4f' }}>*</span>
            <span style={{ marginLeft: 4 }}>模版图标</span>
          </div>
          <IconSelector />
        </div>

        <div style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 8 }}>
                <span style={{ color: '#ff4d4f' }}>*</span>
                <span style={{ marginLeft: 4 }}>主题色</span>
            </div>
            <ColorPicker value={themeColor} onChange={(c) => setThemeColor(c.toHexString())} />
        </div>

        <ProFormTextArea
          name="description"
          label="模版描述"
          placeholder="请输入模版描述"
          rules={[
            { max: 200, message: '模版描述不能超过200个字符' },
          ]}
        />
      </ModalForm>
    </>
  );
};

export default UpdateTemplateForm;
