import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  FooterToolbar,
  PageContainer,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Popconfirm, Switch, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import {
  getMilestoneTemplates,
  deleteMilestoneTemplate,
  batchDeleteMilestoneTemplates,
  toggleMilestoneTemplateStatus,
} from '@/services/milestone';
import type { MilestoneTemplate } from '@/types/milestone';
import { AchievementCategory, MilestoneImportance, CATEGORY_OPTIONS, IMPORTANCE_OPTIONS } from '@/types/milestone';
import CreateMilestoneForm from './components/CreateMilestoneForm';
import UpdateMilestoneForm from './components/UpdateMilestoneForm';

const MilestoneList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<MilestoneTemplate[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  // 删除里程碑
  const { run: deleteRun, loading: deleteLoading } = useRequest(deleteMilestoneTemplate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('删除成功');
      actionRef.current?.reload();
    },
    onError: () => {
      messageApi.error('删除失败，请重试');
    },
  });

  // 批量删除里程碑
  const { run: batchDeleteRun, loading: batchDeleteLoading } = useRequest(batchDeleteMilestoneTemplates, {
    manual: true,
    onSuccess: () => {
      messageApi.success('批量删除成功');
      setSelectedRows([]);
      actionRef.current?.reload();
    },
    onError: () => {
      messageApi.error('批量删除失败，请重试');
    },
  });

  // 切换状态
  const { run: toggleStatusRun } = useRequest(toggleMilestoneTemplateStatus, {
    manual: true,
    onSuccess: () => {
      messageApi.success('状态更新成功');
      actionRef.current?.reload();
    },
    onError: () => {
      messageApi.error('状态更新失败，请重试');
    },
  });

  // 获取分类标签颜色
  const getCategoryColor = (category: AchievementCategory) => {
    const colorMap = {
      [AchievementCategory.PHYSICAL]: 'blue',
      [AchievementCategory.LANGUAGE]: 'green',
      [AchievementCategory.COGNITIVE]: 'orange',
      [AchievementCategory.SOCIAL]: 'purple',
    };
    return colorMap[category];
  };

  // 获取重要程度标签颜色
  const getImportanceColor = (importance: MilestoneImportance) => {
    const colorMap = {
      [MilestoneImportance.LOW]: 'default',
      [MilestoneImportance.MEDIUM]: 'processing',
      [MilestoneImportance.HIGH]: 'warning',
      [MilestoneImportance.CRITICAL]: 'error',
    };
    return colorMap[importance];
  };

  const columns: ProColumns<MilestoneTemplate>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '里程碑名称',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 120,
      valueType: 'select',
      valueEnum: CATEGORY_OPTIONS.reduce((acc, item) => {
        acc[item.value] = { text: item.label };
        return acc;
      }, {} as Record<string, { text: string }>),
      render: (_, record) => {
        const categoryOption = CATEGORY_OPTIONS.find(item => item.value === record.category);
        return (
          <Tag color={getCategoryColor(record.category)}>
            {categoryOption?.label}
          </Tag>
        );
      },
    },
    {
      title: '适用年龄',
      dataIndex: 'ageRange',
      width: 120,
      hideInSearch: true,
      render: (_, record) => `${record.typicalAgeMin}-${record.maxAgeMonths}月`,
    },
    {
      title: '最小年龄(月)',
      dataIndex: 'minAgeMonths',
      width: 120,
      hideInTable: true,
      valueType: 'digit',
    },
    {
      title: '最大年龄(月)',
      dataIndex: 'maxAgeMonths',
      width: 120,
      hideInTable: true,
      valueType: 'digit',
    },
    {
      title: '重要程度',
      dataIndex: 'importance',
      width: 100,
      valueType: 'select',
      valueEnum: IMPORTANCE_OPTIONS.reduce((acc, item) => {
        acc[item.value] = { text: item.label };
        return acc;
      }, {} as Record<string, { text: string }>),
      render: (_, record) => {
        const importanceOption = IMPORTANCE_OPTIONS.find(item => item.value === record.importance);
        return (
          <Tag color={getImportanceColor(record.importance)}>
            {importanceOption?.label}
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
      render: (_, record) => (
        <Switch
          checked={record.isActive}
          onChange={(checked) => toggleStatusRun(record.id, checked)}
          size="small"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <UpdateMilestoneForm
          key="edit"
          trigger={<a>编辑</a>}
          values={record}
          onSuccess={() => actionRef.current?.reload()}
        />,
        <Popconfirm
          key="delete"
          title="确定要删除这个里程碑吗？"
          onConfirm={() => deleteRun(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <a style={{ color: 'red' }}>删除</a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      {contextHolder}
      <ProTable<MilestoneTemplate>
        headerTitle="里程碑模板管理"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <CreateMilestoneForm
            key="create"
            trigger={<Button type="primary">新建里程碑</Button>}
            onSuccess={() => actionRef.current?.reload()}
          />,
        ]}
        request={async (params) => {
          const response = await getMilestoneTemplates({
            current: params.current,
            pageSize: params.pageSize,
            name: params.name,
            category: params.category as AchievementCategory,
            minAgeMonths: params.minAgeMonths,
            maxAgeMonths: params.maxAgeMonths,
            importance: params.importance as MilestoneImportance,
            isActive: params.isActive === 'true' ? true : params.isActive === 'false' ? false : undefined,
          });
          
          return {
            data: response.data || [],
            success: response.success,
            total: response.total || 0,
          };
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择 <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a> 项
            </div>
          }
        >
          <Popconfirm
            title={`确定要删除选中的 ${selectedRowsState.length} 个里程碑吗？`}
            onConfirm={() => batchDeleteRun(selectedRowsState.map(row => row.id))}
            okText="确定"
            cancelText="取消"
          >
            <Button loading={batchDeleteLoading || deleteLoading}>
              批量删除
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}
    </PageContainer>
  );
};

export default MilestoneList;