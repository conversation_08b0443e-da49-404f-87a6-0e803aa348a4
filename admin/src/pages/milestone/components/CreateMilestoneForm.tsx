import { ModalForm, ProFormDigit, ProFormSelect, ProFormSwitch, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React from 'react';
import { createMilestoneTemplate } from '@/services/milestone';
import type { CreateMilestoneTemplateDto } from '@/types/milestone';
import { CATEGORY_OPTIONS, IMPORTANCE_OPTIONS } from '@/types/milestone';

interface CreateMilestoneFormProps {
  trigger: React.ReactElement;
  onSuccess?: () => void;
}

const CreateMilestoneForm: React.FC<CreateMilestoneFormProps> = ({ trigger, onSuccess }) => {
  const [messageApi, contextHolder] = message.useMessage();

  const { run: createRun, loading } = useRequest(createMilestoneTemplate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('创建成功');
      onSuccess?.();
    },
    onError: () => {
      messageApi.error('创建失败，请重试');
    },
  });

  const handleSubmit = async (values: CreateMilestoneTemplateDto) => {
    await createRun(values);
    return true;
  };

  return (
    <>
      {contextHolder}
      <ModalForm<CreateMilestoneTemplateDto>
        title="新建里程碑模板"
        trigger={trigger}
        modalProps={{
          destroyOnClose: true,
        }}
        width={600}
        onFinish={handleSubmit}
        loading={loading}
        initialValues={{
          isActive: true,
          importance: 2,
        }}
      >
        <ProFormText
          name="name"
          label="里程碑名称"
          placeholder="请输入里程碑名称"
          rules={[
            { required: true, message: '请输入里程碑名称' },
            { max: 100, message: '里程碑名称不能超过100个字符' },
          ]}
        />
        
        <ProFormTextArea
          name="description"
          label="描述"
          placeholder="请输入里程碑描述"
          rules={[
            { required: true, message: '请输入里程碑描述' },
            { max: 500, message: '描述不能超过500个字符' },
          ]}
          fieldProps={{
            rows: 4,
          }}
        />
        
        <ProFormSelect
          name="category"
          label="分类"
          placeholder="请选择分类"
          options={CATEGORY_OPTIONS}
          rules={[{ required: true, message: '请选择分类' }]}
        />
        
        <div style={{ display: 'flex', gap: 16 }}>
          <ProFormDigit
            name="minAgeMonths"
            label="最小年龄(月)"
            placeholder="请输入最小年龄"
            min={0}
            max={72}
            rules={[{ required: true, message: '请输入最小年龄' }]}
            fieldProps={{
              style: { width: '100%' },
            }}
          />
          
          <ProFormDigit
            name="maxAgeMonths"
            label="最大年龄(月)"
            placeholder="请输入最大年龄"
            min={0}
            max={72}
            rules={[
              { required: true, message: '请输入最大年龄' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const minAge = getFieldValue('minAgeMonths');
                  if (!value || !minAge || value >= minAge) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('最大年龄必须大于等于最小年龄'));
                },
              }),
            ]}
            fieldProps={{
              style: { width: '100%' },
            }}
          />
        </div>
        
        <ProFormSelect
          name="importance"
          label="重要程度"
          placeholder="请选择重要程度"
          options={IMPORTANCE_OPTIONS}
          rules={[{ required: true, message: '请选择重要程度' }]}
        />
        
        <ProFormSwitch
          name="isActive"
          label="启用状态"
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      </ModalForm>
    </>
  );
};

export default CreateMilestoneForm;