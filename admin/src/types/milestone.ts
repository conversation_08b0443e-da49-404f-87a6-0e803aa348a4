// 里程碑分类枚举
export enum AchievementCategory {
  PHYSICAL = 'PHYSICAL', // 身体发育类
  LANGUAGE = 'LANGUAGE', // 语言发展类
  COGNITIVE = 'COGNITIVE', // 认知能力类
  SOCIAL = 'SOCIAL', // 社交情感类
}

// 里程碑重要程度
export enum MilestoneImportance {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4,
}

// 里程碑模板接口
export interface MilestoneTemplate {
  id: string;
  name: string;
  description: string;
  category: AchievementCategory;
  minAgeMonths: number;
  maxAgeMonths: number;
  importance: MilestoneImportance;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 创建里程碑模板DTO
export interface CreateMilestoneTemplateDto {
  name: string;
  description: string;
  category: AchievementCategory;
  minAgeMonths: number;
  maxAgeMonths: number;
  importance: MilestoneImportance;
  isActive?: boolean;
}

// 更新里程碑模板DTO
export interface UpdateMilestoneTemplateDto extends Partial<CreateMilestoneTemplateDto> {
  id: string;
}

// 里程碑查询参数
export interface MilestoneQueryParams {
  current?: number;
  pageSize?: number;
  name?: string;
  category?: AchievementCategory;
  minAgeMonths?: number;
  maxAgeMonths?: number;
  importance?: MilestoneImportance;
  isActive?: boolean;
}

// 里程碑列表响应
export interface MilestoneListResponse {
  data: MilestoneTemplate[];
  total: number;
  success: boolean;
}

// 分类选项
export const CATEGORY_OPTIONS = [
  { label: '身体发育类', value: AchievementCategory.PHYSICAL },
  { label: '语言发展类', value: AchievementCategory.LANGUAGE },
  { label: '认知能力类', value: AchievementCategory.COGNITIVE },
  { label: '社交情感类', value: AchievementCategory.SOCIAL },
];

// 重要程度选项
export const IMPORTANCE_OPTIONS = [
  { label: '低', value: MilestoneImportance.LOW },
  { label: '中', value: MilestoneImportance.MEDIUM },
  { label: '高', value: MilestoneImportance.HIGH },
  { label: '关键', value: MilestoneImportance.CRITICAL },
];