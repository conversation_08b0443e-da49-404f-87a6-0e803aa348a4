// 习惯分类枚举
export enum HabitCategory {
  DAILY_ROUTINE = 'DAILY_ROUTINE', // 日常作息
  HYGIENE = 'HYGIENE', // 卫生习惯
  LEARNING = 'LEARNING', // 学习习惯
  SOCIAL = 'SOCIAL', // 社交习惯
  EXERCISE = 'EXERCISE', // 运动习惯
  EATING = 'EATING', // 饮食习惯
}

// 习惯难度等级
export enum HabitDifficulty {
  EASY = 1, // 简单
  MEDIUM = 2, // 中等
  HARD = 3, // 困难
}

// 习惯频率类型
export enum HabitFrequency {
  DAILY = 'DAILY', // 每日
  WEEKLY = 'WEEKLY', // 每周
  MONTHLY = 'MONTHLY', // 每月
}

// 习惯模板接口
export interface HabitTemplate {
  id: string;
  name: string;
  description: string;
  category: HabitCategory;
  difficulty: HabitDifficulty;
  frequency: HabitFrequency;
  minAgeMonths: number;
  maxAgeMonths: number;
  icon: string; // 图标名称或URL
  color: string; // 习惯颜色
  isBuiltIn: boolean; // 是否为内置习惯
  isActive: boolean;
  rewardPoints: number; // 完成奖励积分
  createdAt: string;
  updatedAt: string;
}

// 创建习惯模板DTO
export interface CreateHabitTemplateDto {
  name: string;
  description: string;
  category: HabitCategory;
  difficulty: HabitDifficulty;
  frequency: HabitFrequency;
  minAgeMonths: number;
  maxAgeMonths: number;
  icon: string;
  color: string;
  isBuiltIn?: boolean;
  isActive?: boolean;
  rewardPoints: number;
}

// 更新习惯模板DTO
export interface UpdateHabitTemplateDto extends Partial<CreateHabitTemplateDto> {
  id: string;
}

// 习惯查询参数
export interface HabitQueryParams {
  current?: number;
  pageSize?: number;
  name?: string;
  category?: HabitCategory;
  difficulty?: HabitDifficulty;
  frequency?: HabitFrequency;
  minAgeMonths?: number;
  maxAgeMonths?: number;
  isBuiltIn?: boolean;
  isActive?: boolean;
}

// 习惯列表响应
export interface HabitListResponse {
  data: HabitTemplate[];
  total: number;
  success: boolean;
}

// 分类选项
export const HABIT_CATEGORY_OPTIONS = [
  { label: '日常作息', value: HabitCategory.DAILY_ROUTINE },
  { label: '卫生习惯', value: HabitCategory.HYGIENE },
  { label: '学习习惯', value: HabitCategory.LEARNING },
  { label: '社交习惯', value: HabitCategory.SOCIAL },
  { label: '运动习惯', value: HabitCategory.EXERCISE },
  { label: '饮食习惯', value: HabitCategory.EATING },
];

// 难度选项
export const HABIT_DIFFICULTY_OPTIONS = [
  { label: '简单', value: HabitDifficulty.EASY },
  { label: '中等', value: HabitDifficulty.MEDIUM },
  { label: '困难', value: HabitDifficulty.HARD },
];

// 频率选项
export const HABIT_FREQUENCY_OPTIONS = [
  { label: '每日', value: HabitFrequency.DAILY },
  { label: '每周', value: HabitFrequency.WEEKLY },
  { label: '每月', value: HabitFrequency.MONTHLY },
];

// 预设颜色选项
export const HABIT_COLOR_OPTIONS = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d',
  '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16',
  '#a0d911', '#2f54eb', '#fa541c', '#096dd9',
];

// 预设图标选项
export const HABIT_ICON_OPTIONS = [
  'smile', 'heart', 'star', 'trophy', 'gift',
  'book', 'apple', 'coffee', 'home', 'car',
  'phone', 'camera', 'music', 'game', 'tool',
];